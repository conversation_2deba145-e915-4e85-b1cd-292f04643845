{"general": {"accessibility": {"skip_to_content": "Skip to content", "close_modal": "Close (esc)"}, "meta": {"tags": "Tagged \"{{ tags }}\"", "page": "Page {{ page }}"}, "404": {"title": "Whoops! Looks like you’re lost.", "subtext": "Looks like this page is unavailable. Don’t worry, we’ll find you something just as good.", "continue_shopping": "Take me home", "shop_link_text": "Shop our bestsellers"}, "pagination": {"previous": "Previous", "next": "Next", "current_page": "Page {{ current }} of {{ total }}"}, "password_page": {"opening_soon": "Opening Soon", "login_form_heading": "Enter store using password", "login_form_password_label": "Password", "login_form_password_placeholder": "Your password", "login_form_submit": "Enter", "signup_form_email_label": "Email", "signup_form_success": "We will send you an email right before we open!", "admin_link_html": "Are you the store owner? <a href=\"/admin\" class=\"text-link\">Log in here</a>", "password_link": "Enter using password", "powered_by_shopify_html": "This shop will be powered by {{ shopify }}"}, "social": {"follow_us": "Follow us", "share": "Share this article", "share_on_facebook": "Share", "share_on_twitter": "Tweet", "share_on_pinterest": "Pin it", "alt_text": {"share_on_facebook": "Share on Facebook", "share_on_twitter": "Tweet on Twitter", "share_on_pinterest": "Pin on Pinterest"}, "copied": "<PERSON>pied"}, "search": {"title": "Search Results for", "title_not_found": "No results for  ", "results_for": "Search results for ‘{{ terms }}’", "not_found_text": "Sorry, there are no results for “{{ terms }}”.", "not_found": "No results found", "products": {"title": "Results", "view_all": "See all results for “{{ term }}”", "not_found": "No results found. Please try searching with a different term."}, "articles": {"title": "Posts", "view_all": "View All", "not_found": "No posts found"}, "pages": {"title": "Pages"}, "collections": {"title": "Collections"}, "not_found_query": "Sorry, we couldn’t find any matching results for this search. Try a different search or start shopping below."}, "newsletter_form": {"newsletter_title": "Stay in Touch", "newsletter_text": "Join our newsletter and stay in the know about new collections, outfit inspiration, sales, and more.", "email_placeholder": "Enter your email", "confirmation": "Thanks for subscribing", "subscribed": "You already subscribed!", "tag_validation_error": "Please select below option!", "submit": "Submit"}, "filters": {"show_more": "Show More", "show_less": "Show Less"}, "stocks": {"instock_text": "In stock", "outstock_text": "Out of stock", "notify_outstock_text": "Notify me when in stock"}, "add": "Add", "to": "to", "show_now": "Shop Now"}, "blogs": {"article": {"view_all": "View all", "all_topics": "All topics", "by_author": "by {{ author }}", "posted_in": "Posted in", "read_more": "Read more", "back_to_blog": "Back to {{ title }}"}, "comments": {"title": "Leave a comment", "name": "Name", "email": "Email", "message": "Message", "post": "Post comment", "moderated": "Please note, comments must be approved before they are published", "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.", "success": "Your comment was posted successfully! Thank you!", "comments_with_count": {"one": "{{ count }} comment", "other": "{{ count }} comments"}}}, "cart": {"general": {"title": "My Cart", "note": "Leave your message here", "remove": "Remove", "subtotal": "Subtotal", "savings": "Save [money]", "shipping_at_checkout": "Shipping & taxes calculated at checkout", "update": "Update", "checkout": "Checkout", "empty": "Your Cart is Empty", "cookies_required": "Enable cookies to use the shopping cart", "edit": "Edit", "cancel": "Cancel", "continue_shopping": "Continue Shopping", "fabric_sample": "Fabric sample", "max_sample": "Maximum 8 samples", "note_label": "Is this a gift?", "note_label_added": "Gift message added!", "note_placeholder": "Include a gift message (Optional)", "page_title": "Your Cart", "cross_sell_add": "Add", "qty_running_out": "[qty] stocks left", "tnc_alert": "You must agree to the Terms and Conditions", "view_cart": "View Cart", "subtotal_with_count": {"one": "Subtotal ({{ count }} item)", "other": "Subtotal ({{ count }} items)"}, "saved": "{{ discount }} saved", "view_selections": "View Selections", "customisable": "Customisable", "discount_calculate_at_checkout": "Shipping and discounts calculated at checkout", "order_now": "Order Now", "colour": "Colour", "secure_checkout": "Secure Checkout", "you_saved": "You saved {{ disc }}", "order_note": "I would like to add a gift note.", "add": "Add", "save_note": "Save Note", "calculated_at_co": "Calculated at checkout"}, "label": {"product": "Product", "price": "Price", "quantity": "Quantity", "total": "Total", "tnc_agreement_html": "I have agreed to the <a href=\"{{ url }}\" class=\"text-link\">Terms & Conditions</a>", "shipping": "Shipping", "free_shipping": "Free Shipping"}}, "collections": {"general": {"brands": "Brands", "show_more": "Show More", "load_more": "Load More", "load_more_with_count": "Load {{ count }} More", "showing_text": "You've viewed ${productsDisplayedCount} of ${totalProducts} products", "showing_text_html": "You've viewed <span class=\"displayed-count\">${productsDisplayedCount}</span> of <span class=\"total-products\">${totalProducts}</span> products", "filter_and_sort": "Filter & Sort", "items_count": "${totalProducts} Products", "see_more": "See more", "view_products_with_count": "View ${totalProducts} Products"}, "sorting": {"title": "Sort by", "sort": "Sort", "featured": "Featured", "relevance": "Relevance", "best_selling": "Best Selling", "az": "Alphabetically: A-Z", "za": "Alphabetically: Z-A", "price_ascending": "Price: Low to High", "price_descending": "Price: High to Low", "date_descending": "Newest", "date_ascending": "Oldest"}, "filters": {"title": "Filter by", "filter": "Filter", "in_stock": "In Stock", "out_of_stock": "Out of Stock", "apply": "Apply Filter", "clear_group": "Clear", "clear_all": "Clear All", "clear_filters": "Clear Filters", "view_items": "View Items", "all": "All Filters", "show_results": "Show Results", "view_results": "View Results", "no_result": "No matching products"}}, "contact": {"form": {"name": "Name", "email": "Email", "phone": "Phone Number", "message": "Message", "error_name": "Name is required", "error_email": "Email is required", "error_phone": "Mobile Number is required", "error_mobilenumber": "Mobile Number is required", "error_companyemail": "Company Email is required", "error_message": "Message is required", "submit": "Send", "post_success_title": "Thank you for contacting us!", "post_success_text": "We'll get back to you as soon as possible."}}, "customer": {"account": {"title": "My Account", "order_history": "Orders", "address_book": "Addresses", "logout": "Log Out", "details": "Account Details", "view_addresses": "View Addresses", "welcome_title": "Welcome Back", "return": "Return to Account Details", "not_found": "No addresses found"}, "activate_account": {"title": "Activate Account", "subtext": "Create your password to activate your account.", "password": "Password", "password_confirm": "Confirm Password", "submit": "Activate Account", "cancel": "Decline Invitation", "error_password": "Please enter password.", "error_password_confirm": "Please enter password confirmation."}, "addresses": {"title": "Your Addresses", "default": "<PERSON><PERSON><PERSON>", "add_new": "Add New Address", "edit_address": "Edit Address", "first_name": "First Name*", "last_name": "Last Name*", "company": "Company", "address1": "Address Line 1*", "address2": "Address Line 2", "city": "City*", "country": "Country", "province": "Province", "zip": "Postal Code*", "phone": "Phone", "first_name_error": "First name is required.", "last_name_error": "Last name is required.", "address1_error": "Address is required.", "city_error": "City is required.", "zip_error": "Postal code is required.", "set_default": "Set as default address", "add": "Add Address", "update": "Update Address", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "delete_confirm": "Are you sure you wish to delete this address?", "customer": "Customer", "email": "Email", "address": "Address", "default_address": "<PERSON><PERSON><PERSON>", "submit": "Save Address", "back": "Back to Addresses"}, "login": {"title": "Log In", "email": "Email Address", "password": "Password", "forgot_password": "Forgot your password?", "submit": "<PERSON><PERSON>", "guest_title": "Continue as a guest", "guest_continue": "Continue", "error_email": "Email address is required.", "error_password": "Password is required.", "register_link": "Sign Up"}, "orders": {"title": "Orders", "text": "Click on an Order ID to view details.", "order_number": "Order ID", "date": "Date", "payment_status": "Payment Status", "fulfillment_status": "Fulfillment Status", "status": "Status", "order_status": "Order Status", "total": "Total", "amount": "Amount", "none": "You haven't placed any orders yet.", "not_found": "No orders yet", "current": "Current", "past": "Past", "payment": "Payment"}, "order": {"title": "Order {{ name }}", "date": "Date", "items": "Order Item", "cancelled": "Order Cancelled on {{ date }}", "cancelled_reason": "Reason: {{ reason }}", "shipping_address": "Delivery Address", "billing_address": "Billing Address", "payment_status": "Status", "payment_method": "Payment method", "fulfillment_status": "Fulfillment Status", "discount": "Discount", "discount_code": "Discount code", "shipping_method": "Shipping method", "shipping_fee": "Shipping fee", "tax": "Tax", "product": "Product", "sku": "SKU", "price": "Price", "quantity": "Quantity", "total": "Total", "fulfilled_at": "Fulfilled {{ date }}", "subtotal": "Subtotal", "track_shipment": "Track shipment", "price_summary": "View price summary", "alias": "<PERSON><PERSON>", "phone": "Phone", "mobile_phone": "Mobile Phone", "order_status": "Order Status", "back": "Back to Orders", "status_info": "This order is {{ status }}"}, "recover_password": {"title": "Forgot Your Password?", "email": "Email Address", "submit": "Submit", "cancel": "Cancel", "subtext": "We will send you an email to reset your password.", "success_title": "Thank you for submitting.", "success_text": "If an account with that email address exists, you’ll receive a password reset link shortly. Please check your inbox (and spam/junk folder) for the reset instructions. If you did not receive an email, please ", "contact_us": "contact us.", "back_to_login": "Back to Log In", "error_email": "Please enter your email.", "error_not_found": "No account found with that email address."}, "reset_password": {"title": "Reset account password", "subtext": "Enter a new password for {{ email }}", "password": "Password", "password_confirm": "Confirm password", "submit": "Reset Password", "error_password": "Please enter password.", "error_password_confirm": "Please enter password confirmation."}, "register": {"title": "Create An Account", "sub_title": "Be a member today and receive exclusive benefits.", "point_1": "Welcome offer for new members", "point_2": "Earn points and redeem rewards", "point_3": "2x points during birthday month", "first_name": "First name*", "last_name": "Last name*", "name": "Name*", "phone": "Phone Number*", "birthday": "Birthday", "email": "Email Address*", "password": "Password*", "confirm_password": "Confirm Password*", "subscribe": "Sign me up for exclusive offers and product updates", "read_and_agree": "By creating an account, you agree to our ", "submit": "Create Account", "sign_in_note": "Already a member?", "sign_in_link": "Sign in here", "error_first_name": "First name is required.", "error_last_name": "Last name is required.", "error_name": "Name is required.", "error_phone": "Phone number is required.", "error_birthday": "Birthday is required.", "error_email": "Email address is required.", "error_password": "Password is required.", "error_read_and_agree": "Please check this field."}}, "layout": {"navigation": {"search": "Search", "toggle": "expand/collapse", "expand": "expand", "collapse": "collapse"}, "cart": {"title": "Your Cart", "empty": "Your Cart is Empty", "empty_subtext": "Let’s find your new favorite things!", "go_shopping": "Start Shopping", "items_count": {"one": "item", "other": "items"}}, "customer": {"account": "Account", "log_out": "Log out", "log_in": "Log in", "create_account": "Create account"}}, "products": {"product": {"description": "Description", "more_colours": "More colours", "total": "Total", "sold_out": "Sold out", "unavailable": "Unavailable", "on_sale": "Sale", "quantity": "Quantity", "add_to_cart": "Add to Cart", "add_to_bag": "Add to Bag", "select_options": "Select Options", "write_review": "Write A Review", "recommendation": "Recommendations", "recent_viewed": "Recently Viewed", "buy_now": "Buy Now", "in_stock": "In stock", "oos": "Out of stock", "stock_available": "Stock available", "preorder": "Preorder", "from": "From", "only_one_email_per_gift_card": "Only one (1) email per gift card.", "include_gift_message": "Send A Gift Message:", "sender_name": "Sender's Name", "recipient_name": "Recipient's Name", "recipient_email": "Recipient's <PERSON><PERSON>", "error_valid_email": "Please enter a valid email.", "message": "Message", "error_sender_name": "Sender's name can't be blank!", "error_recipient_name": "Recipient's name can't be blank!", "error_recipient_email": "Recipient's email can't be blank!", "error_message": "Message can't be blank!", "send_date": "Select Send Date:", "error_send_date": "Send date can't be blank!", "send_date_info": "Today's date will send after checkout.", "view_details": "View Details", "add_to_wishlist": "Add to Wishlist", "view_product": "View Product", "qty_left": "{{ qty }} pieces left!", "item_code": "Item Code", "please_select": "Please select"}}, "gift_cards": {"issued": {"title_html": "Here's your {{ value }} gift card for {{ shop }}!", "subtext": "Your gift card", "disabled": "Disabled", "expired": "Expired on", "active": "Expires on", "hi": "Hi", "your_have_received_from": "You have received a gift card from {{ sender_name }}!", "redeem_html": "Use code at checkout to redeem:", "shop_link": "Start shopping", "print": "Print", "remaining_html": "{{ balance }}", "copy": "Copy", "add_to_apple_wallet": "Add to Apple Wallet"}}, "date_formats": {"month_day_year": "%B %d, %Y"}, "header": {"login_register": "Sign in / Create account", "sign_in": "Sign In", "account": "My Account", "need_suggestions": "Need Some Suggestions?", "shop_all": "Shop All", "menu": "<PERSON><PERSON>", "wishlist": "Wishlist", "menu_back": "Back", "notification": "Notifications", "recent_searches": "Recent", "suggestions": "Popular", "clear_all": "Clear All", "results": "Results", "suggested_queries": "Suggestions"}, "breadcrumbs": {"home": "Home", "cart": "<PERSON><PERSON>"}, "sections": {"review_slider": {"play": "Play", "playing": "Playing..."}, "people": {"show_more": "Show more"}, "stores_list": {"all": "All", "show_more": "Show More"}}, "flash_cart": {"added_to_bag": "Added to Bag", "view_bag": "View your bag"}, "wishlist": {"no_products": "No products in your wishlist"}, "structured_data": {"breadcrumbs": {"home": "Home", "collections": "Collections", "collection": {"page": "{{ title }} - Page {{ page }}"}, "blog": {"page": "{{ title }} - Page {{ page }}"}, "search": {"title": "Search", "page": "Search result: {{ terms }} - Page {{ page }}"}, "cart": {"title": "<PERSON><PERSON>"}}}}