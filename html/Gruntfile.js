require('dotenv').config();

module.exports = function(grunt) {
    grunt.loadNpmTasks('grunt-purgecss');
    grunt.loadNpmTasks('grunt-contrib-concat');
    grunt.loadNpmTasks('grunt-contrib-uglify');
    grunt.loadNpmTasks('grunt-browser-sync');

    // Yeoman configuration
    let yeomanConfig = {
        src: 'src',
        dist: 'dist'
    }

    // Grunt configuration
    grunt.initConfig({
        yeoman: yeomanConfig,

        pkg: grunt.file.readJSON('package.json'),

        imageoptim: {
            png: {
                options: {
                    jpegMini: false,
                    imageAlpha: true,
                    quitAfter: true
                },

                src: ['assets/img/**/*.png']
            },

            jpg: {
                options: {
                    jpegMini: true,
                    imageAlpha: false,
                    quitAfter: true
                },

                src: ['assets/img/**/*.{jpg,JPG,jpeg,JPEG}']
            }
        },

        sass: {
            dist: {
                options: {
                    style: 'expanded'
                },
                files: {
                    'assets/css/account.css': 'assets/sass/account.scss',
                    'assets/css/blog.css': 'assets/sass/blog.scss',
                    'assets/css/cart.css': 'assets/sass/cart.scss',
                    'assets/css/collection.css': 'assets/sass/collection.scss',
                    'assets/css/index.css': 'assets/sass/index.scss',
                    'assets/css/style.css': 'assets/sass/main.scss',
                    'assets/css/page.css': 'assets/sass/page.scss',
                    'assets/css/password.css': 'assets/sass/password.scss',
                    'assets/css/product.css': 'assets/sass/product.scss',
                    'assets/css/search.css': 'assets/sass/search.scss',
                }
            }
        },

        autoprefixer: {
            options: {
                browsers: ['last 2 version', 'ie 9', 'ie 8']
            },

            multiple_files: {
                expand: true,
                flatten: true,
                src: 'assets/css/style.css',
                dest: 'assets/css'
            }
        },

        cssmin: {
            account: {
                expand: true,
                cwd: 'assets/css',
                src: ['account.css'],
                dest: '../assets',
                ext: '.css'
            },
            blog: {
                expand: true,
                cwd: 'assets/css',
                src: ['blog.css'],
                dest: '../assets',
                ext: '.css'
            },
            cart: {
                expand: true,
                cwd: 'assets/css',
                src: ['cart.css'],
                dest: '../assets',
                ext: '.css'
            },
            collection: {
                expand: true,
                cwd: 'assets/css',
                src: ['collection.css'],
                dest: '../assets',
                ext: '.css'
            },
            footer: {
                expand: true,
                cwd: 'assets/css',
                src: ['footer.css'],
                dest: '../assets',
                ext: '.css'
            },
            index: {
                expand: true,
                cwd: 'assets/css',
                src: ['index.css'],
                dest: '../assets',
                ext: '.css'
            },
            main: {
                expand: true,
                cwd: 'assets/css',
                src: ['style.css'],
                dest: '../assets',
                ext: '.css'
            },
            page: {
                expand: true,
                cwd: 'assets/css',
                src: ['page.css'],
                dest: '../assets',
                ext: '.css'
            },
            password: {
                expand: true,
                cwd: 'assets/css',
                src: ['password.css'],
                dest: '../assets',
                ext: '.css'
            },
            product: {
                expand: true,
                cwd: 'assets/css',
                src: ['product.css'],
                dest: '../assets',
                ext: '.css'
            },
            search: {
                expand: true,
                cwd: 'assets/css',
                src: ['search.css'],
                dest: '../assets',
                ext: '.css'
            }
        },

        purgecss: {
            my_target: {
                options: {
                    content: [
                        '../assets/vendor.js',
                        '../**/*.liquid'
                    ]
                },
                files: {
                    '../assets/style.css': ['../assets/style.css']
                }
            }
        },

        // jshint: {
        //     options: {
        //         force: true
        //     },

        //     beforeconcat: ['assets/js/*.js']
        // },

        concat: {
            dist: {
                src: [
                    './assets/js/vue.global.js',
                    './node_modules/vanilla-lazyload/dist/lazyload.min.js',
                    './node_modules/swiper/swiper-bundle.min.js',
                    './node_modules/axios/dist/axios.min.js',
                    './node_modules/waypoints/lib/noframework.waypoints.min.js',
                    './node_modules/dom-slider/dist/dom-slider.js',
                    './node_modules/clipboard/dist/clipboard.min.js',
                    './node_modules/nouislider/dist/nouislider.min.js',
                    './node_modules/scrollmagic/scrollmagic/minified/ScrollMagic.min.js',
                    './assets/js/animation.gsap.min.js',
                    './assets/js/TweenMax.min.js',
                    './assets/js/format-money.js',
                    './assets/js/360&5.js',
                    './assets/js/on-scroll.js',
                    './assets/js/on-click.js',
                    './assets/js/on-loaded.js',
                    './assets/js/on-pageshow.js',
                    './assets/js/on-resize.js',
                    './assets/js/on-submit.js',
                    './assets/js/header.js',
                    './assets/js/footer.js',
                    './assets/js/flash-cart.js',
                    './assets/js/quick-buy.js',
                ],
                dest: './assets/tmp/js/vendor.js'
            },
        },

        uglify: {
            options: {
                mangle: true
            },
            my_target: {
                files: {
                    '../assets/vendor.min.js': ['./assets/tmp/js/vendor.js']
                }
            }
        },

        watch: {
            options: {
                livereload: true
            },

            sass: {
                files: ['assets/sass/**/*.scss'],
                tasks: ['sass', 'autoprefixer', 'cssmin']
            },

            scripts: {
                files: ['assets/js/**/*.js'],
                tasks: ['concat', 'uglify'],
                options: {
                    livereload: true
                }
            }
        },

        // To use hot relaod, use theme kit command "theme watch --notify=tmp/theme.update"
        browserSync: {
            bsFiles: {
                src : '../tmp/theme.update'
            },
            options: {
                proxy: process.env.BS_PROXY_URL,
                reloadDelay: parseInt(process.env.BS_RELOAD_DELAY),
                watchTask: grunt.cli.tasks.includes('dev-hot-watch'),
                port: parseInt(process.env.BS_PORT),
                snippetOptions: {
                    rule: {
                        match: /<\/body>/i,
                        fn: function (snippet, match) {
                            return snippet + match
                        }
                    }
                }
            }
        }
    });

    // Load grunt tasks matching 'grunt-*'
    require('load-grunt-tasks')(grunt);

    // 4. Where we tell Grunt what to do when we type "grunt" into the terminal.
    grunt.registerTask('dev-watch', ['watch']);
    grunt.registerTask('dev-hot', ['browserSync']);
    grunt.registerTask('dev-hot-watch', ['browserSync', 'watch'])
    grunt.registerTask('build', [
        'sass', 
        'autoprefixer', 
        'cssmin', 
        'purgecss', 
        'concat', 
        'uglify'
    ]);
};
