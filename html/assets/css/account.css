.account-links {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.account-menu-box {
  overflow: auto;
  width: calc(100% + (2 * var(--container_padding)));
  margin-left: -16px;
  margin-right: -16px;
  padding-left: 16px;
  padding-right: 16px;
}
@media (min-width: 600px) {
  .account-menu-box {
    width: calc(100% + (2 * var(--container_padding_t)));
    margin-left: var(--container_padding_min_t);
    margin-right: var(--container_padding_min_t);
    padding-left: var(--container_padding_t);
    padding-right: var(--container_padding_t);
  }
}
@media (min-width: 1024px) {
  .account-menu-box {
    width: 100%;
    margin-left: 0;
    padding-left: 0;
  }
}

.account-menu-box::-webkit-scrollbar {
  display: none;
}

.account-menu {
  width: calc(100% + (2 * var(--container_padding)));
  white-space: nowrap;
}
.account-menu li::before {
  content: "";
  background-color: transparent;
  border-radius: 50%;
  margin-right: 5px;
  display: inline-block;
  width: 10px;
  height: 10px;
}
.account-menu li.active::before {
  background-color: var(--orange);
}
.account-menu li a {
  color: var(--black);
  opacity: 0.2;
}
.account-menu li.active a,
.account-menu li:hover a {
  opacity: 1;
  font-weight: 900 !important;
}
@media (min-width: 600px) {
  .account-menu {
    width: calc(100% + (2 * var(--container_padding_t)));
  }
  .account-menu li::before {
    margin-right: 10px;
  }
}
@media (min-width: 1024px) {
  .account-menu {
    width: 100%;
    white-space: unset;
  }
  .account-menu li::before {
    margin-right: 10px;
  }
}

.account-menu-dropdown {
  width: calc(100% + 28px);
  margin-left: -14px;
}
.account-menu-dropdown .global-select-div {
  border-radius: 0;
  padding-left: 20px;
  padding-right: 20px;
}
.account-menu-dropdown .global-select-div .options {
  border-radius: 0;
  margin-top: 0;
}
.account-menu-dropdown .global-select-div.active {
  border-color: var(--stroke);
}
@media (min-width: 600px) {
  .account-menu-dropdown .global-select-div {
    padding-left: 38px;
    padding-right: 38px;
  }
}
@media (min-width: 600px) {
  .account-menu-dropdown {
    width: calc(100% + 64px);
    margin-left: -32px;
  }
}

.order-wrapper {
  width: calc(100% + 40px);
  margin-left: -16px;
  padding-left: 20px;
  padding-right: 20px;
}
@media (min-width: 600px) {
  .order-wrapper {
    width: calc(100% + 76px);
    margin-left: -38px;
    padding-left: 38px;
    padding-right: 38px;
  }
}
@media (min-width: 1024px) {
  .order-wrapper {
    width: 100%;
    margin-left: 0;
    padding-left: 32px;
    padding-right: 32px;
  }
}
.order-wrapper .order-main-box {
  margin-left: 0;
  margin-right: 0;
}
.ws-nowrap {
  white-space: nowrap;
}

.order-table {
  width: 100%;
  margin-left: 0;
  margin-right: 0;
  overflow-x: auto;
}
.order-table::-webkit-scrollbar {
  background: transparent;
}
@media (min-width: 600px) {
  .order-table {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
  }
}
@media (min-width: 1024px) {
  .order-table {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
  }
}
.order-table table.data-list {
  width: auto;
  table-layout: fixed;
  margin-left: 16px;
  margin-right: 16px;
  border-spacing: 0;
  border-collapse: separate;
  border: 1px solid var(--border);
  min-width: calc(100% + 30px);
}
.order-table table.data-list > tbody > tr:not(:last-child) > td {
  border-bottom: 1px solid var(--stroke);
}
.order-table table.data-list > tbody > tr > td a {
  color: var(--black);
}
@media (min-width: 600px) {
  .order-table table.data-list {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    min-width: unset;
  }
}
@media (min-width: 1024px) {
  .order-table table.data-list {
    width: 100%;
  }
}
.order-table table .number {
  width: 120px;
}
@media (min-width: 600px) {
  .order-table table .number {
    width: 25%;
  }
}
@media (min-width: 1024px) {
  .order-table table .number {
    width: 25%;
  }
}
.order-table table .date {
  width: 130px;
}
@media (min-width: 600px) {
  .order-table table .date {
    width: 25%;
  }
}
@media (min-width: 1024px) {
  .order-table table .date {
    width: 25%;
  }
}
.order-table table .total {
  width: 130px;
}
@media (min-width: 600px) {
  .order-table table .total {
    width: 25%;
  }
}
@media (min-width: 1024px) {
  .order-table table .total {
    width: 25%;
  }
}
.order-table table .status {
  width: 75px;
}
@media (min-width: 600px) {
  .order-table table .status {
    width: 25%;
  }
}
@media (min-width: 1024px) {
  .order-table table .status {
    width: 25%;
  }
}

.orders .order .photo-info .icon {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  font-size: 10px;
}

.form-edit {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  right: -100%;
  overflow: hidden;
  transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.form-edit.active {
  right: 0;
}
.form-edit .btn-close {
  width: 24px;
  height: 24px;
}
@media (min-width: 600px) {
  .form-edit {
    width: 80%;
  }
}
@media (min-width: 1024px) {
  .form-edit {
    width: 500px;
    right: -515px;
  }
}
.form-edit .form-address-box {
  overflow: auto;
  height: calc(100vh - 100px);
}
@media (min-width: 600px) {
  .form-edit .form-address-box {
    height: auto;
  }
}

.account-blob {
  position: absolute;
  width: 100%;
  height: 200px;
  z-index: -999999;
  overflow: hidden;
  pointer-events: none;
}
@media (min-width: 1024px) {
  .account-blob {
    height: 100%;
  }
}
.account-blob::after {
  content: "";
  position: absolute;
  height: 500px;
  width: 500px;
  aspect-ratio: 1/1;
  background: radial-gradient(50% 50% at 50% 50%, #f8c834 0, #fbe399cc 53.5%, #fdf1cc00);
  top: -275px;
  right: -295px;
}
@media (min-width: 1024px) {
  .account-blob::after {
    top: -135px;
    left: -315px;
  }
}
@media (min-width: 1440px) {
  .account-blob::after {
    height: 650px;
    width: 650px;
    top: -160px;
    left: -420px;
  }
}

/*# sourceMappingURL=account.css.map */
