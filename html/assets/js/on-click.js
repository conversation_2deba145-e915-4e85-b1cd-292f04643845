document.addEventListener('click', function(e) {
    const el = e.target;

    /*-- Header utility nav dropdown */
    if(el.classList.contains('header-utility-nav-trigger')) {

        const currWrapperEl = el.closest('.header-utility-nav-item');

        el.classList.toggle('active');
        currWrapperEl.classList.toggle('active');

        if( currWrapperEl.classList.contains('active') ){
            currWrapperEl.classList.remove('dimmed');

            let prevSibling = currWrapperEl.previousElementSibling;
            let nextSibling = currWrapperEl.nextElementSibling;

            while( prevSibling ){
                prevSibling.classList.remove("active");
                prevSibling.classList.add("dimmed");
                prevSibling = prevSibling.previousElementSibling;
            }

            while( nextSibling ){
                nextSibling.classList.remove("active");
                nextSibling.classList.add("dimmed");
                nextSibling = nextSibling.nextElementSibling;
            }

        } else {
            let prevSibling = currWrapperEl.previousElementSibling;
            let nextSibling = currWrapperEl.nextElementSibling;

            while( prevSibling ){
                prevSibling.classList.remove("dimmed");
                prevSibling = prevSibling.previousElementSibling;
            }

            while( nextSibling ){
                nextSibling.classList.remove("dimmed");
                nextSibling = nextSibling.nextElementSibling;
            }

        }

        const target = document.querySelector(`#${el.dataset.target}`);
        target.classList.toggle('active');

        target.addEventListener( 'mouseleave', function() {
            setTimeout( () => {
                el.classList.remove('active');
                currWrapperEl.classList.remove('active');
                target.classList.remove('active');

                let prevSibling = currWrapperEl.previousElementSibling;
                let nextSibling = currWrapperEl.nextElementSibling;
                while( prevSibling ){
                    prevSibling.classList.remove("dimmed");
                    prevSibling = prevSibling.previousElementSibling;
                }
                while( nextSibling ){
                    nextSibling.classList.remove("dimmed");
                    nextSibling = nextSibling.nextElementSibling;
                }
              }, 2000); 
        });
    }

    /*-- Mobile dropdown */
    if(el.classList.contains('mobile-submenu-trigger')) {
        el.closest('.parent').querySelector('.mobile-dropdown').classList.toggle('show');
    }

    /*-- Currency option */
    if(el.classList.contains('custom-select_option')) {
        document.querySelector('#country-code').value = el.dataset.value;
        document.querySelector('#country-code').closest('form').submit();
    }

    /*-- Product variant dropdown */
    if(el.classList.contains('variant-dropdown-trigger')){
        el.nextElementSibling.classList.toggle('hide');
    }
    if(el.classList.contains('variant-dropdown-button')){
        el.closest('.variant-dropdown').classList.add('hide');
    }

    

    /** Global Select Div */
    if(el.classList.contains('global-select-div') || el.closest('.global-select-div')) {
        const selectDivEl = el.classList.contains('global-select-div') ? el : el.closest('.global-select-div');

        document.querySelectorAll('.global-select-div.active').forEach(function(gsd) {
            if(gsd != selectDivEl) {
                gsd.classList.remove('active');
            }
        });

        if(selectDivEl.classList.contains('active')) {
            selectDivEl.classList.remove('active');
        } else {
            selectDivEl.classList.add('active');
        }
    } else {
        document.querySelectorAll('.global-select-div.active').forEach(function(gsd) {
            gsd.classList.remove('active');
        });
    }

    if(el.classList.contains('option') && el.closest('.global-select-div')) {
        const optionEl = el;
        if(optionEl.classList.contains('disable')) {
            return;
        }
        const select = optionEl.closest('.options').nextElementSibling;
        const globalSelect = select.closest('.global-select-div');
        const selectedValue = optionEl.getAttribute('data-value');
        
        // Update select value
        select.value = selectedValue;
        
        // Update the selected attribute on options
        select.querySelectorAll('option').forEach(option => {
            option.removeAttribute('selected');
        });
        const selectedOption = select.querySelector(`option[value="${selectedValue}"]`);
        if (selectedOption) {
            selectedOption.setAttribute('selected', 'selected');
        }
        
        // Dispatch change event for all dropdowns
        select.dispatchEvent(new Event('change'));
        
        if(!globalSelect.classList.contains('selected-inline')) {
            globalSelect.querySelector('.inner .label').style.display = 'none';
        }
        if(globalSelect.querySelector('.option.active')) {
            globalSelect.querySelector('.option.active').classList.remove('active');
        }
        globalSelect.querySelector(`.option[data-value="${selectedValue}"]`).classList.add('active');
        
        // Handle variety dropdown with color circles
        const optionText = optionEl.querySelector('.option-text');
        const colorCircle = optionEl.querySelector('.color-circle');
        const textContainer = globalSelect.querySelector('.inner .text');
        
        if (optionText && colorCircle) {
            // For variety dropdown with color circles, include the circle and text
            textContainer.innerHTML = '';
            
            // Clone the color circle
            const clonedCircle = colorCircle.cloneNode(true);
            textContainer.appendChild(clonedCircle);
            
            // Add the text
            const textSpan = document.createElement('span');
            textSpan.textContent = optionText.textContent;
            textContainer.appendChild(textSpan);
        } else {
            // For regular dropdowns, use the entire option text content
            textContainer.textContent = optionEl.textContent;
        }
        
        // Close the dropdown after selection
        globalSelect.classList.remove('active');
    }
    /** End Global Select Div */

    /** on click .global-minus */
    if(el.classList.contains('global-minus') || el.closest('.global-minus')) {
        const globalMinBtn = el.classList.contains('global-minus') ? el : el.closest('.global-minus');
        const numInput = globalMinBtn.nextElementSibling;
        const currentNum = numInput.value;
        const minNum = numInput.getAttribute('min');
        if (minNum) {
            if (currentNum == minNum) return;
        }

        numInput.value = currentNum - 1;
        numInput.setAttribute('data-action', 'minus');
        numInput.dispatchEvent(new Event('input'));
    }
    /** End on click .global-minus */

    /** on click .global-plus */
    if(el.classList.contains('global-plus') || el.closest('.global-plus')) {
        const globalMinBtn = el.classList.contains('global-plus') ? el : el.closest('.global-plus');
        const numInput = globalMinBtn.previousElementSibling;
        const currentNum = parseInt(numInput.value);
        const maxNum = numInput.getAttribute('max');
        if (maxNum) {
            if (currentNum == maxNum) return;
        }

        numInput.value = currentNum + 1;
        numInput.setAttribute('data-action', 'plus');
        numInput.dispatchEvent(new Event('input'));
    }
    /** End on click .global-plus */

    /** on click .inpage-link */
    if(el.classList.contains('inpage-link') || el.closest('.inpage-link')) {
        e.preventDefault();
        let achr = el.getAttribute('href');
        let c = document.querySelector(''+achr+'');
        if (typeof(c) != 'undefined' && c != null) {
            let achr_target = document.querySelector(''+achr+'');
            let elPos = achr_target.getBoundingClientRect().top;
            let bodyPos = document.body.getBoundingClientRect().top;
            let tPos = elPos - bodyPos;
            window.scrollTo({
                top: tPos,
                behavior: 'smooth'
            });
        }
    }
    /** End on click .inpage-link */

    /** on click checkout button loading state */
	if(el.classList.contains('btn-checkout')) {
		const button = el;
		const tnc = el.closest('form').querySelector('input[name="tnc"]');
		// if (!tnc || !tnc.checked) {
		// 	alert(button.getAttribute('data-tnc-alert'));
		// 	return;
		// }
		button.style.height = `${button.offsetHeight}px`;
		button.style.width = `${button.offsetWidth}px`;
		button.innerHTML = $360.btnLoading();
		button.setAttribute('disabled', true);
		button.form.querySelector(`button[type='submit']`).click();
	}
    /** End on click checkout button loading state */

    /** on click .icon-show for login/register */
    if (el.classList.contains('icon-password') || el.closest('.icon-password')) {
        const icon = el.classList.contains('icon-password') ? el : el.closest('.icon-password');
        const password = icon.previousElementSibling;
        if (password.type == 'password') {
            password.type = 'text';
            icon.querySelector('.icon-show').classList.add('hidden');
            icon.querySelector('.icon-hide').classList.remove('hidden');
        } else {
            password.type = 'password';
            icon.querySelector('.icon-show').classList.remove('hidden');
            icon.querySelector('.icon-hide').classList.add('hidden');
        }
    }
    /** End of on click .icon-show for login/register */
});
