const $360 = {
    headerHeight: null,
    swiperProductCards: {},
    getHeaderHeight: function () {
        const header = document.getElementById('site-header');
        if (!header) return;
        this.headerHeight = header.offsetHeight;
    },
    lazyLoadInstance: new LazyLoad({
        elements_selector: '.lazy',
    }),
    setGlobalContentTopMargin: function () {
        if (!document.querySelector('body.sticky-header')) return;
        if (document.querySelector('body.transparent-header')) return;

        const el = document.querySelector('.global-content-top-margin');
        if (el) {
            el.style.marginTop = `${this.headerHeight - 1}px`;
        }
    },
    setStickyBelowHeaderPosition: function (hide_ann_bar = false) {
        const el = document.querySelectorAll('.sticky-below-header');
        const annBarEl = document.querySelector('.announcement-bar');
        let annBarHeight = 0;
        if (annBarEl) {
            annBarHeight = annBarEl.offsetHeight;
        }
        if (el) {
            el.forEach((e) => {
                if (hide_ann_bar) {
                    e.style.top = `${this.headerHeight - annBarHeight - 1}px`;
                } else {
                    e.style.top = `${this.headerHeight - 1}px`;
                }
            });
        }
    },
    setMenuWrapperPosition: function () {
        let annBarHeight = 0;

        const elMobile = document.querySelector('.mobile-menu-wrapper');
        const elDesktop = document.querySelector('.menu-dropdown-wrapper');
        const elSimple = document.querySelectorAll('.menu-dropdown.simple-dropdown');

        if (elMobile) {
            let topMenuSpacing = this.headerHeight;


            elMobile.style.height = `calc(100% - ${topMenuSpacing}px)`;
            elMobile.style.top = `${topMenuSpacing}px`;

            const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');
            mobileMenuOverlay.style.height = `calc(100% - ${topMenuSpacing}px)`;
            mobileMenuOverlay.style.top = `${topMenuSpacing}px`;

            const mobileSubMenuWrapper = document.querySelector('.mobile-submenu-wrapper');
            if(mobileSubMenuWrapper) {
                mobileSubMenuWrapper.style.height = `calc(100% - ${topMenuSpacing}px)`;
                mobileSubMenuWrapper.style.top = `${topMenuSpacing}px`;
            }
        }
        if (elDesktop) {
            if (document.querySelector('body.hide-bar-on-scroll.hide-bar')) {
                if (document.querySelector('.announcement-bar')) {
                    annBarHeight = document.querySelector('.announcement-bar').offsetHeight;
                }
                elDesktop.style.top = `${this.headerHeight - annBarHeight}px`;
            } else {
                elDesktop.style.top = `${this.headerHeight}px`;
            }
        }

        elSimple.forEach(es => {
            if (document.querySelector('body.hide-bar-on-scroll.hide-bar')) {
                if (document.querySelector('.announcement-bar')) {
                    annBarHeight = document.querySelector('.announcement-bar').offsetHeight;
                }
                es.style.top = `${this.headerHeight - annBarHeight}px`;
            } else {
                es.style.top = `${this.headerHeight}px`;
            }
        });
    },
    setFullHeightPage: function () {
        const el = document.querySelector('.full-height-page');
        if (el) {
            el.style.height = `calc(100vh - ${this.headerHeight}px)`;
        }
    },
    btnLoading: function () {
        return `
            <span class="btn-loading">
                <svg aria-hidden="true" focusable="false" role="presentation" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
                    <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                </svg>
            </span>
        `;
    },
    /**
     * args is an object of qty, variantId, properties, encodeProperties, button, showHeadercart
     * 
     * Add encodeProperties if there is properties need to be encode, 
     * example value is {properties_key: 'number'} 
     * the object value can be 'number' or 'text 
     * 
     * callback: function callback after success
     */
    addToCart: async function ({ qty, variantId, sellingPlanId, properties, encodeProperties, button, showHeaderCart }, callback) {
        // const qty = args.qty;
        // const variantId = args.variantId;
        // let properties = args.properties;
        // const encodeProperties = args.encodeProperties;
        // const button = args.button;
        showHeaderCart = showHeaderCart ? showHeaderCart : true;

        let prevText;
        if (button) {
            prevText = button.innerHTML;
            button.style.height = `${button.offsetHeight}px`;
            button.style.width = `${button.offsetWidth}px`;
            button.innerHTML = this.btnLoading();
            button.setAttribute('disabled', true);
        }

        const data = { quantity: qty, id: variantId };
        if (sellingPlanId) {
            data.selling_plan = sellingPlanId;
        }

        if (properties) {
            data.properties = properties;

            if (encodeProperties) {
                Object.keys(encodeProperties).forEach(function (key) {
                    if (encodeProperties[key] == 'number') {
                        const numberCrypt = new NumberCrypt();
                        data.properties[key] = numberCrypt.encode(data.properties[key])
                    }
                })
            }
        }

        try {
            const addToCart = await axios.post(window.Shopify.routes.root + 'cart/add.js', data);
            const cart = addToCart.data;
            if (document.getElementById('header-cart').childNodes.length) {
                // from_cart: true is to keep hide header cart
                this.showHeaderCart({ update: true, from_cart: true });
            }
            if (document.getElementById('cart-content')) {
                this.refreshCartPage()
            }
            axios.get(window.Shopify.routes.root + 'cart.js')
                .then((response) => {
                    const cart = response.data;
                    this.updateHeaderCartCount(cart);
                });
            this.showHeaderCart({ update: true });
            // flashCart.itemKeysToShow = [cart.key];
            // flashCart.handleShowPopup();
            if (callback) {
                callback(cart);
            }
        } catch (error) {
            console.log(error);
        } finally {
            if (button) {
                button.innerHTML = prevText;
                button.removeAttribute('disabled');
                button.style.height = '';
                button.style.width = '';
            }
        }
    },
    addToCartQuick: function (button) {
        const qty = button.closest('.quick-action').querySelector('.quick-qty').value;
        const variantId = button.getAttribute('data-variant-id');
        const args = { qty, variantId, button };

        $360.addToCart(args);
    },
    crossSellAtc: function (button) {
        const qty = 1;
        const variantId = button.getAttribute('data-variant-id');
        const args = { qty, variantId, button };

        $360.addToCart(args);
    },
    addToCartMultiple: function (args, callback) {
        const products = args.products;
        /*
        products array is object that consist of these
        {
            varId: String. the variant id,
            qty: Numeric. Quantity to be added
            properties: Object. The line item properties Object
            See the details descriptions on addToCart function
        }
        */
        const button = args.button;
        const thisObj = this;

        let prevText;
        if (button) {
            prevText = button.innerHTML;
            button.style.height = `${button.offsetHeight}px`;
            button.style.width = `${button.offsetWidth}px`;
            button.innerHTML = this.btnLoading();
            button.setAttribute('disabled', true);
        }

        const addedItemKeys = [];
        Shopify.moveAlong = () => {
            if (products.length) {
                const product = products.shift();

                const productData = {
                    id: product.varId,
                    quantity: product.qty
                };

                if (product.hasOwnProperty('properties')) {
                    productData.properties = product.properties;

                    if (productData.properties.hasOwnProperty('encodeProperties')) {
                        const encodeProperties = productData.properties.encodeProperties
                        Object.keys(encodeProperties).forEach(function (key) {
                            if (encodeProperties[key] == 'number') {
                                const numberCrypt = new NumberCrypt();
                                productData.properties[key] = numberCrypt.encode(productData.properties[key])
                            }
                        })
                    }
                };

                axios.post(window.Shopify.routes.root + 'cart/add.js', productData)
                    .then(function (response) {
                        addedItemKeys.push(response.data.key)
                        Shopify.moveAlong();
                    })
                    .catch(function (error) {
                        console.log(error)
                    });
            } else {
                if (button) {
                    button.innerHTML = prevText;
                    button.removeAttribute('disabled');
                    button.style.height = '';
                    button.style.width = '';
                }

                if (callback) {
                    callback();
                }

                // thisObj.showHeaderCart({update: true});
                if (document.getElementById('header-cart').childNodes.length) {
                    // from_cart: true is to keep hide header cart
                    this.showHeaderCart({ update: true, from_cart: true });
                }
                axios.get(window.Shopify.routes.root + 'cart.js')
                    .then((response) => {
                        const cart = response.data;
                        this.updateHeaderCartCount(cart);
                    });

                thisObj.showHeaderCart({ update: true });
                // flashCart.itemKeysToShow = addedItemKeys;
                // flashCart.handleShowPopup();

                if (document.getElementById('cart-content')) {
                    thisObj.refreshCartPage()
                }
            }
        };
        Shopify.moveAlong();
    },
    updateItemQty: async function ({ index, qty, groupId, removeSameGroup }, callback) {
        const thisObj = this;
        if (removeSameGroup) {
            // if(removeSameGroup && document.querySelector('.header-cart-loading') && !document.getElementById('cart-content')) {
            //     document.querySelector('.header-cart-loading').classList.add('active');
            // }
            document.querySelector('.page-loading').classList.add('active');
        }

        const doneUpdate = function (callback, cart) {
            if (callback) {
                callback(cart);
            }
            if (document.getElementById('cart-content')) {
                location.reload();
            }
        }

        try {
            const updateCart = await axios.post(window.Shopify.routes.root + 'cart/change.js', { line: index, quantity: qty });
            const cart = updateCart.data;

            if (cart.item_count == 0 && document.getElementById('cart-page-cross-sell')) {
                document.getElementById('cart-page-cross-sell').classList.add('hide-m');
            }

            if (groupId && removeSameGroup) {
                const anotherIndex = cart.items.findIndex(item => {
                    if (item.properties.hasOwnProperty('_group_id')) {
                        return item.properties._group_id == groupId && item.quantity != qty;
                    } else {
                        return false;
                    }
                });

                if (anotherIndex > -1) {
                    thisObj.updateItemQty({
                        index: anotherIndex + 1,
                        qty: qty,
                        groupId: groupId,
                        removeSameGroup: true
                    }, callback)
                } else {
                    setTimeout(() => {
                        document.querySelector('.page-loading').classList.remove('active');
                    }, 700);
                    doneUpdate(callback, cart);
                }
            } else {
                doneUpdate(callback, cart);
            }
        } catch (error) {
            console.log(error);
        }
    },
    updateItemQtyFromCart: function ({ el, index, qty, groupId, removeSameGroup }, callback) {
        const loadingEl = document.querySelector('.header-cart-loading');
        const itemEl = el.closest('.cart-item');

        if (loadingEl) {
            loadingEl.classList.add('active');
        }

        if (qty == 0) {
            const itemHeight = itemEl.offsetHeight;
            itemEl.style.height = `${itemHeight}px`;
            setTimeout(() => {
                itemEl.style.height = 0;
                itemEl.style.marginBottom = 0;
            }, 500);
        }

        $360.updateItemQty({
            index,
            qty,
            groupId,
            removeSameGroup
        }, function () {
            if (callback) {
                callback();
            }

            if (loadingEl) {
                loadingEl.classList.add('active');
            }
        });
    },
    showHeaderCart: function (args = {}, callback) {
        // callback has cart data object
        const from_cart = args.hasOwnProperty('from_cart') ? args.from_cart : false;
        const update = args.hasOwnProperty('update') ? args.update : false;
        const thisObj = this;
        let cart_url = window.Shopify.routes.root + 'cart';
        let crossSellSwiper = null;
        let imageBannerSwiper =null;

        const initCrossSellingSection = () => {
            if (crossSellSwiper == null) {
                crossSellSwiper = new Swiper('.header-cart .cross-sell .swiper', {
                    slidesPerView: 2.245,
                    slidesOffsetBefore: 0,
                    slidesOffsetAfter: 0,
                    spaceBetween: 12,
                    pagination: {
                        el: ".header-cart .cross-sell .swiper-pagination",
                        clickable: true
                    },
                    navigation: {
                        nextEl: ".header-cart .cross-sell .swiper-btn-next",
                        prevEl: ".header-cart .cross-sell .swiper-btn-prev",
                    },
                    breakpoints: {
                        600: {
                            slidesPerView: 3,
                            slidesOffsetBefore: 0,
                            slidesOffsetAfter: 0,
                            spaceBetween: 12,
                        },
                        1024: {
                            slidesPerView: 3,
                            slidesOffsetBefore: 0,
                            slidesOffsetAfter: 0,
                            spaceBetween: 16,
                        }
                    },
                    on: {
                        init: (swiper) => {
                            $360.lazyLoadInstance.update();
                        }
                    }
                });
            }
        }

        const initCartImageBanner = () => {
            const elSwiper = document.querySelector('#cart-drawer-main .empty-cart .image-banner-wrp');
            if (imageBannerSwiper == null && elSwiper) {
                imageBannerSwiper = new Swiper('#cart-drawer-main .empty-cart .image-banner-wrp', {
                    slidesPerView: 2.245,
                    slidesOffsetBefore: 16,
                    slidesOffsetAfter: 16,
                    spaceBetween: 8,
                    pagination: {
                        el: ".header-cart .cross-sell .swiper-pagination",
                        clickable: true
                    },
                    navigation: {
                        nextEl: ".header-cart .cross-sell .swiper-btn-next",
                        prevEl: ".header-cart .cross-sell .swiper-btn-prev",
                    },
                    breakpoints: {
                        600: {
                            slidesPerView: 2.5,
                            slidesOffsetBefore: 16,
                            slidesOffsetAfter: 16,
                            spaceBetween: 8,
                        },
                        1024: {
                            slidesPerView: 2.5,
                            slidesOffsetBefore: 20,
                            slidesOffsetAfter: 20,
                            spaceBetween: 12,
                        },
                        1440: {
                            slidesPerView: 2.5,
                            slidesOffsetBefore: 32,
                            slidesOffsetAfter: 32,
                            spaceBetween: 12,
                        }
                    },
                    on: {
                        init: (swiper) => {
                            $360.lazyLoadInstance.update();
                        }
                    }
                });
            }
        }

        const showIt = () => {
            document.getElementById('header-cart').action = cart_url;
            document.getElementById('header-cart').classList.add('active');
            document.querySelector('.header-cart-overlay').classList.add('active');
            this.hideFloatingApp();

            initCrossSellingSection();
            initCartImageBanner();

            // if(document.querySelector('.header-cart-loading')) {
            //     document.querySelector('.header-cart-loading').classList.remove('active');
            // }

            this.hideFloatingApp();
            this.disableScroll();
            thisObj.lazyLoadInstance.update();
            document.dispatchEvent(new CustomEvent("swym:cart-loaded"));


            const cartNote = document.querySelector('.header-cart-note-input');
            if (cartNote && cartNote.value) {
                const noteCheckbox = document.querySelector('.add-cart-note-trigger');
                const noteWrapper = document.querySelector('.header-cart-note-wrapper');

                if (noteCheckbox && noteWrapper) {
                    noteCheckbox.checked = true;
                    noteWrapper.style.display = 'block';
                    thisObj.adjustCartHeight();
                }
            }
        };

        if (!update && document.getElementById('header-cart').childNodes.length) {
            showIt();
            return;
        }

        const initCartNoteForm = () => {
            const { slideDown, slideUp, slideToggle } = window.domSlider

            const note_cb = document.querySelector('#hc-note-cb');
            if (!note_cb) return;

            note_cb.addEventListener('change', function (cEv) {
                const value = cEv.target.checked;
                const inputNote = document.querySelector('#hc-note-input');
                const cbContainer = note_cb.closest('.note-cb-container')
                const cbLabel = cbContainer.querySelector('.cb-note-label');
                if (!inputNote) return;
                slideToggle({ element: inputNote })
                if (!value) {
                    // cbLabel.innerHTML = cbContainer.querySelector('input[name="cb_label"]').value;
                    inputNote.value = '';
                } else {
                    // cbLabel.innerHTML = cbContainer.querySelector('input[name="cb_label_added"]').value;
                }
            })
        }

        axios.get(window.Shopify.routes.root + 'cart.js')
            .then((response) => {
                const cart = response.data;
                thisObj.updateHeaderCartCount(cart);

                if (document.getElementById('cart-content')) {
                    thisObj.updateCartPage(cart.item_count);
                }

                axios.get(window.Shopify.routes.root + 'cart?view=json')
                    .then((response) => {
                        const html = response.data;
                        document.getElementById('header-cart').innerHTML = html;
                        thisObj.adjustCartHeight();
                        if (!from_cart) {
                            showIt();
                        }
                        this.hideFloatingApp();
                        if (!from_cart) {
                            this.disableScroll();
                        }
                        // initCartNoteForm();
                        initCrossSellingSection();
                        initCartImageBanner();
                        if (cart.item_count > 0 && document.querySelector('#header-cart .cross-sell')) {
                            document.getElementById('header-cart').classList.remove('no-cross-sell');
                            document.getElementById('header-cart').classList.add('flex', 'column-reverse', 'jc-between', 't-flex-row', 't-jc-start');
                        } else {
                            document.getElementById('header-cart').classList.add('no-cross-sell');
                            document.getElementById('header-cart').classList.remove('flex', 'column-reverse', 'jc-between', 't-flex-row', 't-jc-start');
                        }
                        setTimeout(() => {
                            // Handle for injected app element
                            thisObj.adjustCartHeight();
                        }, 100);
                        if (callback) {
                            callback(cart)
                        }
                    })
                    .catch((error) => {
                        console.log(error);
                    });
            })
            .catch((error) => {
                console.log(error);
            });
        window.addEventListener("resize", function (resizeEv) {
            thisObj.adjustCartHeight();
        });
    },
    hideHeaderCart: function () {
        document.getElementById('header-cart').classList.remove('active');
        document.querySelector('.header-cart-overlay').classList.remove('active');
        document.querySelector('.header-cart-overlay').classList.add('fade-out');
        setTimeout(function () {
            document.querySelector('.header-cart-overlay').classList.remove('fade-out');
        }, 150);
        this.enableScroll();
        this.showFloatingApp();
    },
    updateHeaderCartCount(cart) {
        let count = 0;
        cart.items.forEach(item => {
            // if(item.properties) {
            //     if(!item.properties.hasOwnProperty('_group_id')) {
            //         count = count + item.quantity;
            //     } else {
            //         if(item.properties.hasOwnProperty('_group_parent')) {
            //             count = count + item.quantity;
            //         }
            //     }
            // } else {
            //     count = count + item.quantity;
            // }
            count = count + item.quantity;
        });

        document.querySelector('.header-cart-number .cart-count').textContent = count;
        if (count > 0) {
            document.querySelector('.header-cart-number').classList.add('has-item');
            // document.querySelector('.header-right').classList.add('has-cart-item');
        } else {
            document.querySelector('.header-cart-number').classList.remove('has-item');
            // document.querySelector('.header-right').classList.remove('has-cart-item');
        }
    },
    adjustCartHeight: function () {
        if (document.querySelector('.header-cart-bottom')) {
            let topHeight = document.querySelector('.header-cart-title').offsetHeight;
            if (document.querySelector('.listing-cart-wrapper .free-shipping-info')) {
                topHeight = topHeight + document.querySelector('.listing-cart-wrapper .free-shipping-info').offsetHeight;
            }
            const bottomHeight = document.querySelector('.header-cart-bottom').offsetHeight;
            const height = `calc(100% - ${bottomHeight}px)`;
            document.querySelector('.header-cart-middle').style.height = height;
        }
    },
    refreshCartPage: function () {
        const obj360 = this;
        if (document.getElementById('cart-content')) {
            axios.get(window.Shopify.routes.root + 'cart?view=jsonpage')
                .then(function (response) {
                    const cartRight = document.querySelector(".cart-right");
                    document.getElementById('cart-content').innerHTML = response.data;
                    document.querySelector('.page-loading').classList.remove('active');

                    // show/hide note mech
                    const { slideDown, slideUp, slideToggle } = window.domSlider

                    const note_cb = document.querySelector('#pc-note-cb');
                    if (!note_cb) return;

                    note_cb.addEventListener('change', function (cEv) {
                        const value = cEv.target.checked;
                        const inputNote = document.querySelector('#pc-note-input');
                        const cbContainer = note_cb.closest('.note-cb-container')
                        const cbLabel = cbContainer.querySelector('.cb-note-label');
                        if (!inputNote) return;
                        slideToggle({ element: inputNote })
                        if (!value) {
                            // cbLabel.innerHTML = cbContainer.querySelector('input[name="cb_label"]').value;
                            inputNote.value = '';
                        } else {
                            // cbLabel.innerHTML = cbContainer.querySelector('input[name="cb_label_added"]').value;
                        }
                    })
                })
                .catch(function (error) {
                    console.log(error);
                });
        }
    },
    handleCartNote: function (e) {
        const { slideDown, slideUp, slideToggle } = window.domSlider;
        const noteOverlay = document.querySelector('.header-cart-note-overlay');
        const noteForm = document.querySelector('.header-cart-note');
        const noteFormInline = document.querySelector('.add-cart-note-form-inline');
        const noteInput = document.querySelector('.header-cart-note-input');

        /*-- character counter */
        // const maxChars = noteInput.maxLength;
        // noteInput.nextElementSibling.querySelector('.maxlength').textContent = maxChars;
        // noteInput.addEventListener('input', function() {
        //     let counter = maxChars - noteInput.value.length;
        //     noteInput.nextElementSibling.querySelector('.counter').textContent = counter;
        // });

        if (e.target.classList.contains('add-cart-note-trigger')) {
            // noteOverlay.classList.add('active');
            // noteForm.classList.add('active');
            slideToggle({ element: noteOverlay });
            slideToggle({ element: noteForm });
        }

        if (e.target.classList.contains('add-cart-note-trigger-inline')) {
            // noteFormInline.classList.add('active');
            slideToggle({ element: noteFormInline });
        }

        if (!noteForm.hidden || !noteFormInline.hidden) {
            noteInput.focus();
        }
        if (e.target.classList.contains('header-cart-note-overlay')) {
            // noteOverlay.classList.remove('active');
            // noteForm.classList.remove('active');
            // noteFormInline.classList.remove('active');
            slideToggle({ element: noteOverlay });
            slideToggle({ element: noteForm });
        }
        if (e.target.classList.contains('btn-cancel-note')) {
            noteInput.value = '';
            noteOverlay.classList.remove('active');
            noteForm.classList.remove('active');
            noteFormInline.classList.remove('active');
        }
    },
    updateCartNote: async function (e, button) {
        const { slideDown, slideUp, slideToggle } = window.domSlider;
        const noteOverlay = document.querySelector('.header-cart-note-overlay');
        const noteForm = document.querySelector('.header-cart-note');
        const noteFormInline = document.querySelector('.add-cart-note-form-inline');
        let note = document.querySelector('.header-cart-note-input').value;
        let prevText;
        if (button) {
            prevText = button.innerHTML;
            button.style.height = `${button.offsetHeight}px`;
            button.style.width = `${button.offsetWidth}px`;
            button.innerHTML = this.btnLoading();
            button.setAttribute('disabled', true);
        }
        try {
            const updateCartNote = await axios.post(window.Shopify.routes.root + 'cart/update.js', { note: note });
            const cart = updateCartNote.data;
        } catch (error) {
            alert(error.response.data.message);
        } finally {
            if (button) {
                button.innerHTML = prevText;
                button.removeAttribute('disabled');
                button.style.height = '';
                button.style.width = '';
            }
            // noteOverlay.classList.remove('active');
            // noteForm.classList.remove('active');
            // noteFormInline.classList.remove('active');
            slideToggle({ element: noteOverlay });
            slideToggle({ element: noteForm });
        }
    },
    cartNoteCheckboxToggle: function (event, checkbox) {
        const wrapper = document.querySelector('.header-cart-note-wrapper');
        const noteInput = wrapper.querySelector('.header-cart-note-input');
        const loadingEl = document.querySelector('.header-cart-loading');

        if (checkbox.checked) {
            wrapper.style.display = 'block';
            noteInput.focus();
            // If there's existing text when checking, update the cart note
            if (noteInput.value.trim()) {
                if (loadingEl) {
                    loadingEl.classList.add('active');
                }
                this.cartNoteUpdate(noteInput.value).finally(() => {
                    if (loadingEl) {
                        loadingEl.classList.remove('active');
                    }
                });
            }
            this.adjustCartHeight();

        } else {
            wrapper.style.display = 'none';
            // Clear cart note in API but keep the text in textarea
            if (loadingEl) {
                loadingEl.classList.add('active');
            }
            this.cartNoteUpdate('').finally(() => {
                if (loadingEl) {
                    loadingEl.classList.remove('active');
                }
            });
            this.adjustCartHeight();

        }
    },

    cartNoteDebounceUpdate: (function () {
        let timeout = null;
        return function (event, textarea) {
            const loadingEl = document.querySelector('.header-cart-loading');
            const self = this;

            // Clear any existing timeout and loading state
            if (timeout) {
                clearTimeout(timeout);
            }
            if (loadingEl && loadingEl.classList.contains('active')) {
                loadingEl.classList.remove('active');
            }

            // Skip API call if input is empty or only whitespace
            if (!textarea.value.trim()) {
                return;
            }

            // Create new timeout
            timeout = setTimeout(async () => {
                // Only show loading when actually making the API call
                if (loadingEl) {
                    loadingEl.classList.add('active');
                }

                try {
                    await self.cartNoteUpdate(textarea.value);
                } catch (error) {
                    console.error('Error updating cart note:', error);
                } finally {
                    if (loadingEl) {
                        loadingEl.classList.remove('active');
                    }
                }
            }, 300);
        };
    })(),

    cartNoteUpdate: async function (noteValue) {
        try {
            const response = await axios.post(window.Shopify.routes.root + 'cart/update.js', {
                note: noteValue || ''
            });
            return response.data;
        } catch (error) {
            console.error('Error updating cart note:', error);
            throw error;
        }
    },
    productEachColorPicker: function (e, img_url) {
        const swatch_wrapper = e.target.closest('.swatch-wrapper');
        swatch_wrapper.querySelector('.swatch.active').classList.remove('active');

        const this_elem = e.target.closest('.swatch');
        this_elem.classList.add('active');

        const color = this_elem.getAttribute('data-value');
        const swatch_label = e.target.closest('.color-variant-picker').querySelectorAll('.swatch-label');

        swatch_label.forEach(elem => {
            elem.innerHTML = color;
        });

        const image_primary = e.target.closest('.product-each').querySelector('.primary img');
        const image_secondary = e.target.closest('.product-each').querySelector('.secondary img');

        if (img_url) {

            image_primary.setAttribute('data-src', '');
            image_primary.setAttribute('data-srcset', '');
            image_primary.setAttribute('srcset', '');
            image_primary.setAttribute('src', img_url);

            if (image_secondary) {
                image_secondary.setAttribute('data-src', '');
                image_secondary.setAttribute('data-srcset', '');
                image_secondary.setAttribute('srcset', '');
                image_secondary.setAttribute('src', img_url);
            }
        }
    },
    updateViewProductCard: function (el) {
        const card = el.closest('.product-card');
        card.querySelector('.top-con .product-image .primary .global-image-wrapper .image').srcset = ``;
        card.querySelector('.top-con .product-image .primary .global-image-wrapper .image').src = `${el.dataset.primary}`;
        card.querySelector('.secondary .global-image-wrapper .image').srcset = ``;
        card.querySelector('.secondary .global-image-wrapper .image').src = `${el.dataset.mood}`;
    },
    resetViewProductCard: function (el) {
        const card = el.closest('.product-card');
        card.querySelector('.top-con .product-image .primary .global-image-wrapper .image').srcset = ``;
        card.querySelector('.top-con .product-image .primary .global-image-wrapper .image').src = `${el.dataset.primaryDef}`;
        card.querySelector('.secondary .global-image-wrapper .image').srcset = ``;
        card.querySelector('.secondary .global-image-wrapper .image').src = `${el.dataset.moodDef}`;
    },
    selectProductCard: function (el) {
        const card = el.closest('.product-card');
        const color = el.dataset.color;

        card.querySelectorAll('.bottom-con .pc-color-swatches-wrp .swatch').forEach(swatch => {
            swatch.classList.remove('selected');
        });
        el.classList.add('selected');

        card.querySelectorAll('.top-con .galleries-wrp .swiper-slide').forEach(slide => {
            if(!slide.classList.contains(`${color}`)) {
                slide.classList.add('hide-m');
            } else {
                slide.classList.remove('hide-m');
            }
        });

        card.querySelector('.top-con .product-image .primary .global-image-wrapper .image').srcset = ``;
        card.querySelector('.top-con .product-image .primary .global-image-wrapper .image').src = `${el.dataset.primary}`;
        card.querySelector('.secondary .global-image-wrapper .image').srcset = ``;
        card.querySelector('.secondary .global-image-wrapper .image').src = `${el.dataset.mood}`;

        card.querySelector('.top-con').href = `${el.dataset.url}`;
        card.querySelector('.bottom-con .title-wrp').href = `${el.dataset.url}`;
        card.querySelector('.bottom-con .title-wrp').textContent = `${el.dataset.title}`;

        if(el.dataset.priceVaries === 'true') {
            if(card.querySelector('.bottom-con .price-varies').classList.contains('hide-m')) {
                card.querySelector('.bottom-con .price-varies').classList.remove('hide-m');
            }
            card.querySelector('.bottom-con .applied-price').textContent = $360.formatMoney(el.dataset.price, true);
            if(!card.querySelector('.bottom-con .striked-price').classList.contains('hide-m')) {
                card.querySelector('.bottom-con .striked-price').classList.add('hide-m');
            }
            if(!card.querySelector('.bottom-con .discount-tag').classList.contains('hide-m')) {
                card.querySelector('.bottom-con .discount-tag').classList.add('hide-m');
            }
        } else {
            if(!card.querySelector('.bottom-con .price-varies').classList.contains('hide-m')) {
                card.querySelector('.bottom-con .price-varies').classList.add('hide-m');
            }
            card.querySelector('.bottom-con .applied-price').textContent = $360.formatMoney(el.dataset.price, true);
            card.querySelector('.bottom-con .striked-price').textContent = $360.formatMoney(el.dataset.priceCompare, true);
            card.querySelector('.bottom-con .discount-tag').textContent = `${el.dataset.discountText}`;
            if(el.dataset.discount) {
                if(card.querySelector('.bottom-con .striked-price').classList.contains('hide-m')) {
                    card.querySelector('.bottom-con .striked-price').classList.remove('hide-m');
                }
                if(card.querySelector('.bottom-con .discount-tag').classList.contains('hide-m')) {
                    card.querySelector('.bottom-con .discount-tag').classList.remove('hide-m');
                }
            }
        }
    },
    swiperInCard:function (parentEl) {
        const thisObj = this;
        const uqNumber = Date.now() + Math.floor(Math.random() * 1000);
        const allCards = document.querySelectorAll(`${parentEl} .product-card`);
        allCards.forEach(pc => {
            let id = `${uqNumber}_${pc.dataset.id}`;
            if(!thisObj.swiperProductCards[id]){
                thisObj.swiperProductCards[id]= new Swiper(pc.querySelector('.top-con .galleries-wrp .swiper'), {
                    slidesPerView: 1,
                    spaceBetween: 0,
                    loop: false,
                    pagination: {
                        el: pc.querySelector('.top-con .galleries-wrp .swiper .swiper-pagination'),
                        clickable: true,
                    },
                    nested: true,
                    on: {
                        init: function() {
                            $360.lazyLoadInstance.update();
                        },
                        slideChange: function() {
                            $360.lazyLoadInstance.update();
                        },
                        resize: function() {
                            $360.lazyLoadInstance.update();
                        }
                    }
                });
            }
        });
    },
    accordionToggle: function (e, className = null) {
        // if className is provided, only 1 accordion would be open
        const { slideUp, slideToggle } = window.domSlider

        let el = e.target;
        if (!el.classList.contains('accordion-title')) {
            el = el.closest('.accordion-title');
        }

        if (el.classList.contains('active')) {
            el.classList.remove('active');
            if (el.closest('.accordion')) {
                // el.closest('.accordion').classList.remove('b-black');
                // el.closest('.accordion').classList.add('b-border');
            }
        } else {
            el.classList.add('active');
            if (el.closest('.accordion')) {
                // el.closest('.accordion').classList.add('b-black');
                // el.closest('.accordion').classList.remove('b-border');
            }
        }

        if (className) {
            const parentEl = el.closest('.accordion').parentNode;
            parentEl.querySelectorAll(`.accordion ${className}`).forEach(element => {
                if (element != el.nextElementSibling) {
                    element.previousElementSibling.classList.remove('active');
                    slideUp({ element });
                }
            });
        }

        slideToggle({ element: el.nextElementSibling });
    },
    getSearchContent: async function (tpl, q) {
        let surl = `/search?type=product&view=${tpl}&q=${q}`;
        try {
            const goFind = await axios.get(surl);
            return goFind;
        } catch (error) {
            alert(error.response.data.message);
        }
    },
    youtubeVideoId(url) {
        const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
        const match = url.match(regExp);
        return (match && match[2].length === 11) ? match[2] : null;
    },
    setCookie: function (name, value, expires, expiresUnit) {
        const now = new Date();
        if (expiresUnit == 'minute') {
            now.setTime(now.getTime() + (expires * 60 * 1000));
        }
        if (expiresUnit == 'hour') {
            now.setTime(now.getTime() + (expires * 3600 * 1000));
        }
        if (expiresUnit == 'day') {
            now.setTime(now.getTime() + (expires * 86400000));
        }
        document.cookie = `${name}=${value}; expires=${now.toUTCString()}; path=/`;
    },
    getCookie: function (name) {
        // const re = new RegExp(`(?<=${name}=)[^;]*`); Old regex does not support on safari
        const re = new RegExp(`(?:${name}=)[^;]*`);

        try {
            return document.cookie.match(re)[0];
        } catch {
            return null;
        }
    },
    formatDate(dateIsoString) {
        const date = new Date(dateIsoString);
        const year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();

        if (day < 10) {
            day = '0' + day;
        }
        if (month < 10) {
            month = '0' + month;
        }

        return `${day}/${month}/${year}`;
    },
    formatMoney(num, with_currency_code = false, no_decimals = false) {
        let formattedMoney = Shopify.formatMoney(num, $360Shop.money_format);
        if (with_currency_code) {
            formattedMoney = Shopify.formatMoney(num, $360Shop.money_with_currency_format);
        }

        if(no_decimals) {
            formattedMoney = formattedMoney.replace(/(\.[\d]+)|(\s[A-Z]{3}$)/g, '');
        }

        return formattedMoney;
    },
    scrollTo(elString, additionalSpacing = null) {
        const c = document.querySelector('' + elString + '');
        if (typeof (c) != 'undefined' && c != null) {
            const achr_target = document.querySelector('' + elString + '');
            const elPos = achr_target.getBoundingClientRect().top;
            const bodyPos = document.body.getBoundingClientRect().top;
            const tPos = elPos - bodyPos - $360.headerHeight - additionalSpacing;
            window.scrollTo({
                top: tPos,
                behavior: 'smooth'
            });
        }
    },
    setGlobalSelectDivValue: function (fieldId, selectValue) {
        const selectEl = document.getElementById(fieldId);

        selectEl.value = selectValue;
        const wrapperEl = selectEl.closest('.global-select-div');
        if (!wrapperEl.classList.contains('selected-inline')) {
            wrapperEl.querySelector('.label').innerHTML = '';
        }
        wrapperEl.querySelectorAll('.option').forEach(optionEl => {
            if (optionEl.getAttribute('data-value') == selectValue) {
                wrapperEl.querySelector('.text').innerHTML = optionEl.textContent;
            }
        });
    },
    updateQueryString: function (key, value, url) {
        if (!url) url = window.location.href;

        let updated = ''
        var re = new RegExp("([?&])" + key + "=.*?(&|#|$)(.*)", "gi"),
            hash;

        if (re.test(url)) {
            if (typeof value !== 'undefined' && value !== null) {
                updated = url.replace(re, '$1' + key + "=" + value + '$2$3');
            } else {
                hash = url.split('#');
                url = hash[0].replace(re, '$1$3').replace(/(&|\?)$/, '');
                if (typeof hash[1] !== 'undefined' && hash[1] !== null) {
                    url += '#' + hash[1];
                }
                updated = url;
            }
        } else {
            if (typeof value !== 'undefined' && value !== null) {
                var separator = url.indexOf('?') !== -1 ? '&' : '?';
                hash = url.split('#');
                url = hash[0] + separator + key + '=' + value;
                if (typeof hash[1] !== 'undefined' && hash[1] !== null) {
                    url += '#' + hash[1];
                }
                updated = url;
            } else {
                updated = url;
            }
        }

        window.history.replaceState({ path: updated }, '', updated);
    },
    productCard2VariantChange: function (el) {
        const productCard = el.closest('.product-card-2');
        const variants = JSON.parse(decodeURIComponent(productCard.getAttribute('data-variants')));

        let option1 = null;
        let option2 = null;
        let option3 = null;
        if (productCard.querySelector('select.option1')) {
            option1 = productCard.querySelector('select.option1').value;
        }
        if (productCard.querySelector('select.option2')) {
            option2 = productCard.querySelector('select.option2').value;
        }
        if (productCard.querySelector('select.option3')) {
            option3 = productCard.querySelector('select.option3').value;
        }

        const filteredVariants = variants.filter(v => v.option1 == option1 && v.option2 == option2 && v.option3 == option3);
        if (filteredVariants.length) {
            const variantId = filteredVariants[0].id;
            productCard.querySelector('.add-to-cart').setAttribute('data-variant-id', variantId);
        }
    },
    getScrollbarWidth: function () {
        const outer = document.createElement('div');
        outer.style.visibility = 'hidden';
        outer.style.overflow = 'scroll';
        outer.style.msOverflowStyle = 'scrollbar'; // Needed for WinJS apps
        document.body.appendChild(outer);

        const inner = document.createElement('div');
        outer.appendChild(inner);

        const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;

        // Clean up temporary elements
        outer.parentNode.removeChild(outer);

        return scrollbarWidth;
    },
    disableScroll: function () {
        const gap = this.getScrollbarWidth();
        document.querySelector('body').classList.add('disable-scroll');
        document.body.style.paddingRight = `${gap}px`;
        if (document.querySelector('.announcement-bar')) {
            document.querySelector('.announcement-bar').style.paddingRight = `${gap}px`;
        }
        document.querySelector('.header-con__inner').style.marginRight = `${gap}px`;
        // document.querySelector('.header-nav').style.paddingRight = `${gap}px`;
    },
    enableScroll: function () {
        document.querySelector('body').classList.remove('disable-scroll');
        document.body.style.paddingRight = null;
        if (document.querySelector('.announcement-bar')) {
            document.querySelector('.announcement-bar').style.paddingRight = null;
        }
        document.querySelector('.header-con__inner').style.marginRight = null;
        // document.querySelector('.header-nav').style.paddingRight = null;
    },
    hideFloatingApp: function () {
        if(document.getElementById('tidio-chat')) {
            document.getElementById('tidio-chat').classList.add('hide-m');
        }
    },
    showFloatingApp: function () {
        if(document.getElementById('tidio-chat')) {
            document.getElementById('tidio-chat').classList.remove('hide-m');
        }
    },
    updateCartPage(cart) {
        const mainCon = document.querySelector('.global-content-top-margin');
        const allSection = mainCon.querySelectorAll('.shopify-section');
        let target = null;
        allSection.forEach(sec => {
            target = sec.querySelector('#cart-content');
            if (!target) {
                if (cart > 0) {
                    sec.classList.remove('hide-m');
                } else {
                    sec.classList.add('hide-m');
                }

            }
        });
    },
    accountRedirect: function (e) {
        if (e.value != 'history') {
            if (e.value == 'manage') {
                window.location.href = "/tools/recurring/login/";
            } else {
                window.location.href = "/account/" + e.value;
            }
        } else {
            window.location.href = "/account";
        }
    },
    productCardChangeColor(el) {
        const productCard = el.closest('.product-card');
        const title = el.getAttribute('data-title');
        const url = el.getAttribute('data-url');
        const imgUrl = el.getAttribute('data-image-url');
        const productId = el.getAttribute('data-product-id');
        const variantId = el.getAttribute('data-variant-id');
        const quickBuyFunction = el.getAttribute('data-quick-buy');

        productCard.querySelector('.global-image-wrapper').innerHTML = `<img src="${imgUrl}" />`;

        productCard.querySelector('.image-link').setAttribute('href', url);
        productCard.querySelector('.image-link').setAttribute('title', title);

        productCard.querySelector('.details-con .top-con a').setAttribute('href', url);
        productCard.querySelector('.details-con .top-con a').setAttribute('title', title);
        productCard.querySelector('.details-con .top-con a').textContent = title;

        productCard.querySelector('button.wishlist').setAttribute('data-product-id', productId);
        productCard.querySelector('button.wishlist').setAttribute('data-product-url', url);
        productCard.querySelector('button.wishlist').setAttribute('data-variant-id', variantId);
        setTimeout(() => {
            document.dispatchEvent(new CustomEvent("swym:product-list-updated"));
        }, 500);

        productCard.querySelector('.quick-buy').setAttribute('onclick', quickBuyFunction);

        if (productCard.querySelector('.product-color-swatch .each.active')) {
            productCard.querySelector('.product-color-swatch .each.active').classList.remove('active');
        }
        el.classList.add('active');
    },
    checkCartDrawer() {
        setTimeout(() => {
            if (window.location.hash && window.location.hash === '#cart-open') {
                this.showHeaderCart({ update: true });
            }
        }, 300);
    }
}

const refresh360_functions = [];

refresh360_functions.push('getHeaderHeight');
refresh360_functions.push('setGlobalContentTopMargin');
refresh360_functions.push('setStickyBelowHeaderPosition');
refresh360_functions.push('setMenuWrapperPosition');
refresh360_functions.push('setFullHeightPage');
refresh360_functions.push('checkCartDrawer');
