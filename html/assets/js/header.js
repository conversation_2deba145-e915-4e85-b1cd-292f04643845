let headerVue;
document.addEventListener('DOMContentLoaded', function (e) {
    let annBarEndTime = null;
    if (document.querySelector('.announcement-bar')) {
        annBarEndTime = document.querySelector('.announcement-bar').getAttribute('data-end');
    }

    headerVue = Vue.createApp({
        delimiters: ['${', '}'],
        data() {
            return {
                annBarEndTime: annBarEndTime,
                annBarCountDown: null,
                annBarHeight: null,
                showHeaderBar: true,
                activeSubmenu: null,
                hasSubmenuActive: false,
                subMenuFadeIn: false,
                showMobileMenu: false,
                showMobileSubmenu: false,
                activeMobileSubmenu: null,
                mobileSubmenuShopAllLink: null,
                showSearch: false,
                searchFocus: false,
                searchResult: null,
                searchTerm: '',
                searchDelayTimer: null,
                showOverlay: false,
                overlayFadeOut: false,
                searchOverlayFadeOut: false,
                activeMobileTab: '',
                isMobile: true,
                isDesktop: false,
                isHd: false,
                searchHistory: [],
                searchRecent: false,
                searchNotFound: false,
            }
        },
        mounted() {
            this.detectScreen();
            this.setHeaderStyle();

            window.addEventListener('resize', () => {
                this.detectScreen();
            });

            if (document.querySelector('.announcement-bar')) {
                this.annBarHeight = document.querySelector('.announcement-bar').offsetHeight;
            }
            $360.lazyLoadInstance.update();
            if ($360.getCookie('no-bar') == 'true') {
                this.showHeaderBar = false;
            }

            // set promo bar countdown
            if (this.annBarEndTime) {
                let cd = this.calculateCountdown(this.annBarEndTime);
                if (cd.seconds > 0) {
                    let globalMarginTopUpdated = false;
                    const doCountDown = setInterval(() => {
                        cd = this.calculateCountdown(this.annBarEndTime);
                        if (cd.seconds > -1) {
                            this.annBarCountDown = `Ends in ${cd.days}d ${cd.hours}h ${cd.minutes}m ${cd.seconds}s`
                        } else {
                            this.annBarCountDown = null;
                            clearInterval(doCountDown);
                        }

                        $360.getHeaderHeight();
                        $360.setMenuWrapperPosition();
                        if (!globalMarginTopUpdated) {
                            $360.setGlobalContentTopMargin();
                        }
                        globalMarginTopUpdated = true;
                    }, 1000);
                }
            }

            let previousScrollY = window.scrollY;

            let extra;
            let stickyNav = document.querySelector(".sticky-nav");
            if (stickyNav) {
                extra = typeof stickyNav.dataset.extra == "undefined" ? 25 : parseInt(stickyNav.dataset.extra);
            }
            window.addEventListener('scroll', () => {
                let scrollType;
                const currentScrollY = window.scrollY;
                if (currentScrollY > previousScrollY) {
                    scrollType = 'down';
                } else if (currentScrollY < previousScrollY) {
                    scrollType = 'up';
                }
                previousScrollY = currentScrollY;

                if (window.innerWidth <= 768) {
                    extra = -0.5;
                }

                const scrollPosition = document.documentElement.scrollTop || document.body.scrollTop || 0;

                if (scrollType == 'down') {
                    if (scrollPosition >= 300) {
                        document.querySelector('body').classList.add('scrolled');
                        document.querySelector('body').classList.remove('transparent-search');
                        if (document.querySelector('body.hide-bar-on-scroll') && document.querySelector('.announcement-bar')) {
                            document.querySelector('body').classList.add('hide-bar');
                            document.querySelector('.site-header').style.top = `-${this.annBarHeight}px`;
                            if (stickyNav) {
                                setTimeout(() => {
                                    stickyNav.style.top = `${document.querySelector('.site-header').offsetHeight - this.annBarHeight + extra}px`;
                                }, 300);
                            }
                            $360.setStickyBelowHeaderPosition(true);
                        }
                    } else {
                        document.querySelector('body').classList.remove('scrolled');
                        if (document.querySelector('body.hide-bar-on-scroll') && document.querySelector('.announcement-bar')) {
                            document.querySelector('body').classList.remove('hide-bar');
                            document.querySelector('.site-header').style.top = null;
                            if (stickyNav) {
                                setTimeout(() => {
                                    stickyNav.style.top = `${document.querySelector('.site-header').offsetHeight + extra}px`;
                                }, 300);
                            }
                            $360.setStickyBelowHeaderPosition();
                        }
                    }
                } else {
                    if (scrollPosition < 300) {
                        document.querySelector('body').classList.remove('scrolled');
                    }
                    document.querySelector('body').classList.remove('hide-bar');
                    document.querySelector('.site-header').style.top = null;
                    if (stickyNav) {
                        setTimeout(() => {
                            stickyNav.style.top = `${document.querySelector('.site-header').offsetHeight + extra}px`;
                        }, 300);
                    }
                    $360.setStickyBelowHeaderPosition();
                }
                this.setHeaderSearchActiveTopPosition();
            });

            if (document.querySelector('.mobile-menu-main .top-cont button')) {
                this.activeMobileTab = document.querySelector('.mobile-menu-main .top-cont button').textContent;
            }

            document.querySelectorAll('.bottom-cont-btn button').forEach(el => {
                el.addEventListener('click', () => {
                    let menu = "";
                    if (document.querySelector('.bottom-cont .menu-dropdown.expand')) {
                        menu = document.querySelector('.bottom-cont .menu-dropdown.expand').dataset.menu;
                        document.querySelector('.bottom-cont .menu-dropdown.expand').classList.remove('expand');
                        document.querySelector('.bottom-cont .bottom-cont-btn button.toggle').classList.remove('toggle');
                    }
                    if (menu != el.dataset.menu) {
                        document.querySelector('.bottom-cont .menu-dropdown[data-menu="' + el.dataset.menu + '"]').classList.add('expand');
                        el.classList.add('toggle');
                    }
                });
            });

            
            window.addEventListener('resize', () => {
                this.setHeaderSearchActiveTopPosition();
            });

                        
            // Load search history from localStorage
            const savedHistory = localStorage.getItem('searchHistory');
            if (savedHistory) {
                this.searchHistory = JSON.parse(savedHistory);
            }
        },
        methods: {
            detectScreen() {
                if (window.innerWidth < 1024) {
                    this.isMobile = true;
                    this.isDesktop = false;
                    this.isHd = false;
                }
                if (window.innerWidth >= 1024 && window.innerWidth < 1366) {
                    this.isMobile = false;
                    this.isDesktop = true;
                    this.isHd = false;
                }
                if (window.innerWidth >= 1366) {
                    this.isMobile = false;
                    this.isDesktop = false;
                    this.isHd = true;
                }
            },
            setHeaderStyle() {
                let scrollPosition = document.documentElement.scrollTop || document.body.scrollTop || 0;
                if (scrollPosition >= 200) {
                    document.querySelector('body').classList.add('header-force-bg');
                }

                window.addEventListener('scroll', () => {
                    scrollPosition = document.documentElement.scrollTop || document.body.scrollTop || 0;
                    if (scrollPosition >= 200) {
                        document.querySelector('body').classList.add('header-force-bg');
                    } else {
                        document.querySelector('body').classList.remove('header-force-bg');
                    }
                    $360.getHeaderHeight();
                    $360.setStickyBelowHeaderPosition();
                });
            },
            calculateCountdown(futureDateTimeString) {
                // Parse the input date-time string
                const [datePart, timePart] = futureDateTimeString.split(' ');
                const [ftDay, ftMonth, ftYear] = datePart.split('-').map(Number);
                const [ftHours, ftMinutes, ftSeconds] = timePart.split(':').map(Number);

                // Create a Date object for the future date
                const futureDate = new Date(ftYear, ftMonth - 1, ftDay, ftHours, ftMinutes, ftSeconds);

                // Get the current timestamp
                const now = new Date();

                // Calculate the time difference in milliseconds
                const timeDifference = futureDate - now;

                // Convert milliseconds to days, hours, minutes, and seconds
                const days = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
                const hours = Math.floor((timeDifference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeDifference % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeDifference % (1000 * 60)) / 1000);

                this.annBarHeight = document.querySelector('.announcement-bar').offsetHeight;

                // Return the countdown as an object
                return {
                    days,
                    hours,
                    minutes,
                    seconds
                };
            },
            showHeaderCart() {
                $360.showHeaderCart();
            },
            fadeOutDropDown() {
                if (this.showSearch) return;
                this.subMenuFadeIn = false;
                this.activeSubmenu = null;
                const scrollPosition = document.documentElement.scrollTop || document.body.scrollTop || 0;
                if (scrollPosition < 200) {
                    document.querySelector('body').classList.remove('header-force-bg');
                }
                if(this.$refs['menu-dropdown-wrapper']){
                    this.$refs['menu-dropdown-wrapper'].style.height = null;
                }
                if (!this.searchResult) {
                    $360.enableScroll();
                }
                this.removeHeaderLinkOpacity();
                setTimeout(() => {
                    this.hasSubmenuActive = false;
                }, 300);
            },
            setMenuDropdownWrapperHeight() {
                setTimeout(() => {
                    menuDropdown = document.querySelector(`.menu-dropdown.show[data-index="${this.activeSubmenu}"`);
                    if (menuDropdown) {
                        let height = menuDropdown.offsetHeight;
                        if (menuDropdown.classList.contains('simple-dropdown')) {
                            height = 0;
                        }
                        this.$refs['menu-dropdown-wrapper'].style.height = `${height}px`;
                    }
                }, 100);
            },
            setHeaderSearchTopPosition() {
                const headerSearch = document.querySelector('.header-search-form');
                const headerSearchHeight = headerSearch.offsetHeight;
                headerSearch.style.top = `-${headerSearchHeight}px`;
            },
            setHeaderSearchActiveTopPosition() {
                let topMenuSpacing = 0;
                const headerSearch = document.querySelector('.header-search-form');

                // if (document.querySelector('body.hide-bar-on-scroll.hide-bar')) {
                    if (document.querySelector('.announcement-bar')) {
                        topMenuSpacing = document.querySelector('.announcement-bar').offsetHeight;
                    }
                    if(document.querySelector('body.hide-bar-on-scroll.hide-bar')){ 
                        topMenuSpacing = 0;
                    }
                // }
                headerSearch.style.top = `${topMenuSpacing}px`;
            },
            showDropdown(index) {
                if (window.innerWidth < 1024) return;

                $360.lazyLoadInstance.update();

                setTimeout(() => {
                    document.querySelectorAll('.header-link').forEach(hl => {
                        if (hl.getAttribute('data-index') != index) {
                            hl.classList.add('c-brown-40');
                            if(hl.querySelector('.icon svg path')){
                                hl.querySelector('.icon svg path').setAttribute('fill', 'currentColor');
                            }
                        }
                    });
                }, 150);

                if (document.querySelector(`.menu-dropdown[data-index="${index}"]`)) {
                    this.activeSubmenu = index;
                    this.hasSubmenuActive = true;
                    $360.disableScroll();
                    this.showOverlay = true;
                    document.querySelector('body').classList.add('header-force-bg');

                    // Show first banner group when dropdown opens
                    const firstBannerGroup = document.querySelector(`.menu-dropdown[data-index="${index}"] .each-banner-product[data-num="1"]`);
                    if (firstBannerGroup) {
                        // Hide all banner groups first
                        document.querySelectorAll(`.menu-dropdown[data-index="${index}"] .each-banner-product`).forEach(banner => {
                            banner.style.display = 'none';
                        });
                        // Show first banner group
                        firstBannerGroup.style.display = 'flex';
                    }

                    if (document.querySelector(`.menu-dropdown[data-index="${index}"]`).classList.contains('simple-dropdown')) {
                        const leftPos = document.querySelector(`.header-nav .header-link[data-index="${index}"]`).getBoundingClientRect().left;
                        document.querySelector(`.menu-dropdown[data-index="${index}"]`).style.left = `${leftPos}px`;
                    }

                    setTimeout(() => {
                        this.subMenuFadeIn = true;
                    }, 100);
                } else {
                    this.showOverlay = false;
                    this.fadeOutDropDown();
                }

                this.setMenuDropdownWrapperHeight();
                $360.hideFloatingApp();
            },
            hideDropdown() {
                if (this.showOverlay) {
                    this.hideOverlay();
                }

                this.fadeOutDropDown();
            },
            handleLeaveMenu() {
                this.removeHeaderLinkOpacity();
            },
            removeHeaderLinkOpacity() {
                if (this.activeSubmenu) return;
                if (document.querySelector('.header-link.c-brown-40')) {
                    document.querySelectorAll('.header-link.c-brown-40').forEach(hl => {
                        hl.classList.remove('c-brown-40');
                       
                    });
                }
            },
            toggleMobileMenu() {
                this.showMobileMenu = !this.showMobileMenu;
                if (this.showMobileMenu) {
                    $360.hideFloatingApp();
                    document.body.classList.add('header-force-bg');
                    this.clearSearch(true, true);
                    $360.disableScroll();
                } else {
                    $360.showFloatingApp();
                    $360.enableScroll();
                    if (!document.body.classList.contains('scrolled')) {
                        document.body.classList.remove('header-force-bg');
                    }
                }
                setTimeout(() => {
                    this.showMobileSubmenu = false;
                }, 500);
                const iconMenu = document.querySelector('.header-mobile-menu .icon-menu');
                const iconCloseMenu = document.querySelector('.header-mobile-menu .icon-close-menu');
                const body = document.querySelector('body');

                if (this.showMobileMenu) {
                    iconMenu.classList.remove('flex');
                    iconMenu.classList.add('hide-m');
                    iconCloseMenu.classList.remove('hide-m');
                    iconCloseMenu.classList.add('flex');
                } else {
                    iconMenu.classList.remove('hide-m');
                    iconMenu.classList.add('flex');
                    iconCloseMenu.classList.remove('flex');
                    iconCloseMenu.classList.add('hide-m');
                }
            },
            clickParentMenu(e, title) {
                let el = e.target.classList.contains('main-menu-title') ? e.target : e.target.closest('.main-menu-title');
                this.mobileSubmenuShopAllLink = el.getAttribute('href');

                if (el.closest('li').classList.contains('with-children')) {
                    this.activeMobileSubmenu = title;
                    this.showMobileSubmenu = true;
                    setTimeout(() => {
                        $360.lazyLoadInstance.update();
                    }, 500);
                } else {
                    window.location.href = el.getAttribute('href');
                }
            },
            async goSearch() {
                $360.disableScroll();

                if (this.searchDelayTimer) {
                    clearTimeout(this.searchDelayTimer);
                    this.searchDelayTimer = null;
                }

                // Show recent searches immediately if search term is empty
                if (this.searchTerm == '') {
                    this.searchResult = null;
                    this.searchRecent = this.searchHistory.length > 0;
                    this.searchNotFound = false;
                    return;
                }

                this.searchDelayTimer = setTimeout(async () => {

                    try {
                        // const { data } = await axios.get(`${window.Shopify.routes.root}search/suggest?q=${this.searchTerm}&resources[type]=product,page,collection&resources[limit_scope]=each&resources[options][unavailable_products]=hide&resources[options][fields]=title,product_type,variants.title&section_id=predictive-search`);
                        const { data } = await axios.get(`${window.Shopify.routes.root}search/suggest?q=${this.searchTerm}&resources[type]=product,page,collection,article,query&resources[limit_scope]=each&resources[options][prefix]=last&resources[options][fields]=title,product_type,variants.title,variants.sku&section_id=predictive-search`);
                        const resultsMarkup = new DOMParser().parseFromString(data, 'text/html').querySelector('#shopify-section-predictive-search').innerHTML;
                        this.searchResult = resultsMarkup;
                        this.searchRecent = false;
                        if(this.searchResult.includes('text-not-found')){
                            this.searchNotFound = true;
                        } else {
                            this.searchNotFound = false;
                        }
                        setTimeout(() => {
                            $360.lazyLoadInstance.update();
                        }, 500);
                    } catch (error) {
                        console.log(error)
                    }
                }, 800);
            },
            onHoverOverlay() {
                this.hideDropdown();
                // this.fadeOutDropDown();
            },
            hideOverlay() {
                this.overlayFadeOut = true;
                setTimeout(() => {
                    this.showOverlay = false;
                    this.overlayFadeOut = false;
                }, 150);
            },
            clearSearch(fromMobile = false, fromSearch = false) {
                this.searchTerm = '';
                this.searchResult = null;
                this.searchFocus = false;
                this.searchOverlayFadeOut = true;
                this.showSearch = false;
                this.searchNotFound = false;

                if (!fromSearch) {
                    this.searchRecent = false;
                }


                if(!fromMobile){
                
                    document.querySelector('body').classList.remove('header-force-bg');
                    document.querySelector('body').classList.remove('search-active');
                    if(!document.querySelector('body').classList.contains('scrolled')) {
                        if(!fromMobile){
                            document.querySelector('body').classList.add('transparent-search');
                        }
                    } else {
                        document.querySelector('body').classList.add('header-force-bg');
                    }
                } else {
                    if(!fromSearch){
                        document.querySelector('body').classList.add('transparent-search');
                        document.querySelector('body').classList.remove('header-force-bg');
                    } else {
                        document.querySelector('body').classList.add('header-force-bg');
                    }
                }
                
                setTimeout(() => {
                    this.searchOverlayFadeOut = false;
                    if(!this.showMobileMenu){
                        this.hideSearch();
                    }
                     this.setHeaderSearchTopPosition();

                }, 500);
            },
            handleShowSearch() {
                if (!this.isHd && this.showMobileMenu) {
                    this.toggleMobileMenu();
                }
                this.showSearch = !this.showSearch;
                if (this.showSearch) {
                    document.body.classList.add('search-active');
                    this.setHeaderSearchActiveTopPosition();
                    $360.disableScroll();
                    document.querySelector('body').classList.add('header-force-bg');
                    if (this.searchTerm === '' && this.searchHistory.length > 0) {
                        this.searchRecent = true;
                    }
                    this.$nextTick(() => {
                        const focusSearch = () => {
                            const searchInput = this.$refs['search-input'] || document.querySelector('#search');
                            if (searchInput) {
                                searchInput.focus();
                                if (searchInput.value) {
                                    searchInput.setSelectionRange(searchInput.value.length, searchInput.value.length);
                                }
                            } else {
                                setTimeout(focusSearch, 100);
                            }
                        };
                        
                        setTimeout(focusSearch, 300);
                    });
                    document.querySelector('body').classList.remove('transparent-search');
                } else {
                    this.clearSearch(false, true);
                }
            },
            hideSearch() {
                document.body.classList.remove('search-active');
                $360.enableScroll();
                if (!document.body.classList.contains('scrolled') && !this.showMobileMenu) {
                    document.body.classList.remove('header-force-bg');
                }
            },
            handleSearchFocus() {
                this.searchFocus = true;
                $360.disableScroll();
                if (this.searchTerm === '' && this.searchHistory.length > 0) {
                    this.searchRecent = true;
                }
            },
            saveSearch() {
                if (!this.searchTerm) return;
                
                if (!this.searchHistory.includes(this.searchTerm)) {
                    this.searchHistory.unshift(this.searchTerm);
                    if (this.searchHistory.length > 5) {
                        this.searchHistory.pop();
                    }
                    localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
                }
            },
            clearSearchHistory() {
                this.searchHistory = [];
                localStorage.removeItem('searchHistory');
            },
            handleSearchSubmit(e) {
                e.preventDefault();
                if (!this.searchTerm) return;
                this.saveSearch();
                e.target.submit();
            },
            handleHoverMenuProductBanner(number) {
                if (!this.activeSubmenu) return;

                const selector = `.menu-dropdown[data-index="${this.activeSubmenu}"] .each-banner-product`;
                const banners = document.querySelectorAll(selector);

                if (!banners.length) return;

                banners.forEach(banner => {
                    banner.style.display = banner.dataset.num === String(number) ? 'flex' : 'none';
                });
            }
        }
    }).mount('#header-con');
});
