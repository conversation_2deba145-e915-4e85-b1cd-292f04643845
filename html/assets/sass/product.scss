@import "helpers/functions";
@import "helpers/config";
@import "helpers/mixins";

@import "base/breakpoints";

// .product-top-section {
// 	.right-side {
// 		.product-info {
// 			.product-vendor {
// 				margin-bottom: 5px;
			
// 				@include on(tablet) {
// 					margin-bottom: 7px;
// 				}
// 			}

// 			.product-title {
// 				margin-bottom: 5px;

// 				@include on(tablet) {
// 					margin-bottom: 3px;
// 				}
// 			}

// 			.installment-pay-text {
// 				button {
// 					svg g path {
// 						fill: var(--grey);
// 					}
// 				}
				
// 			}

// 			.product-features {
// 				overflow-x: auto;
// 				display: flex;
// 				width: 100vw;
// 				margin-left: var(--container_padding_min);
// 				padding-left: var(--container_padding);
// 				padding-right: var(--container_padding);

// 				@include on (tablet){
// 					margin-left: var(--container_padding_min_t);
// 					padding-left: var(--container_padding_t);
// 					padding-right: var(--container_padding_t);
// 				}

// 				@include on (desktop){
// 					flex-wrap: wrap;
// 					width: auto;
// 					margin-left: initial;
// 					padding-left: initial;
// 					padding-right: initial;
// 				}

// 				> * {
// 					flex: none;
// 					@include on (desktop){
// 						flex: initial;
// 					}
// 				}
// 			}

// 			.okendo-review {
// 				svg {
// 					height: 12px;
// 				}

// 				.oke-sr-count {
// 					font-family: 'Avenir', sans-serif !important;
// 					letter-spacing: 0px !important;
// 					font-weight: 400 !important;
// 					font-size: 12px !important;
// 					font-size: 0.75rem !important;
// 					line-height: 1.2 !important;
					
// 					@include on (tablet){
// 						font-size: 12px !important;
// 						letter-spacing: 0px !important;
// 						font-size: 0.75rem !important;
// 						line-height: 1.2 !important;
// 					}

// 					@include on (desktop){
// 						font-size: 12px !important;
// 						letter-spacing: 0px !important;
// 						font-size: 0.75rem !important;
// 						line-height: 1.2 !important;
// 					}

// 					@include on (hd){
// 						font-size: 12px !important;
// 						font-size: 0.75rem !important;
// 						line-height: 1.2 !important;
// 						letter-spacing: 0px !important;
// 					}

// 					@include on (hhd){
// 						font-size: 13px !important;
// 						font-size: 0.8125rem !important;
// 						line-height: 1.2 !important;
// 						letter-spacing: 0px !important;
// 					}
// 				}
// 			}
// 		}
// 		.product-form {
// 			.global-form-qty {
// 				width: 90px;
// 				height: 54px;
// 			}

// 			#atc-btn {
// 				height: 56px;
// 			}

// 			.wishlist-btn {
// 				width: 56px;
// 				height: 56px;
// 				display: flex;
// 				align-items: center;
// 				justify-content: center;
// 				border-radius: 50%;
// 				background: var(--white) !important;
// 				border: 1px solid var(--border) !important;
// 				padding: initial !important;

// 				&.swym-added {
// 					opacity: initial !important;

// 					svg path {
// 						stroke: var(--orange) !important;
// 						fill: var(--orange) !important;
// 					}
// 				}
// 			}

// 			.loyalty-el {
// 				.line-el {
// 					height: 5px;
// 				}
// 			}

// 			.add-on {
// 				.each {
// 					margin-bottom: 15px;

// 					&:last-child {
// 						margin-bottom: 0;
// 					}
// 				}
// 			}

// 			.share-el {
// 				.each {
// 					width: 40px;
// 					height: 40px;
// 				}

// 				.copied {
// 					opacity: 0;
// 					visibility: hidden;
// 					transition: all 0.5s linear;

// 					&.active {
// 						opacity: 1;
// 						visibility: visible;
// 					}
// 				}
// 			}
// 		}

// 		.pdpr-options {
// 			.option-wrapper {
// 				.color-option {
// 					border-radius: 17px;
// 					padding: 6px 12px;
// 					border: 1px solid var(--border);
// 					transition: all 0.2s ease;
				
// 					&:has(input:checked) {
// 						border: 1px solid var(--black);
// 					}

// 					.color-circle {
// 						width: 18px;
// 						height: 18px;
// 						border-radius: 50%;
// 					}
// 				}

// 				.global-select-div {
// 					.text {
// 						padding-right: 15px;
// 					}

// 					.options {
// 						width: 100%;
// 						left: auto;
// 						right: 0;

// 						.option {
// 							width: auto;
// 							min-width: 100%;
// 						}
// 					}
// 				}

// 				.vpi-text {
// 					button {
// 						width: 14px;
// 						height: 14px;
// 					}
// 				}
// 			}
// 		}
// 	}
// }

// .sticky-atc {
// 	position: fixed;
// 	top: -150px;
// 	left: 0;
//     transition: top .2s linear;

// 	&.active {
// 		top: 0;
// 	}

// 	.left-side {
// 		.image-con {
// 			width: 80px;
// 		}
// 	}

// 	.right-side {
// 		button {
// 			padding: 10px 25px;

// 			@include on(desktop) {
// 				padding: 15px 45px;
// 			}
// 		}
// 	}
// }

// .vpi-popup,
// .casa-dollars-popup {
//     position: fixed;
//     width: 90%;
//     top: 50%;
//     left: 50%;
//     transform: translate(-50%, -50%);

// 	@include on(tablet) {
//         width: 500px;
// 	}

// 	@include on(desktop) {
//         width: 550px;
// 	}

// 	.close-btn {
// 		width: 15px;
// 		height: 15px;
// 	}
// }

// .product-card-2 {
//     height: 90px;

//     .imgbox {
//         width: 90px;
//         height: auto;
//     }

//     .img-wrp {
//         width: 83px;
//     }

//     .atcbox {
//         width: 30px;
//         height: auto;
//     }

//     .infobox {
//         width: calc(100% - 150px);
//     }
// }
