// ==========================================================================
// Base – Helpers
// ==========================================================================

// General Resets
.left{ float: left; }
.right{ float: right; }
.no-float { float: none; }
.no-bg { background: transparent; }
.no-border { border: 0; }
.vertical-top{ vertical-align: top; }
.cursor{ cursor:pointer; }
.overflow { overflow: hidden; }
.pb-2 { padding-bottom: 0.125em; }
.mt-2 {	margin-top: 0.125rem; }
.ml-2 { margin-left: 0.125rem; }
.mr-2 { margin-right: 0.125rem; }
.transition{
	@include transition(all,0.3s,ease-in-out);
}
.dont-break {
  white-space: nowrap;
}
.block{display:block;}

.background-cover{
	background:{
		repeat:no-repeat;
		size:cover;
		position:center;
	}
}
// svg{ width: 100%; }

.striked {
	text-decoration: line-through;
}

.striked-price.striked {
}

.btn-checkout > * {
	pointer-events: none;
}