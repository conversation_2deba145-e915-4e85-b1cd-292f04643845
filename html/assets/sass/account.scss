@import "helpers/functions";
@import "helpers/config";
@import "helpers/mixins";

@import "base/breakpoints";

.account-links {
	transition: all .5s cubic-bezier(.25,.46,.45,.94);
}
.account-menu-box {
	overflow: auto;
	width: calc(100% + (2 * var(--container_padding)));
	margin-left: -$container_padding;
	margin-right: -$container_padding;
	padding-left: $container_padding;
	padding-right: $container_padding;

	@include on(tablet){
		width: calc(100% + (2 * var(--container_padding_t)));
		margin-left: var(--container_padding_min_t);
		margin-right: var(--container_padding_min_t);
		padding-left: var(--container_padding_t);
		padding-right: var(--container_padding_t);
	}

	@include on(desktop) {
		width: 100%;
		margin-left: 0;
		padding-left: 0;
	}
}

.account-menu-box::-webkit-scrollbar {
	display: none;
}

.account-menu {
	width: calc(100% + (2 * var(--container_padding)));
	white-space: nowrap;

	li::before {
		content: "";
		background-color: transparent;
		border-radius: 50%;
		margin-right: 5px;
		display: inline-block;
		width: 10px;
		height: 10px;
	}

	li.active::before {
		background-color: var(--orange);
	}

	li a {
		color: var(--black);
		opacity: 0.2;
	}

	li.active a,
	li:hover a {
		opacity: 1;
		font-weight: 900 !important;
	}

	@include on(tablet) {
		width: calc(100% + (2 * var(--container_padding_t)));
		
		li::before {
			margin-right: 10px;
		}			
	}

	@include on(desktop) {
		width: 100%;
		white-space: unset;

		li::before {
			margin-right: 10px;
		}			
	}
}

.account-menu-dropdown {
	width: calc(100% + 28px);
	margin-left: -14px;
	
	.global-select-div {
		border-radius: 0;
		padding-left: 20px;
		padding-right: 20px;

		.options {
			border-radius: 0;
			margin-top: 0;
		}

		&.active {
			border-color: var(--stroke);
		}

		@include on(tablet) {
			padding-left: 38px;
			padding-right: 38px;
		}
	}

	@include on(tablet) {
		width: calc(100% + 64px);
		margin-left: -32px;
	}
}
.order-wrapper {
	width: calc(100% + 40px);
	margin-left: -$container_padding;
	padding-left: 20px;
	padding-right: 20px;

	@include on(tablet) {
		width: calc(100% + 76px);
		margin-left: -38px;
		padding-left: 38px;
		padding-right: 38px;
	}
	@include on(desktop) {
		width: 100%;
		margin-left: 0;
		padding-left: 32px;
		padding-right: 32px;
	}
	
	.order-main-box {
		// margin-left: -$container_padding;
		// margin-right: -$container_padding;
		margin-left: 0;
		margin-right: 0;

		@include on(tablet) {
			// margin-left: 0;
			// margin-right: 0;
		}
		@include on(desktop) {
			// margin-left: 0;
			// margin-right: 0;
		}
	}
}

.ws-nowrap {
	white-space: nowrap;
}

.order-table {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    overflow-x: auto;

	&::-webkit-scrollbar {
		background: transparent;
	}

	@include on(tablet) {
		width: 100%;
		margin-left: 0;
		margin-right: 0;
		padding-left: 0;
		padding-right: 0;
	}

	@include on(desktop) {
		width: 100%;
		margin-left: 0;
		margin-right: 0;
		padding-left: 0;
		padding-right: 0;
	}

	table.data-list {
		width: auto;
		table-layout: fixed;
		margin-left: $container_padding;
		margin-right: $container_padding;
		border-spacing: 0;
		border-collapse: separate;
		border: 1px solid var(--border);
		min-width: calc(100% + 30px);
		// min-width: calc(100% - (2 * $container_padding));

		// & > thead > tr > td,
		& > tbody > tr:not(:last-child) > td {
			border-bottom: 1px solid var(--stroke);
		}

		& > tbody > tr > td a {
			color: var(--black);
		}

		@include on(tablet) {
			width: 100%;
			margin-left: 0;
			margin-right: 0;
			min-width: unset;
		}

		@include on(desktop) {
			width: 100%;
		}
	}

	table .number {
		width: 120px;

		@include on(tablet) {
			width: 25%;
		}

		@include on(desktop) {
			width: 25%;
		}
	}

	table .date {
		width: 130px;

		@include on(tablet) {
			width: 25%;
		}

		@include on(desktop) {
			width: 25%;
		}
	}

	table .total {
		width: 130px;

		@include on(tablet) {
			width: 25%;
		}

		@include on(desktop) {
			width: 25%;
		}
	}

	table .status {
		width: 75px;

		@include on(tablet) {
			width: 25%;
		}

		@include on(desktop) {
			width: 25%;
		}
	}
}

.orders {
	.order {
		.photo-info {
			.icon {
				width: 15px;
				height: 15px;
				border-radius: 50%;
				font-size: 10px;
			}
		}
	}
}

.form-edit {
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	right: -100%;
	overflow: hidden;
	transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);

	&.active {
		right: 0;
	}

	.btn-close {
        width: 24px;
        height: 24px;
    }

	@include on(tablet) {
		// margin: 15px;
		// border-radius: 10px;
		// max-height: calc(100vh - 30px);
		// box-shadow: 0 1px 10px #0000001a;
		width: 80%;
	}

	@include on(desktop) {
		width: 500px;
		right: -515px;
	}

	.form-address-box {
		overflow: auto;
		height: calc(100vh - 100px);

		@include on(tablet) {
			height: auto;
		}
	}
}
.account-blob {
	position: absolute;
	width: 100%;
	height: 200px;
	z-index: -999999;
	overflow: hidden;
	pointer-events: none;

	@include on(desktop) {
		height: 100%;
	}

	&::after {
		content: "";
        position: absolute;
        height: 500px;
		width: 500px;
        aspect-ratio: 1 / 1;
        background: radial-gradient(50% 50% at 50% 50%, #f8c834 0, #fbe399cc 53.5%, #fdf1cc00);
        top: -275px;
        right: -295px;

		@include on(desktop) {
			top: -135px;
            left: -315px;
		}
		
		@include on(hd) {
			height: 650px;
			width: 650px;
			top: -160px;
            left: -420px;
		}
	}
}
