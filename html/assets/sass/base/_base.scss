* {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	outline:0;
}

html {
	-webkit-font-smoothing: antialiased;
	height:100%;
	scroll-behavior: smooth;
}

body {
	background-color: var(--beige-1);
	
	&.disable-scroll {
		position: relative;
		box-sizing: border-box;
		margin: 0;
		height: 100%;
		overflow: hidden;
	}

	&.no-bar {
		.mobile-menu-overlay {
			@include on(desktop) {
				top: 67px;
			}
		}

		.sticky-below-header,
		.global-content-top-margin {
			margin-top: 58px;

			@include on(desktop) {
				margin-top: 126px;
			}
		}

		.shopify-policy__container {
			// margin-top: 51px !important;
			// margin-bottom: 51px !important;
	
			@include on(desktop) {
				// margin-top: 69px !important;
				// margin-bottom: 69px !important;
			}
		}
	
		.sticky-below-header,
		.menu-dropdown-wrapper {
			top: 0;
		}
	}

	&.transparent-header {
		&.header-force-bg {
			.site-header {
				background-color: var(--white);
				border-bottom: 1px solid var(--border);
	
				.header-con__inner {
					.header-left {
						.header-nav {
							ul {
								li {
									.header-link {
										color: var(--black);
	
										.icon {
											svg {
												path {
													stroke: var(--black);
												}
											}
										}
									}
								}
							}
						}
					}
	
					.header-right {
						.auth-btn {
							border-color: var(--black);
							color: var(--black);
	
							&:hover {
								color: var(--black);
								background-color: var(--black);
							}
						}
					} 

					.icon svg {
						path,
						line {
							stroke: var(--black);
						}
					} 
				} 
			}
			&.transparent-search {
				.site-header {
					background-color: transparent;
					border-bottom: 1px solid transparent;
					.header-con__inner {
						.header-left {
							.header-nav {
								ul {
									li {
										.header-link {
											color: var(--black);
		
											.icon {
												svg {
													path {
														stroke: var(--black);
													}
												}
											}
										}
									}
								}
							}
						}
		
						.header-right {
							.auth-btn {
								border-color: var(--black);
								color: var(--black);
		
								&:hover {
									color: var(--black);
									background-color: var(--black);
								}
							}
						} 
	
						.icon svg {
							path,
							line {
								stroke: var(--black);
							}
						} 
					} 
					
				}
			}
		}

		&.sticky-header {
			.global-content-top-margin {
				margin-top: 0;
			}
		}

		.site-header {
			background-color: transparent;
			border-bottom: 1px solid transparent;

			.header-con__inner {
				.header-left {
					.header-nav {
						ul {
							li {
								.header-link {
									color: var(--black);

									.icon {
										svg {
											path {
												stroke: var(--black);
											}
										}
									}
								}
							}
						}
					}
				}

				.header-right {
					.auth-btn {
						border-color: var(--black);
						color: var(--black);

						&:hover {
							color: var(--black);
							background-color: var(--black);
						}
					}					
				} 

				.icon svg {
					path,
					line {
						stroke: var(--black);
					}
				} 
			} 
		}

		.global-content-top-margin {
			margin-top: 39px;
		}
	}

	&.sticky-header {
		.site-header {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
		}

		.global-content-top-margin {
			margin-top: 80px;

			@include on(desktop) {
				margin-top: 105px;
			}
		}
	}

	&:not(.sticky-header) {
		.menu-dropdown-wrapper {
			position: absolute;
		}
	}

	&.search-active {
		.site-header {
			border-bottom: 1px solid transparent !important;

			@include on(desktop) {
				border-bottom: 1px solid var(--stroke) !important;
			}
		}
	}
}

a {
	text-decoration:none;
}

input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
	-webkit-appearance:none;
}

button, input, textarea {
	appearance: none;
	border: none;
	background: none;
	resize: none;
	padding: 0;
	margin: 0;
	border-radius: 0;

	&:active, &:focus {
		outline: none;
	}
}

textarea {
	vertical-align: top;
}

button, input[type=submit] {
	cursor: pointer;
	-webkit-appearance: none;
}

img, video, audio, table, embed, applet {
	max-width: 100%;
}

.p1 b, .p1 strong, .p2 b, .p2 strong {
	font-weight: 700;
}
.pointer {
	cursor: pointer;
}
