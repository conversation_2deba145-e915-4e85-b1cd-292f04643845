@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes rotator {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(270deg);
    }
}

@keyframes dash {
    0% {
        stroke-dashoffset: 280;
    }
    50% {
        stroke-dashoffset: 75;
        transform: rotate(135deg);
    }
    100% {
        stroke-dashoffset: 280;
        transform: rotate(450deg);
    }
}

//   Total cycle duration becomes 1s (wobble) + 5s (pause) = 6s.
@keyframes wobble {
    0% {
        -webkit-transform: translateX(0%);
        transform: translateX(0%);
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
    }
    2.5% { /* Original 15% * (1/6) */
        -webkit-transform: translateX(-32px) rotate(-10deg);
        transform: translateX(-32px) rotate(-10deg);
    }
    5% { 
        -webkit-transform: translateX(calc(32px / 2)) rotate(10deg);
        transform: translateX(calc(32px / 2)) rotate(10deg);
    }
    7.5% { 
        -webkit-transform: translateX(calc(-32px / 2)) rotate(calc(-10deg / 1.8));
        transform: translateX(calc(-32px / 2)) rotate(calc(-10deg / 1.8));
    }
    10% { 
        -webkit-transform: translateX(calc(32px / 3.3)) rotate(calc(10deg / 3));
        transform: translateX(calc(32px / 3.3)) rotate(calc(10deg / 3));
    }
    16.67% { 
        -webkit-transform: translateX(0%);
        transform: translateX(0%);
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
    }
    100% {
        -webkit-transform: translateX(0%);
        transform: translateX(0%);
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
    }
}
@keyframes wobble2 {
    0% { 
      transform: rotate(0deg);
    }
    9.375% { 
      transform: rotate(20deg);
    }
    18.75% { 
      transform: rotate(40deg);
    }
    28.125% { 
      transform: rotate(20deg);
    }
    37.5% { 
      transform: rotate(0deg);
    }
    37.5% { 
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(0deg);
    }
  }
