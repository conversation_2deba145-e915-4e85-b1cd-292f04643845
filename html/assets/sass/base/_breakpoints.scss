// Grid Containers
$tablet-container: 600px;
$desktop-container: 1024px;
$large-desktop-container: 1200px;
$hd-container: 1440px;
$hhd-container:	1920px;

$max-width: $hhd-container;

// Responsive Breakpoints
$tablet-breakpoint:  ($tablet-container);
$desktop-breakpoint: ($desktop-container);
$large-desktop-breakpoint: ($large-desktop-container);
$hd-breakpoint: ($hd-container);
$hhd-breakpoint: ($hhd-container);

$screen-sizes: (
	tiny: em(0),
	tablet: $tablet-container,
	desktop: $desktop-container,
	large-desktop: $large-desktop-container,
	hd: $hd-container,
	hhd: $hhd-container
);
