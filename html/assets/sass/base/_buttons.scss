button {
    position: relative;

    &.disabled,
    &:disabled {
        background-color: var(--border);
        color: var(--black);
        border-color: var(--border);
        cursor: not-allowed;

        &:hover {
            background-color: var(--border);
            color: var(--black);
            border-color: var(--border);
        }
    }
}

.btn-loading {
    transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    svg {
        animation: rotator 1.4s linear infinite;

        .path {
            stroke-dasharray: 280;
            stroke-dashoffset: 0;
            transform-origin: center;
            stroke: var(--dark-blue);
            animation: dash 1.4s ease-in-out infinite;
        }
    }
}

.dark-btn-loading {
    .btn-loading {
        svg {
            .path {
                stroke: var(--light-grey);
            }
        }
    }
}

.btn1 {
    &:hover {
        .btn-loading {
            svg {
                .path {
                    stroke: var(--light-grey);
                }
            }
        }
    }
}

.btn3 {
    background-color: rgba(255, 255, 255, 0.7) !important;
    border-color: rgba(255, 255, 255, 0.7) !important;

    &:hover {
        background-color: rgba(255, 255, 255, 0.7) !important;
        border-color: var(--black) !important;
    }
}