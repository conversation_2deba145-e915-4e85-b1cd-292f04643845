.bold-400 {
	font-weight: 400 !important;
}

.bold-500 {
	font-weight: 500 !important;
}

.bold-600 {
	font-weight: 600 !important;
}

.bold-700 {
	font-weight: 700 !important;
}

.bold-900 {
	font-weight: 900 !important;
}

.lh-0 { 
	line-height: 0 !important; 
}

.lh-1 { 
	line-height: 1 !important; 
}

.ls-0point5 {
	letter-spacing: 0.5px !important;
}

.ls-0point6 {
	letter-spacing: 0.6px !important;
}

.ls-0point7 {
	letter-spacing: 0.7px !important;
}

.ls-1 {
	letter-spacing: 1px !important;
}

.ls-1point5 {
	letter-spacing: 1.5px !important;
}

.ls-2 {
	letter-spacing: 2px !important;
}

.underline{ text-decoration: underline; text-underline-offset: 1px; }
.uppercase {text-transform: uppercase;}
.capitalize {text-transform: capitalize;}
.rte{
	&.c-brown {
		ul {
			li {
				&::before {
					background-color: var(--brown);
				}
			}
		}
	}

	&.short-spacing {
		p,
		ul,
		ol {
			padding-bottom: 5px;
		}
	}

	&.medium-spacing {
		p,
		ul,
		ol {
			padding-bottom: 7px;
		}
	}

	a {
		color: var(--link-grey);
		border-bottom: initial;
		text-decoration: underline;
		text-underline-offset: 5px;
		transition: all .2s linear;
	}

	em{
		font:{
			style:italic;
		}
	}
	
	p{
		padding:{
			bottom: rem(10);
		}

		&:last-child{
			padding:{
				bottom: 0;
			}
		}
	}

	strong {
		font:{
			weight: 700;
		};
	}

	ul{
		list-style-type: none;
		padding-bottom: $rte-spacing;

		&:last-child {
			padding-bottom: 0;
		}

		li {
			position: relative;
			margin-bottom: rem(3);
			padding-left: rem(15);

			&:before {
                content: "";
                display: inline-block;
                width: 4px;
                height: 4px;
                border-radius: 50%;
                position: absolute;
                top: 6px;
                left: 0;
                background-color: var(--brown-80);
            }

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	ol{
		counter-reset: ol-counter;
		list-style-type: none;
		padding-bottom: $rte-spacing;

		&:last-child {
			padding-bottom: 0;
		}

		li {
			position: relative;
			margin-bottom: 15px;
			padding-left: 23px;

			&:before {
				counter-increment: ol-counter;
				content: counter(ol-counter) ". ";
				position: absolute;
				top: 0;
				left: 0;
			}

			&:last-child {
				margin-bottom: 0;
			}
		}
	}
}

.ul-disc-black li::before {
	content: "";
	border-radius: 50%;
	margin-right: 8px;
	margin-bottom: 2px;
	display: inline-block;
	width: 5px;
	height: 5px;
	background-color: var(--black);
}

.text-link1 {
	text-decoration: underline;
    color: var(--black);
    text-underline-offset: 5px;
    text-decoration-thickness: 1px;

	&:hover, &.active {
		color: var(--brown);
	}
}

.tl { text-align: left; }
.tr { text-align: right; }
.tc { text-align: center; }
.tj { text-align: justify; }

@include on(tablet) {
	.t-tl { text-align: left; }
  	.t-tr { text-align: right; }
  	.t-tc { text-align: center; }
  	.t-tj { text-align: justify; }
}

@include on(desktop) {
	.d-tl { text-align: left; }
  	.d-tr { text-align: right; }
  	.d-tc { text-align: center; }
  	.d-tj { text-align: justify; }
}

@include on(hd) {
	.hd-tl { text-align: left; }
  	.hd-tr { text-align: right; }
  	.hd-tc { text-align: center; }
  	.hd-tj { text-align: justify; }
}

@include on(hhd) {
	.hhd-tl { text-align: left; }
  	.hhd-tr { text-align: right; }
  	.hhd-tc { text-align: center; }
  	.hhd-tj { text-align: justify; }
}
