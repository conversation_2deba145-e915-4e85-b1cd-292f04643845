.link1,
.link2,
.link3,
.link4 {
    position: relative;

    &::before,
    &::after {
        position: absolute;
        top: 50%;
        right: 0;
        display: flex;
        align-items: center;
        transform: translateY(-50%);
        transition: opacity 0.3s linear;
    }

     &:hover {
        &::after {
            opacity: 0;
        }
        &::before {
            opacity: 1;
        }
    }

    &::after {
        opacity: 1;
    }

    &::before {
        opacity: 0;
    }
}

.link1 {
    padding-right: 13px;

    &:hover {
        opacity: 0.5;
    }

    &::after,
    &::before {
        width: 8px;
        height: 8px;
    }

    // This is the default icon
    &::after {
        content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" viewBox="0 0 11 11" fill="none"><path d="M5.22396 1.5L9 5.5L5.22396 9.5M8.35014 5.50006H1" stroke="%231F1F1F" stroke-width="1.5" stroke-linecap="square"/></svg>');
    }

    // This is the hover icon
    &::before {
        content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" viewBox="0 0 11 11" fill="none"><path d="M5.22396 1.5L9 5.5L5.22396 9.5M8.35014 5.50006H1" stroke="%231F1F1F" stroke-width="1.5" stroke-linecap="square"/></svg>');
    }
}

.link2 {
    padding-right: 13px;

    &:hover {
        opacity: 0.7;
    }

    &::after,
    &::before {
        width: 8px;
        height: 8px;
    }

    // This is the default icon
    &::after {
        content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" viewBox="0 0 11 11" fill="none"><path d="M5.22396 1.5L9 5.5L5.22396 9.5M8.35014 5.50006H1" stroke="white" stroke-width="1.5" stroke-linecap="square"/></svg>');
    }

    // This is the hover icon
    &::before {
        content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" viewBox="0 0 11 11" fill="none"><path d="M5.22396 1.5L9 5.5L5.22396 9.5M8.35014 5.50006H1" stroke="white" stroke-width="1.5" stroke-linecap="square"/></svg>')
    }
}

.link3 {
    padding-right: 13px;

    &:hover {
        opacity: 0.5;
    }

    &::after,
    &::before {
        width: 8px;
        height: 8px;
    }

    // This is the default icon
    &::after {
        content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none"><path d="M5.22396 1L9 5L5.22396 9M8.35014 5.00006H1" stroke="%231F1F1F" stroke-width="1.2" stroke-linecap="square"/></svg>');
    }

    // This is the hover icon
    &::before {
        content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none"><path d="M5.22396 1L9 5L5.22396 9M8.35014 5.00006H1" stroke="%231F1F1F" stroke-width="1.2" stroke-linecap="square"/></svg>')
    }
}

.link4 {
    padding-right: 13px;

    &:hover {
        opacity: 0.5;
    }
    
    &::after,
    &::before {
        width: 8px;
        height: 8px;
    }

    // This is the default icon
    &::after {
        content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none"><path d="M5.22396 1L9 5L5.22396 9M8.35014 5.00006H1" stroke="white" stroke-width="1.2" stroke-linecap="square"/></svg>')
    }

    // This is the hover icon
    &::before {
        content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none"><path d="M5.22396 1L9 5L5.22396 9M8.35014 5.00006H1" stroke="white" stroke-width="1.2" stroke-linecap="square"/></svg>')
    }
}