.page-overlay {
	&.header-cart-overlay {
		z-index: 99999999;
	}
}

// Sticky header style is on _base.scss body.sticky-header

.site-header {
	transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);

	&.submenu-active {
		// border-bottom: 1px solid var(--light-grey);
	}

	.announcement-bar {
		.ann-bar-inner {
			@include on(desktop) {
				padding: 0 56px;
			}

			.the-text {
				padding: 4px 0;
				white-space: nowrap;

				@include on(desktop) {
					padding: 6px 0;
				}

				&.bounding-text {
					transform: translateX(40%);
					animation: bounding-text 10s linear infinite;

					@include on(tablet) {
						transform: translateX(0);
						position: relative;
						animation: none;
					}
				}

				@keyframes bounding-text {
					0% {
						transform: translateX(40%);
					}
		
					100% {
						transform: translateX(-50%);
					}
				}

				@include on(tablet) {
					padding-left: 38px;
					padding-right: 38px;
				}

				@include on(desktop) {
					width: auto;
					padding-left: 0;
					padding-right: 0;
				}
			}
		}
	}

	.header-con {
		height: 48px;
		transition: background-color 500ms ease, opacity 500ms ease;

		@include on(desktop) {
			padding-left: 32px;
			padding-right: 32px;
			height: 52px;
		}

		@include on(hd) {
			padding-left: 40px;
			padding-right: 40px;
		}

		&__inner {
			display: flex;
			height: 100%;

			.header-left {
				display: flex;
				align-items: center;

				.header-mobile-menu {
					padding: 12px 4px 12px 16px;
					display: flex;

					@include on(tablet) {
						padding: 12px 4px 12px 20px;
					}

					@include on(desktop) {
						display: none;
					}

					.icon {
						width: 24px;
						height: 24px;
					}

				
				}

				.icon-search-btn {
					padding: 12px 12px 12px 4px;

					@include on(tablet) {
						padding: 12px 16px 12px 4px;
					}
				}

				.icon-search {
					transform: translateY(2.5px);
				}
			}


			.header-logo {
				width: 100px;
			}

			.header-nav {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);

				ul {
					li {
						.header-link {
							padding: 18px 0;
							transition: all 0.2s linear;

							&.active::after {
								content: "";
								position: absolute;
								bottom: 0;
								left: 0;
								width: 100%;
								height: 2px;
								background-color: var(--black);
							}
		
							>* {
								pointer-events: none;
							}
		
							.menu-label {
								padding: 3px 5px 2px 5px;
								margin-top: -10px;
								font-size: 9px;
							}
		
							.icon {
								width: 12px;
								height: 12px;
		
								svg {
									transition: all 0.2s linear;
								}
							}
		
							&.active {
								.icon {
									svg {
										transform: rotate(180deg);
										path {
											fill: var(--brown);
										}
									}
								}
								
							}
						}
					}
				}
			}

			.header-right {
				ul {
					li {
						button,
						a {
							padding: 12px 6px;

							@include on(desktop) {
								padding: 0;
							}
						}

						&:last-child {
							button,
							a {
								padding-right: 16px;

								@include on(tablet) {
									padding-right: 20px;
								}

								@include on(desktop) {
									padding-right: 0;
								}
							}
						}
					}

					.icon {
						width: 24px;
						height: 24px;
					}

					.acc-initial {
						background:  var(--orange);
						width: 24px;
						height: 24px;
						letter-spacing: -1px;
					}

					.header-cart-number {
						width: 16px;
						height: 16px;
						margin-left: 5px;
						border-radius: 50%;
						display: none;
						align-items: center;
						justify-content: center;

						&.has-item {
							display: flex;
						}

						.cart-count {
							transform: translateY(0.3px);
						}
					}
				}
			}
		}
	}
}

.header-search-form {
	width: 100%;
	position: fixed;
	top: 0;
	left: 0;
	opacity: 0;
	transition: opacity 0.3s ease;
	pointer-events: none;
	height: calc(100% - 33px);
	max-height: fit-content;
	overflow-y: auto;
	scrollbar-width: none;

	.search-input-wrapper {
		margin: 0 auto;
		max-width: 1920px;
		@include on(desktop) {
			height: 65px;
		}
		form {
			@include on(desktop) {
				margin-top: 2px;
			}		
		}
	}
	.search-dropdown-wrapper {
		.inner-left {
			.grid-flex.m-2 > * {
				width: calc(50% - 6px);
				@include on(desktop) {
					width: calc(50% - 8px);
				}
			}
		}
	}
 	.search-featured-products {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 12px;

		.product-card:nth-child(n+4) {
				@include on(desktop) {
					display: none;
				}
				@include on(large-desktop) {
					display: block;
				}
		}

		@include on(desktop) {
			grid-template-columns: repeat(3, 1fr);
			gap: 16px;
		}
		@include on(large-desktop) {
			grid-template-columns: repeat(4, 1fr);
		}
		.product-card {
			padding: 6px;

			@include on(desktop) {
				padding: 8px 8px 12px 8px;
			}
			.quick-action{
				@include on(desktop) {
					margin-top: 12px;
				}
			}	

			.text-con {
				.title-con { 
					margin-top: 8px;
				}
			}
		}
	}

	&.active {
		top: 138px;
		opacity: 1;
		pointer-events: auto;

		@include on(desktop) {
			top: 154px;
		}
	}

	.input {
		&:focus {
			color: var(--brown);
			&::-webkit-input-placeholder {
				color: var(--brown);
			}
			&::-moz-placeholder {
				color: var(--brown);
			}
			&:-ms-input-placeholder {
				color: var(--brown);
			}
			&:-moz-placeholder {
				color: var(--brown);
			}
		}
		&::-webkit-input-placeholder {
			color: var(--brown-40);
		}

		&::-moz-placeholder {
			color: var(--brown-40);
		}

		&:-ms-input-placeholder {
			color: var(--brown-40);
		}

		&:-moz-placeholder {
			color: var(--brown-40);
		}
	}
	.icon-search {
		top: 50%;
		transform: translateY(-50%);
	}

	.clear-search {
		top: 50%;
		transform: translateY(-50%);
		svg {
			width: 20px;
			height: 20px;

			@include on(desktop) {
				width: 24px;
				height: 24px;
			}
		}

	}
	.clear-all {
		text-underline-offset: 3px;
	}
}

#predictive-search-results {
	
	.results-wrapper {
		height: calc(100vh - 82.5px);
		height: calc(100dvh - 82.5px);
		overflow-x: scroll;
		-ms-overflow-style: none;
		scrollbar-width: none;

		@include on(desktop) {
			max-height: calc(100vh - 223px);
			height: fit-content;
		}

		&::-webkit-scrollbar {
			display: none;
		}
		.results-wrapper-top {
			&.grid-flex.d-minus-6 > * {
					width: calc(50% - 6px);
					@include on(desktop) {
						width: calc(50% - 8px);
					}
			}
		}
		.product-list {

			.each:nth-child(n+5) {
				@include on(desktop) {
					display: none;
				}
				@include on(hd) {
					display: block;
				}
			}

			.each:nth-child(n+6) {
				@include on(hd) {
					display: none;
				}
			}

			&.d-max-3 {
				.each:nth-child(n+4) {
					@include on(desktop) {
						display: none;
					}
					@include on(hd) {
						display: block;
					}

				}
				.each:nth-child(n+5) {
					@include on(hd) {
						display: none;
					}
				}
				.each {
					display: block;
				}
			}

			.product-card {

				@include on(desktop) {
					padding: 8px 8px 12px 8px;
				}
				

				.quick-action{
					@include on(desktop) {
						margin-top: 12px;
					}
				}
			}
		}
	}
	
}


.mobile-menu-wrapper {
	position: fixed;
	top: 0;
	left: -100%;
	width: 100%;
	transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
	overflow: hidden;
	transform: translate3d(0px, 0px, 0px);

	@include on(desktop) {
		width: 50%;
	}

	&.active {
		left: 0;
	}

	.icon-caret {
		width: 6px;
	}

	.mobile-menu-main {
		height: 100%;

		.menu-cont {
			height: 100%;
			overflow-y: auto;

			.mobile-menu {
				.parent {
					&:first-child {
						.main-menu-title {
							padding-top: 20px;
						}
					}

					&:last-child {
						.main-menu-title {
							padding-bottom: 16px;
						}
					}

					.main-menu-title {
						padding: 7px 16px;

						@include on(tablet) {
							padding: 10px 38px;
						}

						.image-mobile-menu {
							width: 42px;
							height: 42px;
							border-radius: 50%;
							overflow: hidden;
						}
					}
				}
			}

			.loyalty-message {
				.loyalty-image {
					width: 30px;
				}
			}
		}
	}

	.mobile-submenus {
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: -100vw;
		overflow-y: auto;
		overflow-x: hidden;
		transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);

		&.active {
			left: 0;
		}

		.submenu {
			ul {
				padding-top: 14px;
				padding-bottom: 14px;

				li {
					a {
						display: block;
						padding: 6px 16px;
					}
				}
			}
		}
	}
}

.menu-dropdown-wrapper {
	position: fixed;
	top: 88px;
	left: 0;
	width: 100%;
	height: calc(100vh - 88px);
	box-shadow: 0 5px 10px 0 rgba(0, 0, 0, .1);
	opacity: 0;
	visibility: hidden;
	z-index: 99999;
	transition: all 0.3s linear;
	overflow: hidden;
	margin-top: -0.5px;

	@include on(desktop) {
		top: 172px;
		height: auto;
	}

	@include on(hhd) {
		top: 163px;
	}

	&.main-menu {
		top: 109px;

		@include on(hhd) {
			top: 112px;
		}
	}

	&.fade-in {
		opacity: 1;
		visibility: visible;
	}

	.menu-dropdown {
		display: none;

		&.show {
			display: block;
			animation: fadeIn 0.5s;
		}

		.menu-group {
			ul {
				li {
					&:last-child {
						margin-bottom: 0;
					}
				}
			}
		}

		.banners_dropdown-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(10rem, 1fr));
		}

		.brands_dropdown-list-links-grid {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			grid-template-rows: repeat(8, auto);
			grid-auto-flow: column;
		}

		.left-side {
			flex: 1;
			.grid-flex.d-5.minus-16 > * {
				width: calc(20% - 16px);
			}
		}

		.right-side {
		}
	}
}

.simple-dropdown {
	display: none;
	position: fixed;
	z-index: 99999;

	&.show {
		display: block;
	}

	a:hover {
		background-color: var(--brown-20);
	}

	.sd-links {
		a {
			padding: 10px 24px;

			&:first-child {
				padding-top: 24px;
			}

			&:last-child {
				padding-bottom: 24px;
			}
		}
	}
}

.header-cart {
	transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: fixed;
	top: 0;
	right: 0;
	height: 100%;
	width: 100%;
	background: var(--white);
	z-index: 99999999;
	transform: translateX(100%);
	overflow: hidden;

	@include on(tablet) {
		border-radius: 8px 8px 8px 8px;
		width: 400px;
		height: calc(100% - 40px);
		top: 20px;
		right: -15px;
	}

	@include on(desktop) {
		height: calc(100% - 64px);
		top: 32px;
	}

	@include on(hd) {
		height: calc(100% - 80px);
		top: 40px;
	}

	&.active {
		right: 100%;

		@include on(tablet) {
			right: calc(400px + 20px);
		}

		@include on(desktop) {
			right: calc(400px + 32px);
		}

		@include on(hd) {
			right: calc(400px + 40px);
		}
	}

	.header-cart-loading {
		display: none;
		background-repeat: no-repeat;
		background-position: center;
		background-image: url("load.gif");
		background-size: 30px;
		transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);

		&.active {
			display: block;
		}
	}

	.header-cart-overlay {
		display: none;
		transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);

		&.active {
			display: block;
		}
	}

	.header-cart-note-overlay {
		display: none;
		transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);

		&.active {
			display: block;
		}
	}

	.header-cart-note {
		display: none;
		// transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);

		&.active {
			display: block;
		}
	}

	.header-cart-wrapper {
		.header-cart-title .title-wrapper {
			width: calc(100% - 15px);
			align-self: flex-start;

			@include on(desktop) {
				align-self: center;
			}
		}

		.header-cart-close {
			width: 16px;
			height: 16px;
			align-self: flex-end;

			svg {
				width: 16px;
				height: 16px;
			}
		}

		.empty-cart {		
			.top-wrp {
				height: calc(100% - (198px + 16px));

				@include on (tablet) {
					height: calc(100% - (187px + 16px));
				}

				@include on (desktop) {
					height: calc(100% - (187px + 20px));
				}

				.empty-ctn-wrapper {				
				}
			}
			
			.bot-wrp {

				.img-banner-item {
					width: 148px;
					height: auto;

					@include on (desktop) {
						width: 160px;
					}
				}

			}
		}

		.listing-cart-inner {
			height: calc(100vh - 106px);

			@include on(desktop) {
				height: calc(100vh - 118px);
			}

			@include on(hd) {
				height: calc(100vh - 120px);
			}

			.header-cart-bottom {
				position: fixed;
				left: 0;
				bottom: 0;
				opacity: 1;
			}
		}

		.header-cart-middle {
			overflow: auto;
			-ms-overflow-style: none;
			height: calc(100vh - 300px);

			@include on(desktop) {
				height: calc(100vh - 326px);
			}

			@include on(hd) {
				height: calc(100vh - 375px);
			}

			&::-webkit-scrollbar {
				width: 0;
				opacity: 0;
			}

			&:hover::-webkit-scrollbar {
				opacity: 1;
			}

			&::-webkit-scrollbar-track {
				background: var(--light-grey);
			}

			&::-webkit-scrollbar-thumb {
				background: transparent;
			}

			&:hover::-webkit-scrollbar-thumb {
				background: var(--grey);
				border-radius: 3px;
			}
		}
	}

	.cross-sell {
		.cs-buttons {
			display: none;
			height: 48px;

			@include on(tablet) {
				display: flex;
				justify-content: flex-end;
				align-items: center;
				gap: 5;
			}

			button {
				width: 48px;
				height: 48px;
				position: relative;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: var(--white);
				border-radius: 50%;
				top: unset;
				right: unset;
				left: unset;
				margin-top: 0;

				svg {
					width: 12px;
					height: 12px;
				}
			}
		}

		.swiper-item {
			background-color: var(--grey);

			@include on(desktop) {
				background-color: var(--white);
			}
		}
	}

	.cross-sell-pagination {
		height: 10px;
	}
}
