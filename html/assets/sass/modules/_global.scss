$overlayBg: rgba(0, 0, 0, 0.7);

body {
	background-image: url('bg-texture.jpg');
	background-position: center center;
	background-repeat: repeat;
	background-attachment: fixed;

	&.no-bg {
		background-image: none;
	}
}

a {
	cursor: pointer;
	outline: none;
	&.underline {
		text-decoration: underline;
		text-underline-offset: 3px;
		text-decoration-thickness: 1px;
	}
}

sup {
	vertical-align: top;
	font-size: small;
}

.global-content-top-margin {
	margin-top: 0;
}

.shopify-challenge__container {
	#g-recaptcha {
		padding-top: 20px;
		padding-bottom: 20px;
	}
}

.shopify-policy__container {
	// margin-top: 87px !important;
	// margin-bottom: 87px !important;

	@include on(desktop) {
		// margin-top: 109px !important;
		// margin-bottom: 109px !important;
	}
}

.shopify-policy__title {
	font-size: 40px;
	line-height: 1.1em;
	padding-top: 50px;
	padding-bottom: 50px;

	@include on(tablet) {
		font-size: 40px;
		padding-top: 50px;
		padding-bottom: 50px;
	}

	@include on(desktop) {
		font-size: 64px;
		padding-top: 70px;
		padding-bottom: 70px;
	}

	h1 {
		font-size: 40px;
		line-height: 1.1em;

		@include on(tablet) {
			font-size: 40px;
		}

		@include on(desktop) {
			font-size: 64px;
		}
	}
}

.shopify-policy__body {
	.rte {
		.title {
			font-weight: 900;
			font-size: 33px;
			line-height: 1.2;
			letter-spacing: 0px;

			@include on(tablet) {
				font-size: 36px;
			}

			@include on(desktop) {
				font-size: 40px;
			}

			@include on(hd) {
				font-size: 45px;
			}

			@include on(hhd) {
				font-size: 55px;
			}
		}

		a {
			font-weight: normal;
		}
	}
}

.sticky-below-header {
	position: sticky;
	top: 82px;
	left: 0;
	transition: all .5s cubic-bezier(.25,.46,.45,.94);

	@include on(desktop) {
		top: 104px;
	}
	&.--only-mobile {
		@include on(desktop) {
			position: static;
			top: 0;
		}
	}
}

.overlay {
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	background: #000000;
	z-index: 2;
	opacity: 0.35;
}

body {
	&.nooverflow {
		overflow: hidden !important;
	}
}

.global-image-wrapper {
	position: relative;
	width: 100%;
	height: 0;
	padding-bottom: 100%;
	background: #eee;

	&.no-bg {
		background: transparent;
	}

	.image {
		position: absolute;
		top: 0;
		left: 0;
		opacity: 0;
		transition: opacity .1s;

		&.loaded {
			opacity: 1;
		}
	}

	&.product {
		padding-bottom: 66%;
	}

	.video {
		position: absolute;
		top: 0;
		left: 0;
		opacity: 0;
		transition: opacity 1s;

		&.loaded {
			opacity: 1;
		}
	}
}

.page-overlay,
.global-popup,
.quick-buy-popup {
	display: block;
	opacity: 0;
	visibility: hidden;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: $overlayBg;
	z-index: 99999;

	&.active {
		opacity: 1;
		visibility: visible;
		animation: fadeIn 0.2s;
	}
	&.search-overlay {
	  &.active {
		opacity: 1;
		visibility: visible;
		animation: fadeIn 0.5s ease;
	  }
	  &.fade-out {
		animation: fadeOut 0.5s ease;
	  }
	}	

	&.fade-out {
		display: block;
		animation: fadeOut 0.2s;
	}
}

.page-loading {
	display: none;
	transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
	background-repeat: no-repeat;
	background-position: center;
	background-image: url("load.gif");
	background-size: 30px;

	&.active {
		display: block;
	}
}


.global-input {
	border-radius: 4px;
	border: 1px solid var(--border);
	display: flex;
	align-items: center;
	color: var(--black);
	&:has(input:focus),
	&:has(textarea:focus) {
		border-color: var(--border);
	}
	input, textarea, select {
		flex: auto;
		color: var(--black);
		// min-height: 49px;
		padding: 12.5px 16px;
		&::-webkit-input-placeholder { color: var(--grey) }
		&::-moz-placeholder { color: var(--grey) }
		&:-ms-input-placeholder { color: var(--grey) }
		&:-moz-placeholder { color: var(--grey) }
		&::placeholder { color: var(--grey) }
	}
	&__prefix {
		padding-left: 14px;
		margin-right: 2px;
	}
	&__suffix {
		padding-right: 14px;
		margin-left: 2px;
	}
	&__prefix,
	&__suffix {
		flex: none;
		display: flex;
		align-items: center;
	}
}


.global-input-error {
	border: 1px solid #AC1C1C !important;

	&::placeholder {
		color: #AC1C1C;
	}
}

.global-error-msg {
	// color: #912E21;
	color: #D91200;

	ul {
		li {
			padding-left: initial;

			&::before {
				display: none;
			}
		}
	}
}

select.global-input {
	appearance: none;
	// background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
	background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none"><path d="M5 7.5L10.0008 12.5L15 7.5" stroke="%23471F1D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>');
	background-repeat: no-repeat;
	background-position: right 10px center;
	// background-size: 1em;
	width: 100%;
	padding: 12px;
    // min-height: 49px;
	border: none;

	option {
        color: rgba(0,28,56,.6);
    }
}

select.global-input.active {
	background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none" style="transform: rotate(180deg)"><path d="M17 8.5L10 15.5L3 8.5" stroke="%23001C38" stroke-width="0.965517" stroke-linecap="round" stroke-linejoin="round"></path></svg>');
}

.global-flex {
	display: flex;

	&.flex-center {
		align-items: center;
		align-content: center;
		justify-content: center;
	}
}

.global-select-div {
	position: relative;
	border: 1px solid var(--border);
	padding: 12.5px 16px;
	border-radius: 4px;
	cursor: pointer;
	display: flex;
	justify-content: center;
	flex-direction: column;
	// color: var(--dark-blue);
	.label {
		color: var(--black);
		&.opacity-10 {
			opacity: 1;
		}
	}
	.text {
		color: var(--black);
	}
	&.active {
		border-color: var(--black);
		.icon {
			transform: translateY(-50%) rotate(180deg);
		}
		.options {
			display: block;
		}
	}
	&.selected-inline {
		.inner {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.text {
			margin-right: 17px;
		}
	}
	&.pdp-variety-select {
		.options {
			max-height: rem(265);
		}
	}
	.options {
		display: none;
		position: absolute;
		min-width: auto;
		width: 100%;
		top: 100%;
		left: 0;
		background: var(--white);
		outline: 1px solid var(--border);
		border-radius: 4px;
		margin-top: 12px;
		cursor: default;
		max-height: rem(200);
		overflow-y: auto;
		z-index: 2;
		transition: all 0.3s ease;
		li {
			padding: 11px 14px;
			@include on(desktop) {
				padding-top: 13px;
				padding-bottom: 13px;
			}
			cursor: pointer;
			&.active,
			&:hover {
				background-color: var(--background);
			}
			&.disable {
				background: #c3c3c3;
				opacity: 0.5;
				cursor: not-allowed;
			}
		}
	}

	select {
		display: none;
	}

	.icon {
		position: absolute;
		top: calc(50%);
		right: -4px;
		transform: translateY(-50%);
		width: 20px;
		height: 20px;
		display: flex;
		align-items: center;
		justify-content: center;

		svg {
			width: 100%;
			height: 100%;
		}
	}
}

.global-popup {
	.gp-inner {
		width: calc(100% - 50px);
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);

		@include on(tablet) {
			width: 500px;
		}

		.close {
			width: 20px;
			height: 20px;

			svg {
				width: 20px;
				height: 20px;
			}
		}
	}
}

.global-drawer {
	display: none;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: $overlayBg;
	z-index: 99999;
	opacity: 0;
	transition: opacity 0.4s 0s cubic-bezier(0.25, 1, 0.5, 1);

	&.active {
		display: block;
		animation: fadeIn 0.4s;
		opacity: 1;
	}

	&.fade-out {
		display: block;
		animation: fadeOut 0.4s;
	}

	.gd-inner {
		width: 100%;
		height: 100%;
		right: -100%;
		transition: all .75s 0s cubic-bezier(0.25, 1, 0.5, 1);

		@include on(tablet) {
			width: 500px;
			right: -500px;
		}

		@include on(desktop) {
			width: 600px;
			right: -600px;
		}

		&.active {
			right: 0;
		}

		.close {
			width: 14px;
			height: 14px;

			svg {
				width: 14px;
				height: 14px;
			}
		}
	}
}

.global-toggle,
.global-radio,
.global-checkbox {
	display: flex;
	align-items: center;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.global-toggle {
	input {
		& ~ .checkmark {
			background: var(--border);
			width: 36px;
			display: flex;
			margin-right: 12px;
			padding: 2px;
			transition: all .2s ease;
			&::after {
				content: "";
				width: 16px;
				height: 16px;
				background: var(--white);
				transform: translateX(0%);
				transition: all .2s ease;
			}
		}
		&:checked ~ .checkmark {
			background: var(--yellow);
			&::after {
				transform: translateX(100%);
				transition: all .2s ease;
			}
		}
	}
}

.global-checkbox,
.global-radio {
	input {
		& ~ .checkmark {
			border: 1px solid var(--border);
			height: 16px;
			width: 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 8px;
		}
		&:checked ~ .checkmark {
			border-color: var(--black);
			&::after {
				content: "";
				width: 8px;
				height: 8px;
				background: var(--black);
			}
		}
	}
}

.global-checkbox .checkmark {
	border-radius: 2px;
}
.global-checkbox .checkmark::after {
	border-radius: 1px;
}
.global-radio .checkmark,
.global-radio .checkmark::after,
.global-toggle .checkmark,
.global-toggle .checkmark::after {
	border-radius: 100px;
}

.image {
	display: block;
	width: 100%;
	height: auto;
}

.global-notification {
	display: none;
}

.global-form-qty {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background-color: var(--white);
	border: 1px solid var(--brown-20);
	border-radius: 6px;

	
	button {

		&:disabled {
			cursor: not-allowed;
			opacity: 0.5;
		}

		&.global-minus {
			padding-left: 12px;
			padding-right: 12px;
			padding-top: 16px;
			padding-bottom: 9px;

			@include on(desktop) {
				padding-top: 15px;
				padding-bottom: 9px;
			}
		}

		&.global-plus {
			padding-left: 12px;
			padding-right: 12px;
			padding-top: 15px;
			padding-bottom: 9px;


			@include on(desktop) {
				padding-top: 15px;
				padding-bottom: 9px;
			}
		}

		svg {
			width: 18px;
			height: 18px;
		}
	}

	.global-qty-text {
		width: 20px;
		height: fit-content;
		text-align: center;
		-moz-appearance: textfield !important;

		&::-webkit-inner-spin-button,
		&::-webkit-outer-spin-button {
			-webkit-appearance: none;
			margin: 0;
		}
	}
}

.dropdown-icon {
	width: 7px;
}

.global-dropdown-qty {
	font-variant-numeric: lining-nums;
	width: 80px;
	padding-left: 18px;
	padding-right: 38px;
	-webkit-appearance: none;
	background-color: transparent;
	background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI4LjgxMyIgaGVpZ2h0PSI0LjgxMSI+PHBhdGggZmlsbD0iIzFDMTgxMiIgZD0iTTQuNSA0LjgxMWEuNS41IDAgMCAxLS4zNDUtLjEzOGwtNC0zLjgxMUEuNS41IDAgMCAxIC4xMzguMTU1LjUuNSAwIDAgMSAuODQ1LjEzOGwzLjY0NiAzLjQ3NEw3Ljk1OS4xNDZhLjUuNSAwIDAgMSAuNzA3IDAgLjUuNSAwIDAgMSAwIC43MDdMNC44NTMgNC42NjRhLjUuNSAwIDAgMS0uMzUzLjE0N1oiLz48L3N2Zz4=);
	background-repeat: no-repeat;
	background-position: center right 18px;
	background-size: 7px;
}

.global-dropdown-qty.small {
	width: 60px;
	padding-left: 14px;
	padding-right: 27px;
	background-position: center right 14px;
	line-height: 2;
}

.global-pagination {
	display: flex;
	justify-content: center;
	column-gap: 8px;
	
	@include on(desktop) {
		column-gap: 12px;
	}
	
	&> * {
		height: 36px;
		width: 36px;
		border-radius: 4px;
		border: 1px solid var(--border);
		&:not([data-nav]) {
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.pagination-page {
		&.arrow {
			display: none;

			@include on(desktop) {
				display: flex;
				align-items: center;
				column-gap: 8px;
			}
		}
	}

	button {
		&[data-active] {
			border-color: var(--black);
			background: var(--black);
			color: var(--white);
		}

		&:not([data-active]) {
			border: none;
			width: auto;
			@include on(tablet) {
				width: 36px;
			}
		}
	}

	a {
		&[data-nav] {
			width: unset;
			padding: 0px 12px;
			span {
				display: flex;
				align-items: center;
				column-gap: 4px;
				height: 100%;
			}
		}

		&[data-disabled] {
			opacity: 0.3;
			pointer-events: none;
		}

		i {
			display: flex;
			align-items: center;
			width: 10px;
		}
	}
}

.product-card {
	height: 100%;
	container-name: product-card;
	container-type: inline-size;
	border-radius: 6px;
	background: none;

	@include on(desktop) {
		background: var(--background);
		border-radius: 8px;
	}

	&.no-rounded-on-mobile {
		border-radius: 0;
		
		@include on(desktop) {
			border-radius: 8px;
		}

		.galleries-wrp {
			border-radius: 0;
		}
	}

	.galleries-wrp {
		border-radius: 6px;
		overflow: hidden;
	}

	.top-con {
		z-index: 2;

		.tags-1 {
			z-index: 3;
		}
		
		.swiper-pagination {
			left: 12px;
			bottom: 12px;

			.swiper-pagination-bullet {
				width: 6px;
				height: 6px;
				background: var(--black);
				opacity: .3;

				&.swiper-pagination-bullet-active {
					opacity: 1;
				}
			}
		}
	}

	.bottom-con {
		z-index: 2;
		padding-top: 10px;

		@include on(desktop) {
			padding-top: 0;
			padding-bottom: 16px;
			padding-left: 16px;
			padding-right: 16px
		}
		.title-wrp {
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 1;
			align-self: stretch;
			overflow: hidden;
			text-overflow: ellipsis;			
		}
	}

	&.with-hover {
		.primary {
			z-index: 2;
			visibility: visible;
			opacity: 1;
		}

		.secondary {
			z-index: 1;
			visibility: hidden;
			opacity: 0;
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;

			.global-image-wrapper {
				padding: 0;
				height: 100%;

				.image {
					width: auto;
					object-fit: cover;
					height: 100%;					
				}
			}
		}

		@include on(desktop) {
			&:hover {
				.primary {
					visibility: hidden;
					opacity: 0;
				}

				.secondary {
					visibility: visible;
					opacity: 1;
				}

				.swatch {
					&.--pc {
						&.circle {
							&.selected {
								border-color: var(--white);
							}
						}
					}
				}
			}
		}
	}

	// .jdgm-preview-badge .jdgm-prev-badge__text {
	// 	color: var(--dark-grey);
	// }

	// .jdgm-preview-badge .jdgm-star.jdgm--on:before, 
	// .jdgm-preview-badge .jdgm-star.jdgm--half:before,
	// .jdgm-star.jdgm--off:before {
	// 	font-size: 13px;
	// }
}

.product-card-2 {
	height: 100%;
	.product-link {
		width: 100%;
		.img-wrp {
			width: 90px;
			height: auto;
		}

		.txt-wrp {
			width: calc(100% - 90px);
			.icon-wrp {
				svg {
					width: 14px;
					height: 14px;
				}
			}
		}
	}
}

.product-label,
.product-label2 {
	padding: 1px 6px;
	border-radius: 4px;
}

.content-image-slider-each {
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;

	& div {
		height: 100%;

		& img {
			height: 100%;
			object-fit: cover;
		}
	}
}

.btn-icon {
	display: inherit;
}

.global-social {
	a {
		svg {
			width: auto;
			height: 20px;
		}
	}
}

/*============== Custom Checkbox ==================*/
.cb-container {
	display: inline-block;
	position: relative;
	padding-left: 32px;
	cursor: pointer;
	user-select: none;
	line-height: 17px;

	// &:hover input ~ .checkmark {
	//     background-color: var(--comfort-orange);
	// }

	&.like-radio {
		padding-left: 24px;

		.checkmark {
			width: 14px;
			height: 14px;
			top: 3px;
			border-radius: 50%;
			border: 1px solid #BCBBBB;

			&::after {
				width: 8px;
				height: 8px;
				border: none;
				background-color: var(--brown);
				border-radius: 50%;
				left: 2px;
				top: 2px;
			}
		}
	}

	input {
		position: absolute;
		opacity: 0;
		cursor: pointer;
		height: 0;
		width: 0;

		&:checked {
			~.checkmark {
				background-color: var(--white);
				border: 1px solid var(--dark-blue);

				&:after {
					display: block;
				}
			}
		}
	}

	.text {
		display: block;
	}

	.checkmark {
		position: absolute;
		top: 50%;
		left: 0;
		height: 20px;
		width: 20px;
		padding: 4px;
		border-radius: 2px;
		border: 1px solid var(--stroke);
		background-color: var(--white);
		transform: translateY(-50%);

		&:after {
			content: "";
			position: absolute;
			display: none;
			top: 2px;
			left: 7px;
			width: 5px;
			height: 10px;
			border: solid var(--dark-blue);
			border-width: 0 1px 1px 0;
			transform: rotate(45deg);
		}
	}
}

/*============== End Custom Checkbox ==================*/

/*============== Custom Radio ==================*/
.radio-container {
	display: inline-block;
	position: relative;
	padding-left: 32px;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

	input {
		position: absolute;
		opacity: 0;
		cursor: pointer;

		&:checked~.checkmark {
			border: 1px solid var(--dark-blue);

			&:after {
				display: block;
			}
		}
	}

	.checkmark {
		position: absolute;
		top: 50%;
		left: 0;
		height: 20px;
		width: 20px;
		background-color: var(--white);
		border: 1px solid var(--stroke);
		border-radius: 50%;
		transform: translateY(-50%);

		&:after {
			content: "";
			position: absolute;
			display: none;
			top: 3px;
			left: 3px;
			width: 12px;
			height: 12px;
			border-radius: 50%;
			background: var(--dark-blue);
		}
	}
}

/*============== End Custom Radio ==================*/

/*============== Custom Radio with image ==================*/
.radio-container-img {
	cursor: pointer;
	display: inline-block !important;
	position: relative;
	border: 2px solid transparent;

	input[type="radio"] {
		position: absolute;
		opacity: 0;
		cursor: pointer;
	}
}

/*============== End Custom Radio with image ==================*/

/*======= end fabric icon =======*/

.payment-icons {
	column-gap: 10px;

	.each {
		width: 43px;

		@include on(desktop) {
			width: 46px;
		}
	}
}

.fade {
	opacity: 0;
	transition: all 0.3s linear;

	&.animated {
		opacity: 1;
	}
}

[v-cloak] {
	display: none !important;
}

.block {
	display: block;
}

.none {
	display: none;
}

.tag-each {
	padding: 5px 10px;
	line-height: 1;
}

.testimonial-each {
	.avatar {
		width: rem(100);
	}

	.text {
		width: calc(100% - 20px);
	}

	.global-play-button {
		position: absolute;
		top: rem(20);
		right: rem(20);
	}
}

.global-play-button {
	padding: rem(5) rem(10);
	display: flex;
	align-items: center;

	svg {
		height: 9px;
		width: 9px;
		margin-right: 6px;
	}
}

.oke-stars {
	svg {
		width: auto;
	}
}

#whatsapp-chat-widget {
	.wa-chat-bubble-close-button {
		svg {
			width: 12px;
			height: 13px;
		}
	}

	.wa-widget-send-button {
		svg {
			width: 50px;
			height: 26px;
		}
	}
}

.sticky-nav {
	flex: 1;
	transition: top .3s linear;

	@include on(desktop) {
		margin-left: 0;
		margin-right: 0;
	}

	ul {
		border-top: 1px solid #DBDBDB;
		border-bottom: 1px solid #DBDBDB;
		width: calc(100% + 50px);
		margin-left: -25px;
		overflow-x: scroll;
		white-space: nowrap;

		&::-webkit-scrollbar {
			display: none;
		}

		@include on (tablet) {
			width: calc(100% + 70px);
			margin-left: -35px;
		}

		@include on (desktop) {
			border-top: none;
			border-bottom: none;
			width: 100%;
			margin-left: 0;
			overflow-x: initial;
			white-space: initial;
		}

		li {
			cursor: pointer;
			display: inline-flex;
			padding-top: 20px;
			padding-bottom: 20px;
			margin-right: 30px;

			@include on (desktop) {
				display: flex;
				align-items: center;
				padding-top: 0;
				padding-bottom: 0;
			}

			&:hover,
			&.active {
				color: #2A7478;
				border-bottom: 2px solid #2A7478;

				a,
				p,
				span {
					color: #2A7478;
				}

				@include on(desktop) {
					border-bottom: initial;
				}
			}

			&.active {
				&::before {
					@include on(desktop) {
						content: '';
						border-top: 1px solid #2A7478;
						width: 10px;
						margin-right: 10px;
						height: 3.5px;
					}
				}
			}
		}
	}
}

.quick-buy-popup {
	.qb-inner {
		width: 100%;
		bottom: -100vh;
		right: 0;
		transition: all 0.2s linear;

		@include on(tablet) {
			width: 490px;
			height: 100%;
			top: 0;
			bottom: unset;
			right: -490px;
		}

		&.active {
			bottom: 0;

			@include on(tablet) {
				right: 0;
			}

			.qb-atc-wrapper {
				bottom: 0;

				@include on(tablet) {
					bottom: unset;
				}
			}
		}

		.close {
			width: 14px;
			height: 14px;

			svg {
				width: 14px;
				height: 14px;
			}
		}

		#quick-buy-content {
			height: 100%;
		}
	}

	.image-con {
		width: 130px;
	}

	.form-con {
		max-height: 400px;
		overflow: auto;

		@include on (tablet) {
			max-height: calc(100% - 233.33px);
		}

		.price {
			display: flex;

			.striked-price {
				margin-left: 5px;
			}
		}
	}

	.opt-list {
		input {
			display: none;

			&:checked~.text {
				border: 2px solid var(--black);
				font-weight: 500;
			}
		}

		.text {
			padding: 12px 23px;
			border: 1px solid var(--border);
			border-radius: 5px;
		}
	}

	.item-choosed {
		display: none;
		column-gap: 8px;

		&.active {
			display: flex;
		}
	}

	.atc-wrapper {
		width: 100%;
		z-index: 9;

		@include on(desktop) {
			position: relative;
			bottom: unset;
			left: unset;
			width: auto;
			box-shadow: none;
			z-index: 0;
		}

		.global-form-qty {
			width: 90px;
			height: 39.4px;

			@include on(tablet) {
				width: 105px;
				height: auto;
			}
		}

		.qb-atc-wrapper {
			width: 100%;
			position: fixed;
			bottom: -640px;
			left: 0;
			padding: 15px;
			background-color: var(--cream);
			border-top: 1px solid var(--light-grey);
			transition: all 0.2s linear;

			@include on(tablet) {
				width: auto;
				position: relative;
				bottom: unset;
				left: unset;
				padding: 0;
				background-color: transparent;
				border-top: none;
			}
		}
	}
}

.color-option {
	transition: all 0.2s ease;
	border-radius: 100%;
	padding: 0;
	border: 1px solid transparent;

	&:has(input:checked), &.active {
		border: 1px solid var(--black);
	}
	&.oos {
		.color-circle-wrp {
			.color-circle {
				position: relative;
				&::after {
					content: '';
					width: 1px;
					height: 100%;
					background-color: var(--white);
					position: absolute;
					top: 0;
					left: 50%;
					transform: rotate(45deg);
				}
			}
			
		}
	}

	.color-circle-wrp {
		width: 28px;
		height: 28px;
		padding: 2.5px;
		border-radius: 100%;
	}

	.color-circle {
		width: 100%;
		min-width: 21px;
		min-height: 21px;
		height: 100%;
		border-radius: 100%;
	}
}

.breadcrumb {
	max-width: 100%;
	overflow: hidden;

	@media screen and (min-width: 500px) {
		.breadcrumb {
			max-width: 430px;
		}
	}

	.bc-item {
		color: var(--grey);

		&.text {
			white-space: nowrap;

			&.last {
				color: var(--grey);
				overflow: hidden;
				-o-text-overflow: ellipsis;
				text-overflow: ellipsis;
			}
		}

		&.separator {
			color: var(--grey);
		}
	}
}


.article-card {
	.article-image {
		border-radius: 6px;
	}
	.article-text {
		.excerpt {
			overflow: hidden;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;

			p {
				padding-bottom: 0;
				
				&:last-child {
					display: none;
				}
			}
		}
	}
	.article-tag {
        padding: 2px 6px;
		border-radius: 10px;
    }
}

.password-field {
	.icon-password {
		display: inline-flex;
		align-items: center;
		height: 100%;
		right: 0;
		padding-left: 10px;
		padding-right: 16px;

		.icon-show,
		.icon-hide {
			display: flex;
		}

		.icon-show.hidden,
		.icon-hide.hidden {
			display: none;
		}
	}
}

.blob {
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: -999999;
	overflow-x: hidden;
	pointer-events: none;
}

.blob:after {
	content: "";
	position: absolute;
	height: inherit;
	aspect-ratio: 1/1;
	background: radial-gradient(
		50% 50% at 50% 50%,
		#F8C834 0%,
		rgba(251, 227, 153, 0.80) 53.5%,
		rgba(253, 241, 204, 0.00) 100%
	);
}

.video-wrapper {
	cursor: pointer;

	.video-btn {
		background-color: var(--white);
        padding: 12px 11px 12px 13px;
		border-radius: 50%;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	.icon-pause {
		display: none;
	}
	&.playing {
		.video-btn {
			opacity: 0;
            transition: opacity .3s ease-in-out;
		}
		.icon-play {
			display: none;
		}
		.icon-pause {
			display: flex;
		}

		&:hover {
			.video-btn {
				opacity: 1;
			}
		}
	}
}
.yt-container  {
	.yt-cover {
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
		z-index: 999999;
	}
	
	.yt-wrapper {
		position: relative;
		width: 100%;
		padding-bottom: 56.25%;

		.yt-iframe {
			position: absolute;
			top: 0;
			left: 0;
			height: 100%;
		}
	}

	.image-cover {
		&.hide {
			display: none;
		}

		.global-image-wrapper {
			height: 100%;

			img {
				height: 100%;
				object-fit: cover;
			}
		}
	}
	
	&:has(.image-cover) {
		.yt-wrapper {
			display: none;
		}
	}

	&:has(.image-cover.hide) {
		.yt-wrapper {
			display: block;
		}
	}
}
