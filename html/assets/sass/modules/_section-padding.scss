$sp_xs_m: 24px; 
$sp_xs_t: 24px; 
$sp_xs_d: 36px; 
$sp_xs_hd: 40px; 
$sp_xs_hhd: 56px;

$sp_sm_m: 36px; 
$sp_sm_t: 36px; 
$sp_sm_d: 48px; 
$sp_sm_hd: 60px; 
$sp_sm_hhd: 72px;

$sp_md_m: 48px; 
$sp_md_t: 48px; 
$sp_md_d: 60px; 
$sp_md_hd: 80px; 
$sp_md_hhd: 96px;

$sp_lg_m: 60px; 
$sp_lg_t: 60px; 
$sp_lg_d: 80px; 
$sp_lg_hd: 120px; 
$sp_lg_hhd: 148px;

.sp-xs {
    padding-top: $sp_xs_m;
    padding-bottom: $sp_xs_m;

    @include on(tablet) {
        padding-top: $sp_xs_t;
        padding-bottom: $sp_xs_t;
    }

    @include on(desktop) {
        padding-top: $sp_xs_d;
        padding-bottom: $sp_xs_d;
    }

    @include on(hd) {
        padding-top: $sp_xs_hd;
        padding-bottom: $sp_xs_hd;
    }

    @include on(hhd) {
        padding-top: $sp_xs_hhd;
        padding-bottom: $sp_xs_hhd;
    }
}

.spt-xs-half {
    padding-top: $sp_xs_m / 2;

    @include on(tablet) {
        padding-top: $sp_xs_t / 2;
    }

    @include on(desktop) {
        padding-top: $sp_xs_d / 2;
    }

    @include on(hd) {
        padding-top: $sp_xs_hd / 2;
    }

    @include on(hhd) {
        padding-top: $sp_xs_hhd / 2;
    }
}

.spb-xs-half {
    padding-bottom: $sp_xs_m / 2;

    @include on(tablet) {
        padding-bottom: $sp_xs_t / 2;
    }

    @include on(desktop) {
        padding-bottom: $sp_xs_d / 2;
    }

    @include on(hd) {
        padding-bottom: $sp_xs_hd / 2;
    }

    @include on(hhd) {
        padding-bottom: $sp_xs_hhd / 2;
    }
}

.sp-sm {
    padding-top: $sp_sm_m;
    padding-bottom: $sp_sm_m;

    @include on(tablet) {
        padding-top: $sp_sm_t;
        padding-bottom: $sp_sm_t;
    }

    @include on(desktop) {
        padding-top: $sp_sm_d;
        padding-bottom: $sp_sm_d;
    }

    @include on(hd) {
        padding-top: $sp_sm_hd;
        padding-bottom: $sp_sm_hd;
    }

    @include on(hhd) {
        padding-top: $sp_sm_hhd;
        padding-bottom: $sp_sm_hhd;
    }
}

.spt-sm-half {
    padding-top: $sp_sm_m / 2;

    @include on(tablet) {
        padding-top: $sp_sm_t / 2;
    }

    @include on(desktop) {
        padding-top: $sp_sm_d / 2;
    }

    @include on(hd) {
        padding-top: $sp_sm_hd / 2;
    }

    @include on(hhd) {
        padding-top: $sp_sm_hhd / 2;
    }
}

.spb-sm-half {
    padding-bottom: $sp_sm_m / 2;

    @include on(tablet) {
        padding-bottom: $sp_sm_t / 2;
    }

    @include on(desktop) {
        padding-bottom: $sp_sm_d / 2;
    }

    @include on(hd) {
        padding-bottom: $sp_sm_hd / 2;
    }

    @include on(hhd) {
        padding-bottom: $sp_sm_hhd / 2;
    }
}

.sp-md {
    padding-top: $sp_md_m;
    padding-bottom: $sp_md_m;

    @include on(tablet) {
        padding-top: $sp_md_t;
        padding-bottom: $sp_md_t;
    }

    @include on(desktop) {
        padding-top: $sp_md_d;
        padding-bottom: $sp_md_d;
    }

    @include on(hd) {
        padding-top: $sp_md_hd;
        padding-bottom: $sp_md_hd;
    }

    @include on(hhd) {
        padding-top: $sp_md_hhd;
        padding-bottom: $sp_md_hhd;
    }
}

.spt-md-half {
    padding-top: $sp_md_m / 2;

    @include on(tablet) {
        padding-top: $sp_md_t / 2;
    }

    @include on(desktop) {
        padding-top: $sp_md_d / 2;
    }

    @include on(hd) {
        padding-top: $sp_md_hd / 2;
    }

    @include on(hhd) {
        padding-top: $sp_md_hhd / 2;
    }
}

.spb-md-half {
    padding-bottom: $sp_md_m / 2;

    @include on(tablet) {
        padding-bottom: $sp_md_t / 2;
    }

    @include on(desktop) {
        padding-bottom: $sp_md_d / 2;
    }

    @include on(hd) {
        padding-bottom: $sp_md_hd / 2;
    }

    @include on(hhd) {
        padding-bottom: $sp_md_hhd / 2;
    }
}

.sp-lg {
    padding-top: $sp_lg_m;
    padding-bottom: $sp_lg_m;

    @include on(tablet) {
        padding-top: $sp_lg_t;
        padding-bottom: $sp_lg_t;
    }

    @include on(desktop) {
        padding-top: $sp_lg_d;
        padding-bottom: $sp_lg_d;
    }

    @include on(hd) {
        padding-top: $sp_lg_hd;
        padding-bottom: $sp_lg_hd;
    }

    @include on(hhd) {
        padding-top: $sp_lg_hhd;
        padding-bottom: $sp_lg_hhd;
    }
}
.spt-lg-half {
    padding-top: $sp_lg_m / 2;

    @include on(tablet) {
        padding-top: $sp_lg_t / 2;
    }

    @include on(desktop) {
        padding-top: $sp_lg_d / 2;
    }

    @include on(hd) {
        padding-top: $sp_lg_hd / 2;
    }

    @include on(hhd) {
        padding-top: $sp_lg_hhd / 2;
    }
}

.spb-lg-half {
    padding-bottom: $sp_lg_m / 2;

    @include on(tablet) {
        padding-bottom: $sp_lg_t / 2;
    }

    @include on(desktop) {
        padding-bottom: $sp_lg_d / 2;
    }

    @include on(hd) {
        padding-bottom: $sp_lg_hd / 2;
    }

    @include on(hhd) {
        padding-bottom: $sp_lg_hhd / 2;
    }
}

.spt-no {
    padding-top: 0;
}

.spb-no {
    padding-bottom: 0;
}