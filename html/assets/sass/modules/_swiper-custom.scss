

// .swiper-button-wrapper {
//     position: absolute;
//     width: 100%;
//     top: calc(278px / 2);
// }
.swiper-button-prev ,
.swiper-button-next {
    background-color: rgba(255, 255, 255, 0.70);
    border: 1px solid var(--border);
    border-radius: 4px;
    width: 48px;
    height: 48px;
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    &::after {
        display: none;
    }

    // svg {
    //     width: 14px;
    //     height: auto;
    //     object-fit: contain;
    //     transform-origin: center;
    // }
    
    &.swiper-button-disabled {
       opacity: 0.3;
    }
}

// .swiper-slide {
//     height: auto;
// }

// .swiper-button-next {
//     right: 20px;
// }

// .swiper-button-prev {
//     left: 20px;
// }

// .swiper-pagination-bullets {
//     &.static{
//         position:static;
//         transform:none;
//         text-align:left;
//     }
//     &.white {
//         &.swiper-pagination-horizontal {
            
//             .swiper-pagination-bullet {
//                 margin-right: 16px;
//                 background: var(--white);
//                 border: 0;

//                 &::before {
//                     content: "";
//                     display: inline-block;
//                     width: 16px;
//                     height: 16px;
//                     border: 1px solid transparent;
//                     border-radius: 100%;
//                     margin-left: -4px;
//                     margin-top: -4px;
//                 }

//                 &.swiper-pagination-bullet-active {
//                     background: var(--white);
//                 }

//                 &.swiper-pagination-bullet-active::before {
//                     border: 1px solid var(--white);
//                 }
//             }
//         }
//     }

//     &.swiper-pagination-horizontal {
//         bottom: 0;
//         line-height: 0;
//         display: flex;

//         .swiper-pagination-bullet {
//             width: 8px;
//             height: 8px;
//             background: none;
//             opacity: 1;
//             margin: initial;
// 			margin-left: initial;
// 			margin-right: 8px;
//             border: 1px solid var(--black);
    
//             &.swiper-pagination-bullet-active {
//                 background: var(--black);
//             }
//         }
//     }

//     .swiper-pagination-bullet {
//         border-radius: 4px;
//         cursor: pointer;
//     }
// }
// .swiper {
//     .note-overlay {
//         position: absolute;
//         bottom: 10%;
//         left: 10px;
//         z-index: 10;
//         @include on (tablet){
//             bottom: 10%;
//             left: 20px;
//         }
//         @include on(desktop) {
//             bottom: 10%;
//             left: 30px;
//         }
//     }
//     .note-overlay2 {
//         position: absolute;
//         bottom: 42%;
//         left: 10px;
//         z-index: 10;
//         @include on (tablet){
//             bottom: 20%;
//             left: 20px;
//         }
//         @include on(desktop) {
//             bottom: 13%;
//             left: 30px;
//         }
//     }
//     .note-overlay3 {
//         position: absolute;
//         bottom: 35%;
//         left: 10px;
//         z-index: 10;
//         @include on (tablet){
//             bottom: 16%;
//             left: 20px;
//         }
//         @include on(desktop) {
//             bottom: 10%;
//             left: 30px;
//         }
//     }
// }

// .swiper-scrollbar.swiper-scrollbar {
// 	position: initial;
// 	width: 100%;
// 	background: var(--border);
// 	height: 4px;
// 	border-radius: 5px;
// }

// .swiper-scrollbar.swiper-scrollbar .swiper-scrollbar-drag {
// 	background: var(--black);
// }

// .swiper-button-next {
//     right: 20px;
// }
// .swiper-button-prev {
//     left: 20px;
// }
// .swiper-button-next, 
// .swiper-button-prev {
//     border: 0;
//     width: 40px;
//     height: 40px;
//     border-radius: 100%;
//     background: var(--cream);
//     box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.20);
//     padding: 14px;

//     &::after {
//         content: "";
//     }
    
//     &.swiper-button-disabled {
//         opacity: 0 !important;
//     }

//     svg {
//         width: 12px;
//     }
// }

/* Style definition for numbered pagination used by: Full Width Banner Section */
// .swiper-pagination[data-type="numbered"] {
//     bottom: 0;
//     .swiper-pagination-bullet {
//         border-radius: initial;
//         height: initial;
//         width: initial;
//         min-width: 77px;
//         border-bottom: 3px solid rgba(255, 255, 255, 0.4);
//         padding-bottom: 10px;
//         background-color: transparent;
//         margin: 0 5px;
//         transition: all .1s linear;
//         opacity: 1;
//         &.swiper-pagination-bullet-active {
//             border-bottom-color: rgba(255, 255, 255, 1.0);
//         }
//         &:not(.swiper-pagination-bullet-active):hover {
//             border-bottom-color: rgba(255, 255, 255, 0.6);
//         }
//     }
// }

// .swiper-pagination {
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     bottom: 15px !important;

//     &.left-align {
//         justify-content: flex-start;
//         padding-left: $container_padding;

//         @include on(tablet) {
//             padding-left: $container_padding_t;
//         }
//     }

//     .swiper-pagination-bullet {
//         background-color: var(--dark-grey);
//         opacity: 1;

//         &:first-child {
//             margin-left: 0;
//         }

//         &:last-child {
//             margin-right: 0;
//         }

//         &.swiper-pagination-bullet-active {
//             background-color: var(--brown);
//         }
//     }
// }