// ==========================================================================
// Base – Mobile-First Grid
// ==========================================================================

// Micro Clearfix - http://nicolasgallagher.com/micro-clearfix-hack/
:root {
  --container_padding: #{$container_padding};
  --container_padding_t: #{$container_padding_t};
  --container_padding_d: #{$container_padding_d};
  --container_padding_hd: #{$container_padding_hd};
  --container_padding_hhd: #{$container_padding_hhd};
  --container_padding_min: -#{$container_padding};
  --container_padding_min_t: -#{$container_padding_t};
  --container_padding_min_d: -#{$container_padding_d};
  --container_padding_min_hd: -#{$container_padding_hd};
  --container_padding_min_hhd: -#{$container_padding_hhd};
  --gutter: #{$gutter};
  --gutter_t: #{$gutter_t};
  --gutter_d: #{$gutter_d};
  --gutter_hd: #{$gutter_hd};
  --gutter_hhd: #{$gutter_hhd};
}

.clear:before, .clear:after { content: " "; display: table; } .clear:after { clear: both; }
.row:before, .row:after { content: ""; display: table; } .row:after { clear: both; }
.row-m:before, .row-m:after { content: ""; display: table; } .row-m:after { clear: both; }

.row {
  position: relative;
  margin-left: -$gutter;
  margin-right: -$gutter;

  @include on (tablet){
    margin-left: -$gutter_t;
    margin-right: -$gutter_t;
  }

  @include on(desktop) {
    margin-left: -$gutter_d;
    margin-right: -$gutter_d;
  }

  @include on(hd) {
    margin-left: -$gutter_hd;
    margin-right: -$gutter_hd;
  }

  @include on(hhd) {
    margin-left: -$gutter_hhd;
    margin-right: -$gutter_hhd;
  }
}

// Mobile Container
.container {
  @extend %cf;
  max-width: 1920px;
  margin-left: auto;
  margin-right: auto;
  padding-left: $container_padding;
  padding-right: $container_padding;

  @include on(tablet){
    padding-left: $container_padding_t;
    padding-right: $container_padding_t;
  }

  @include on(desktop){
    padding-left: $container_padding_d;
    padding-right: $container_padding_d;
  }

  @media only screen and (min-width: 1200px) {
    padding-left: $container_padding_hd;
    padding-right: $container_padding_hd;
  }

  // @include on(hd){
  //   padding-left: $container_padding_hd;
  //   padding-right: $container_padding_hd;
  // }

  &--m {
    @include on(desktop) {
      max-width: (10 / 12) * 100%;
    }
  }

  &--s {
    @include on(desktop) {
      max-width: (8 / 12) * 100%;
    }
  }

  &--xs {
    @include on(desktop) {
      max-width: (6 / 12) * 100%;
    }
  }
}

.container-full {
  margin-left: auto;
  margin-right: auto;
  position:relative;
}

// Mobile-first Grid Columns - Global Rules
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12{
  @extend %cf;
  padding-left: $gutter;
  padding-right: $gutter;
  position: relative;
  float: left;

  @include on(tablet) {
    padding-left: $gutter_t;
    padding-right: $gutter_t;
  }

  @include on(desktop) {
    padding-left: $gutter_d;
    padding-right: $gutter_d;
  }

  @include on(hd) {
    padding-left: $gutter_hd;
    padding-right: $gutter_hd;
  }

  @include on(hhd) {
    padding-left: $gutter_hhd;
    padding-right: $gutter_hhd;
  }
}

// Mobile-first Grid
$columns: 12;
@for $i from 1 through $columns {
  .col-#{$i} {
    width: ($i / $columns) * 100%;
  }

  .grid-#{$i} {
    width: 100% / $i;
    display:inline-block;
    vertical-align: top;
    padding-left: $gutter;
    padding-right: $gutter;

    @include on(tablet) {
      padding-left: $gutter_t;
      padding-right: $gutter_t;
    }
  
    @include on(desktop) {
      padding-left: $gutter_d;
      padding-right: $gutter_d;
    }

    @include on(hd) {
      padding-left: $gutter_hd;
      padding-right: $gutter_hd;
    }

    @include on(hhd) {
      padding-left: $gutter_hhd;
      padding-right: $gutter_hhd;
    }
  }
}

.col-1per5 {
  width: 20%;
}

// Mobile Push Offsets
@for $i from 1 through $columns - 1 {
  .push-#{$i} {
    left: ($i / $columns) * 100%;
  }
}

// Mobile Pull Offsets
@for $i from 1 through $columns - 1 {
  .pull-#{$i} {
    left: -($i / $columns) * 100%;
  }
}

// Mobile Helpers
.n-mr{margin-right:-$container_padding;}
.n-ml{margin-left:-$container_padding;}
.hide-m { display: none; }
.show-m { display: block; }
.show-m-inline-block { display: inline-block; }
.show-m-inline { display: inline; }
.show-m-flex { display: flex; }
.no-pl{
  padding-left:0;
}
.no-pr{
  padding-right:0;
}
.ml-auto {
  margin-left: auto;
}
.mr-auto {
  margin-right: auto;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

@each $item in $margins{
  $i: rem($item);
  .mb-#{$item} {
    margin-bottom:$i;
  }
  .mr-#{$item} {
    margin-right:$i;
  }
  .ml-#{$item} {
    margin-left:$i;
  }
  .mt-#{$item} {
    margin-top:$i;
  }
  .m-#{$item} {
    margin:$i;
  }
  .pb-#{$item} {
    padding-bottom:$i;
  }
  .pt-#{$item} {
    padding-top:$i;
  }
  .pl-#{$item} {
    padding-left:$i;
  }
  .pr-#{$item} {
    padding-right:$i;
  }
  .p-#{$item} {
    padding:$i;
  }
  .colgap-#{$item} {
    column-gap:$i;
  }
  .rowgap-#{$item} {
    row-gap:$i;
  }
}

.mt-6 {
  margin-top: 0.375rem;
}

@each $bo in $borders{
  $i: rem($bo);
  .bl-#{$bo}{
    border-left:$i solid;
  }
  .br-#{$bo}{
    border-right:$i solid;
  }
  .bb-#{$bo}{
    border-bottom:$i solid;
  }
  .bt-#{$bo}{
    border-top:$i solid;
  }
  .b-#{$bo}{
    border:$i solid;
  }
}

// Tablet Grid
@media only screen and (min-width: $tablet-breakpoint) {
  // Tablet Grid Columns - Global Rules
  .col-t-1, .col-t-2, .col-t-3, .col-t-4, .col-t-5, .col-t-6, .col-t-7, .col-t-8, .col-t-9, .col-t-10, .col-t-11, .col-t-12{
    padding-left: $gutter_t;
    padding-right: $gutter_t;
    position: relative;
    float: left;

    @include on(desktop) {
      padding-left: $gutter_d;
      padding-right: $gutter_d;
    }

    @include on(hd) {
      padding-left: $gutter_hd;
      padding-right: $gutter_hd;
    }

    @include on(hhd) {
      padding-left: $gutter_hhd;
      padding-right: $gutter_hhd;
    }
  }

  // Tablet Grid
  $columns: 12;
  @for $i from 1 through $columns {
    .col-t-#{$i} {
      width: ($i / $columns) * 100%;
    }
    .grid-t-#{$i} {
      width: 100% / $i;
      display:inline-block;
      vertical-align: top;
      padding-left:$gutter_t;
      padding-right:$gutter_t;

      @include on(desktop) {
        padding-left: $gutter_d;
        padding-right: $gutter_d;
      }

      @include on(hd) {
        padding-left: $gutter_hd;
        padding-right: $gutter_hd;
      }

      @include on(hhd) {
        padding-left: $gutter_hhd;
        padding-right: $gutter_hhd;
      }
    }
  }

  .col-t-1per5 {
    width: 20%;
  }

  // Tablet Push Offsets
  @for $i from 1 through $columns - 1 {
    .push-t-#{$i} {
      left: ($i / $columns) * 100%;
    }
  }

  // Tablet Pull Offsets
  @for $i from 1 through $columns - 1 {
    .pull-t-#{$i} {
      left: -($i / $columns) * 100%;
    }
  }

  // Tablet Helpers
  .t-n-mr{margin-right:-$container_padding_t;}
  .t-n-ml{margin-left:-$container_padding_t;}
  .hide-t { display: none; }
  .show-t { display: block; }
  .show-t-inline-block { display: inline-block; }
  .show-t-inline { display: inline; }
  .show-t-flex { display: flex; }
  .t-no-padding { padding: 0; }
  .t-no-push, .t-no-pull { left: 0; }
  .t-no-pl{
    padding-left:0;
  }
  .t-no-pr{
    padding-right:0;
  }
  .t-ml-auto {
    margin-left: auto;
  }
  .t-mr-auto {
    margin-right: auto;
  }
  .t-mx-auto {
    margin-left: auto;
    margin-right: auto;
  }
  .t-pl-38 {
		padding-left: 2.375rem;
	}

	.t-pr-38 {
		padding-right: 2.375rem;
	}

  @each $item in $margins{
    $i: rem($item);
    .t-mb-#{$item} {
      margin-bottom:$i;
    }
    .t-mt-#{$item} {
      margin-top:$i;
    }
    .t-ml-#{$item} {
      margin-left:$i;
    }
    .t-mr-#{$item} {
      margin-right:$i;
    }
    .t-m-#{$item} {
      margin:$i;
    }
    .t-pb-#{$item} {
      padding-bottom:$i;
    }
    .t-pt-#{$item} {
      padding-top:$i;
    }
    .t-pl-#{$item} {
      padding-left:$i;
    }
    .t-pr-#{$item} {
      padding-right:$i;
    }
    .t-p-#{$item} {
      padding:$i;
    }
    .t-colgap-#{$item} {
      column-gap:$i;
    }
    .t-rowgap-#{$item} {
      row-gap:$i;
    }
  }

  @each $bo in $borders{
    $i: rem($bo);
    .t-bl-#{$bo}{
      border-left:$i solid;
    }
    .t-br-#{$bo}{
      border-right:$i solid;
    }
    .t-bb-#{$bo}{
      border-bottom:$i solid;
    }
    .t-bt-#{$bo}{
      border-top:$i solid;
    }
    .t-b-#{$bo}{
      border:$i solid;
    }
  }
}

// Desktop Grid
@media only screen and (min-width: $desktop-breakpoint) {
  // Desktop Grid Columns - Global Rules
  .col-d-1, .col-d-2, .col-d-3, .col-d-4, .col-d-5, .col-d-6, .col-d-7, .col-d-8, .col-d-9, .col-d-10, .col-d-11, .col-d-12{   
    padding-left: $gutter_d;
    padding-right: $gutter_d;
    position: relative;
    float: left;
  }

  // Desktop Grid
  $columns: 12;
  @for $i from 1 through $columns {
    .col-d-#{$i} {
      width: ($i / $columns) * 100%;
    }
    .grid-d-#{$i} {
      width: 100% / $i;
      display:inline-block;
      vertical-align: top;
      padding-left:$gutter_d;
      padding-right:$gutter_d;
    }
  }

  .col-d-1per5 {
    width: 20%;
  }

  // Desktop Push Offsets
  @for $i from 1 through $columns - 1 {
    .push-d-#{$i} {
      left: ($i / $columns) * 100%;
    }
  }

  // Desktop Pull Offsets
  @for $i from 1 through $columns - 1 {
    .pull-d-#{$i} {
      left: -($i / $columns) * 100%;
    }
  }

  // Desktop Helpers
  .row-d {
    @include on(desktop) {
      margin-left: -$gutter_d;
      margin-right: -$gutter_d;
    }
  }
  .d-n-mr{margin-right:-$container_padding_d;}
  .d-n-ml{margin-left:-$container_padding_d;}
  .hide-d { display: none; }
  .show-d { display: block; }
  .show-d-inline-block { display: inline-block; }
  .show-d-inline { display: inline; }
  .d-no-padding { padding: 0; }
  .d-no-push, .d-no-pull { left: 0; }
  .d-no-pl{
    padding-left:0;
  }
  .d-no-pr{
    padding-right:0;
  }
  .d-ml-auto {
    margin-left: auto;
  }
  .d-mr-auto {
    margin-right: auto;
  }
  .d-mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  @each $item in $margins{
    $i: rem($item);
    .d-mb-#{$item} {
      margin-bottom:$i;
    }
    .d-mt-#{$item} {
      margin-top:$i;
    }
    .d-ml-#{$item} {
      margin-left:$i;
    }
    .d-mr-#{$item} {
      margin-right:$i;
    }
    .d-m-#{$item} {
      margin:$i;
    }
    .d-pb-#{$item} {
      padding-bottom:$i;
    }
    .d-pt-#{$item} {
      padding-top:$i;
    }
    .d-pr-#{$item} {
      padding-right:$i;
    }
    .d-pl-#{$item} {
      padding-left:$i;
    }
    .d-p-#{$item} {
      padding:$i;
    }
    .d-colgap-#{$item} {
      column-gap:$i;
    }
    .d-rowgap-#{$item} {
      row-gap:$i;
    }
  }
  @each $bo in $borders{
    $i: rem($bo);
    .d-bl-#{$bo}{
      border-left:$i solid;
    }
    .d-br-#{$bo}{
      border-right:$i solid;
    }
    .d-bb-#{$bo}{
      border-bottom:$i solid;
    }
    .d-bt-#{$bo}{
      border-top:$i solid;
    }
    .d-b-#{$bo}{
      border:$i solid;
    }
  }
}


// HD Grid
@media only screen and (min-width: $hd-breakpoint) {
  // HD Grid Columns - Global Rules
  .col-hd-1, .col-hd-2, .col-hd-3, .col-hd-4, .col-hd-5, .col-hd-6, .col-hd-7, .col-hd-8, .col-hd-9, .col-hd-10, .col-hd-11, .col-hd-12{   
    padding-left: $gutter_hd;
    padding-right: $gutter_hd;
    position: relative;
    float: left;
  }

  // HD Grid
  $columns: 12;
  @for $i from 1 through $columns {
    .col-hd-#{$i} {
      width: ($i / $columns) * 100%;
    }
    .grid-hd-#{$i} {
      width: 100% / $i;
      display:inline-block;
      vertical-align: top;
      padding-left:$gutter_hd;
      padding-right:$gutter_hd;
    }
  }

  .col-hd-1per5 {
    width: 20%;
  }

  // HD Push Offsets
  @for $i from 1 through $columns - 1 {
    .push-hd-#{$i} {
      left: ($i / $columns) * 100%;
    }
  }

  // HD Pull Offsets
  @for $i from 1 through $columns - 1 {
    .pull-hd-#{$i} {
      left: -($i / $columns) * 100%;
    }
  }

  // HD Helpers
  .hd-n-mr{margin-right:-$container_padding_hd;}
  .hd-n-ml{margin-left:-$container_padding_hd;}
  .hide-hd { display: none; }
  .show-hd { display: block; }
  .show-hd-inline-block { display: inline-block; }
  .show-hd-inline { display: inline; }
  .hd-no-padding { padding: 0; }
  .hd-no-push, .hd-no-pull { left: 0; }
  .hd-no-pl{
    padding-left:0;
  }
  .hd-no-pr{
    padding-right:0;
  }
  .hd-ml-auto {
    margin-left: auto;
  }
  .hd-mr-auto {
    margin-right: auto;
  }
  .hd-mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  @each $item in $margins{
    $i: rem($item);
    .hd-mb-#{$item} {
      margin-bottom:$i;
    }
    .hd-mt-#{$item} {
      margin-top:$i;
    }
    .hd-ml-#{$item} {
      margin-left:$i;
    }
    .hd-mr-#{$item} {
      margin-right:$i;
    }
    .hd-pb-#{$item} {
      padding-bottom:$i;
    }
    .hd-pt-#{$item} {
      padding-top:$i;
    }
    .hd-pl-#{$item} {
      padding-left:$i;
    }
    .hd-pr-#{$item} {
      padding-right:$i;
    }
    .hd-p-#{$item} {
      padding:$i;
    }
    .hd-colgap-#{$item} {
      column-gap:$i;
    }
    .hd-rowgap-#{$item} {
      row-gap:$i;
    }
  }
  @each $bo in $borders{
    $i: rem($bo);
    .hd-bl-#{$bo}{
      border-left:$i solid;
    }
    .hd-br-#{$bo}{
      border-right:$i solid;
    }
    .hd-bb-#{$bo}{
      border-bottom:$i solid;
    }
    .hd-bt-#{$bo}{
      border-top:$i solid;
    }
  }
}

// Desktop Grid
@media only screen and (min-width: $hhd-breakpoint) {
  // hhd Grid Columns - Global Rules
  .col-hhd-1, .col-hhd-2, .col-hhd-3, .col-hhd-4, .col-hhd-5, .col-hhd-6, .col-hhd-7, .col-hhd-8, .col-hhd-9, .col-hhd-10, .col-hhd-11, .col-hhd-12{   
    padding-left: $gutter_hhd;
    padding-right: $gutter_hhd;
    position: relative;
    float: left;
  }

  // hhd Grid
  $columns: 12;
  @for $i from 1 through $columns {
    .col-hhd-#{$i} {
      width: ($i / $columns) * 100%;
    }
  }

  .col-hhd-1per5 {
    width: 20%;
  }

  // hhd Push Offsets
  @for $i from 1 through $columns - 1 {
    .push-hhd-#{$i} {
      left: ($i / $columns) * 100%;
    }
  }

  // hhd Pull Offsets
  @for $i from 1 through $columns - 1 {
    .pull-hhd-#{$i} {
      left: -($i / $columns) * 100%;
    }
  }

  // hhd Helpers
  .hide-hhd { display: none; }
  .show-hhd { display: block; }
  .show-hhd-inline-block { display: inline-block; }
  .show-hhd-inline { display: inline; }
  .hhd-no-padding { padding: 0; }
  .hhd-no-push, .hhd-no-pull { left: 0; }
  .hhd-no-pl{
    padding-left:0;
  }
  .hhd-no-pr{
    padding-right:0;
  }
  .hhd-ml-auto {
    margin-left: auto;
  }
  .hhd-mr-auto {
    margin-right: auto;
  }
  .hhd-mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  @each $item in $margins{
    $i: rem($item);
    .hhd-mb-#{$item} {
      margin-bottom:$i;
    }
    .hhd-mt-#{$item} {
      margin-top:$i;
    }
    .hhd-mr-#{$item} {
      margin-right:$i;
    }
    .hhd-ml-#{$item} {
      margin-left:$i;
    }
    .hhd-pb-#{$item} {
      padding-bottom:$i;
    }
    .hhd-pt-#{$item} {
      padding-top:$i;
    }
    .hhd-pl-#{$item} {
      padding-left:$i;
    }
    .hhd-pr-#{$item} {
      padding-right:$i;
    }
    .hhd-p-#{$item} {
      padding:$i;
    }
    .hhd-colgap-#{$item} {
      column-gap:$i;
    }
    .hhd-rowgap-#{$item} {
      row-gap:$i;
    }
  }
  @each $bo in $borders{
    $i: rem($bo);
    .hhd-bl-#{$bo}{
      border-left:$i solid;
    }
    .hhd-br-#{$bo}{
      border-right:$i solid;
    }
    .hhd-bb-#{$bo}{
      border-bottom:$i solid;
    }
    .hhd-bt-#{$bo}{
      border-top:$i solid;
    }
  }
}


//ADDON: full width
.container-max{
  .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, .col-t-1, .col-t-2, .col-t-3, .col-t-4, .col-t-5, .col-t-6, .col-t-7, .col-t-8, .col-t-9, .col-t-10, .col-t-11, .col-t-12, .col-d-1, .col-d-2, .col-d-3, .col-d-4, .col-d-5, .col-d-6, .col-d-7, .col-d-8, .col-d-9, .col-d-10, .col-d-11, .col-d-12, .col-hd-1, .col-hd-2, .col-hd-3, .col-hd-4, .col-hd-5, .col-hd-6, .col-hd-7, .col-hd-8, .col-hd-9, .col-hd-10, .col-hd-11, .col-hd-12{   
    padding-left: 0;
    padding-right: 0;
    float: left;
  }
}
