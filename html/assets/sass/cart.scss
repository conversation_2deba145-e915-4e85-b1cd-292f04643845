@import "helpers/functions";
@import "helpers/config";
@import "helpers/mixins";

@import "base/breakpoints";


.header-cart-items {
	container-name: header-cart-items;
	container-type: inline-size;

	.cart-item {
		transition: all 0.3s linear;

		.accordion {
			.icon {
				width: 8px;
				height: 8px;

				svg {
					width: 8px;
					height: 8px;

					path {
						stroke: var(--link-grey);
					}
				}
			}

		}

		.image-con {
			width: 80px;

			@include on(desktop) {
				width: 88px;
			}
		}

		.details-con {

			.item-plus-minus {
				container-name: item-plus-minus;
				container-type: inline-size;
			}
		}
	}
}

.cross-sell {
	.nav-wrapper {

		.swiper-btn-next,
		.swiper-btn-prev {
			width: 20px;
			height: 20px;
		}

		.swiper-btn-next.swiper-button-disabled,
		.swiper-btn-prev.swiper-button-disabled {
			opacity: .3;
		}
	}

}

.cart-loading {
	display: none;
	background-repeat: no-repeat;
	background-position: center;
	background-image: url("load.gif");
	background-size: 30px;
	transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);

	&.active {
		display: block;
	}
}