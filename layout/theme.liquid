<!doctype html>
<!--[if IE 9]> <html class="ie9 no-js" lang="{{ shop.locale }}"> <![endif]-->
<!--[if (gt IE 9)|!(IE)]><!--> <html class="no-js" lang="{{ shop.locale }}"> <!--<![endif]-->
<head>
  <link rel="preconnect" href="//cdn.shopify.com" />
  <link rel="dns-prefetch" href="//cdn.shopify.com" />
  <link rel="preconnect" href="//v.shopify.com" />
  <link rel="dns-prefetch" href="//v.shopify.com" />
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="format-detection" content="telephone=no">
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">
  <link rel="canonical" href="{{ canonical_url }}">
  {% if product.tags contains 'hidden' %}
    <meta name="robots" content="noindex, nofollow">
  {% endif %}
  {%- if settings.favicon != blank %}<link rel="shortcut icon" href="{{ settings.favicon | img_url:'100x' }}" type="image/png">{%- endif -%}
  {% assign page_title_new = page_title %}
  {%- if request.page_type == 'cart' -%}
	  {% assign page_title_new = 'cart.general.page_title' | t %}
  {%- endif -%}
  {%- capture seo_title -%}
    {{ page_title_new }}
    {%- if current_tags -%}
      {%- assign meta_tags = current_tags | join: ', ' %} &ndash; {{ 'general.meta.tags' | t: tags: meta_tags -}}
    {%- endif -%}
    {%- if current_page != 1 -%}
      &ndash; {{ 'general.meta.page' | t: page: current_page }}
    {%- endif -%}
    {%- unless page_title_new contains shop.name -%}
      &ndash; {{ shop.name }}
    {%- endunless -%}
  {%- endcapture -%}
  <title>{{ seo_title }}</title>
  <link rel="preload" href="{{ 'style.css' | asset_url }}" as="style">
  <link rel="preload" href="{{ 'vendor.min.js' | asset_url }}" as="script">
  {% if request.page_type == "product" %}
    <link rel="preload" href="{{ product.featured_image | image_url: width: "2000" }}" as="image">
  {% endif %}
  {%- if page_description -%}
    <meta name="description" content="{{ page_description | escape }}">
  {%- endif -%}
  {%- if settings.typography != blank -%}
    {{ settings.typography }}
  {%- endif -%}
  {{ 'style.css' | asset_url | stylesheet_tag }}

  {% render 'color-swatch-css' %}
  {% render '360shop-script' %}
  {% render 'script-head' %}

  {{ 'vendor.min.js' | asset_url | script_tag }}
  {%- render 'social-meta-tags' -%}
  {%- render 'json-ld' -%}
  {{ content_for_header }}
</head>

{%- assign is_exclude = false -%}
{%- if settings.header_sticky_disable_page_types contains request.page_type -%}
  {%- assign is_exclude = true -%}
{%- endif -%}

{%- assign header_class = '' -%}
{%- if is_exclude == false -%}
  {%- if settings.header_sticky -%}
    {%- assign header_class = header_class | append: ' sticky-header' -%}
  {%- endif -%}
  {%- if settings.header_hide_bar_on_scroll -%}
    {%- assign header_class = header_class | append: ' hide-bar-on-scroll' -%}
  {%- endif -%}
  {%- if request.page_type == 'product' -%}
    {%- assign header_class = header_class | append: ' product-page' -%}
  {%- endif -%}
  {% if settings.show_announcement == false %}
    {%- assign header_class = header_class | append: ' no-bar' -%}
  {% endif %}
{%- endif -%}

{%- assign trans_pages = settings.header_transparen_pages | newline_to_br | strip_newlines | split: '<br />' -%}
{%- for path in trans_pages -%}
  {%- if path == request.path -%}
    {%- assign header_class = header_class | append: ' transparent-header' -%}
    {%- break -%}
  {%- endif -%}
{%- endfor -%}

<body class="{{ header_class }}" {% if settings.body_bg_color != blank %}style="background-color: {{ settings.body_bg_color }};"{% endif %}>
  {% liquid
    render 'script-body'
    section 'typography-header'
    section 'typography-body'
    section 'colors'
    section 'buttons'
    section 'links'
  %}

  <div class="fixed top-0 left-0 w-full h-full bg-white page-loading opacity-5 zi-9">&nbsp;</div>
  <div class="page-overlay header-cart-overlay drawer-info-overlay" onclick="$360.hideHeaderCart()">&nbsp;</div>
  {% liquid
    assign has_cross_sel = false
    for num in (1..5)
      assign product_setting = 'cart_cross_sell_' | append: forloop.index
      assign product = settings[product_setting]
      if product != blank and cart.item_count > 0
        assign has_cross_sel = true
        break
      endif
    endfor
  %}
  
  <div class="header-cart {% if has_cross_sel == true %}flex column-reverse jc-between t-flex-row t-jc-start{% else %}no-cross-sell{% endif %} relative" id="header-cart"></div>
  {%- section 'header' -%}

  {% if canonical_url contains "challenge" %}
  <section class="pt-20 pb-20 global-content-top-margin t-pt-30 d-pt-50 t-pb-30 d-pb-50">
    <div class="container">
      {{ content_for_layout }}
    </div>
  </section>
  {% else %}
    {%- if request.path contains '/policies/' -%}
      <style>
        .shopify-policy__container {
          max-width: unset;
          padding-left: 0;
          padding-right: 0;
        }
        .shopify-policy__container .shopify-policy__title {
          padding: 0;
          text-transform: capitalize;
        }
        .shopify-policy__container .shopify-policy__body {
          padding-top: 48px;
        }

        .shopify-policy__container .shopify-policy__body .rte h2,
        .shopify-policy__container .shopify-policy__body .rte h5,
        .shopify-policy__container .shopify-policy__body .rte strong {
          margin-top: 16px;
          margin-bottom: 16px;
        }
        .shopify-policy__container .shopify-policy__body .rte strong {
          display: inline-block;
          font-family: formiga, sans-serif;
          font-weight: 800;
          font-size: 20px;
          line-height: 1.2;
          letter-spacing: 0px;
        }
        .shopify-policy__container .shopify-policy__body .rte h2:first-child,
        .shopify-policy__container .shopify-policy__body .rte h5:first-child,
        .shopify-policy__container .shopify-policy__body .rte strong:first-child {
          margin-top: 0;
        }
        .shopify-policy__container .shopify-policy__body .rte p,
        .shopify-policy__container .shopify-policy__body .rte ul {
          padding-bottom: 16px;
        }
        .shopify-policy__container .shopify-policy__body .rte p:last-child,
        .shopify-policy__container .shopify-policy__body .rte ul:last-child {
          padding-bottom: 0;
        }

        @media only screen and (min-width:600px) {
          .shopify-policy__container .shopify-policy__body .rte strong {
            font-size: 21px;
          }
        }
        @media only screen and (min-width:1024px) {
          .shopify-policy__container .shopify-policy__body {
            padding-top: 80px;
          }

          .shopify-policy__container .shopify-policy__body .rte h2,
          .shopify-policy__container .shopify-policy__body .rte h5,
          .shopify-policy__container .shopify-policy__body .rte strong {
            margin-top: 44px;
          }
          .shopify-policy__container .shopify-policy__body .rte strong {
            font-size: 23px;
          }
        }
        @media only screen and (min-width:1440px) {
          .shopify-policy__container .shopify-policy__body .rte strong {
            font-size: 24px;
          }
        }
        @media only screen and (min-width:1920px) {
          .shopify-policy__container .shopify-policy__body .rte strong {
            font-size: 25px;
          }
        }
      </style>

      <div class="relative global-content-top-margin clear">
        <div class="container pt-48 pb-48 d-pt-80 d-pb-80">
          <div class="row">
            <div class="col-12 col-d-8 push-d-2">
              {{ content_for_layout | replace: 'rte', 'rte p2' | replace: 'shopify-policy__title', 'shopify-policy__title rte' }}
            </div>
          </div>
        </div>
      </div>

      <script>
        function capitalizeEachWord(str) {
          // Check if the input is a string and not empty
          if (typeof str !== 'string' || str.trim() === '') {
              return ''; // Return an empty string for invalid input
          }

          // Split the string by spaces to get individual words
          const words = str.split(' ');

          // Map over each word to transform it
          const capitalizedWords = words.map(word => {
              // If the word is empty (e.g., multiple spaces), return it as is
              if (word.length === 0) {
                  return '';
              }
              // Convert the word to lowercase, then capitalize its first letter
              return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
          });

          // Join the transformed words back together with spaces
          return capitalizedWords.join(' ');
        }

        document.querySelectorAll(".shopify-policy__body strong").forEach((el) => {
          el.innerHTML = capitalizeEachWord(el.innerHTML);
        });
      </script>
    {%- else -%}
      {{ content_for_layout }}
    {%- endif -%}
  {% endif %}
  
  {% section 'running-text' %}
  {% section 'footer' %}
  {% render 'script-footer' %}
</body>
</html>
