<!doctype html>
<!--[if IE 9]> <html class="ie9 no-js" lang="{{ shop.locale }}"> <![endif]-->
<!--[if (gt IE 9)|!(IE)]><!--> <html class="no-js" lang="{{ shop.locale }}"> <!--<![endif]-->
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">
  <link rel="canonical" href="{{ canonical_url }}">
  {% if settings.favicon != blank %}<link rel="shortcut icon" href="{{ settings.favicon | img_url:'100x' }}" type="image/png">{% endif %}

  {% capture seo_title %}
    {{ page_title }}
    {% if current_tags %}
      {%- assign meta_tags = current_tags | join: ', ' %} &ndash; {{ 'general.meta.tags' | t: tags: meta_tags -}}
    {% endif %}
    {% if current_page != 1 %}
      &ndash; {{ 'general.meta.page' | t: page: current_page }}
    {% endif %}
    {% unless page_title contains shop.name %}
      &ndash; {{ shop.name }}
    {% endunless %}
  {% endcapture %}
  <title>{{ seo_title }}</title>
  <link rel="preconnect dns-prefetch" href="https://cdn.shopify.com" />
  <link rel="preconnect dns-prefetch" href="https://v.shopify.com" />
  {% if page_description %}
    <meta name="description" content="{{ page_description | escape }}">
  {% endif %}
  {% if settings.typography != blank %}
    {{ settings.typography }}
  {% endif %}
  {{ 'style.css' | asset_url | stylesheet_tag }}
  {% render 'social-meta-tags' %}
  <link rel="stylesheet" href="{{ 'password.css' | asset_url }}" type="text/css" media="print" onload="this.media='all';">
  {{ content_for_header }}
</head>

<body>
  {% section 'typography-header' %}
  {% section 'typography-body' %}
  {% section 'colors' %}
  {% section 'buttons' %}
  {% section 'links' %}
  <style>
  .password-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .password-page:before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
  }

  .password-details {
    position: relative;
    z-index: 10;
  }
  </style>
  {{ content_for_layout }}
</body>
</html>
