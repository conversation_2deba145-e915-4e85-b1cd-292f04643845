<div class="sticky account-links mb-8 d-mb-0">
    <div class="welcome d-mb-40">
        <h1 class="h4">Hi, {{ customer.first_name }}</h1>
    </div>
    <ul class="hide-m show-d">
        <li class="pb-12{% if template == 'customers/account' or template == 'customers/order' %} active{% endif %}">
			<a href="{{ routes.account_url }}" class="p1 {% if template == 'customers/account' or template == 'customers/order' %} bold-700{% else %} c-brown-40{% endif %}" title="{{ 'customer.account.order_history' | t | escape }}">
                {{ 'customer.account.order_history' | t }}
            </a>
		</li>
		<li class="pb-12{% if template == 'customers/addresses' %} active{% endif %}">
			<a href="{{ routes.account_addresses_url }}" class="p1 {% if template == 'customers/addresses' %} bold-700{% else %} c-brown-40{% endif %}" title="{{ 'customer.account.address_book' | t | escape }}">
                {{ 'customer.account.address_book' | t }}
            </a>
		</li>
        <li>
            <a href="{{ routes.account_logout_url }}" class="p1 c-brown-40" title="{{ 'customer.account.logout' | t | escape }}">
                {{ 'customer.account.logout' | t }}
            </a>
        </li>
    </ul>
</div>
<script>
    let previousScrollY = window.scrollY;
    window.addEventListener('scroll', () => {
        let scrollType;
        const currentScrollY = window.scrollY;
        if (currentScrollY > previousScrollY) {
            scrollType = 'down';
        } else if (currentScrollY < previousScrollY) {
            scrollType = 'up';
        }
        previousScrollY = currentScrollY;

        const scrollPosition = document.documentElement.scrollTop || document.body.scrollTop || 0;

        if(scrollType == 'down') {
            if (scrollPosition >= 300) {
                document.querySelector('.account-links').style.top = document.querySelector('#site-header').offsetHeight - 37 + 60 + 'px';
            } else {
                document.querySelector('.account-links').style.top = document.querySelector('#site-header').offsetHeight + 60 + 'px';
            }
        } else {
            document.querySelector('.account-links').style.top = document.querySelector('#site-header').offsetHeight + 60 + 'px';
        }
    });
</script>