{%- comment -%}
  video: Video object
  video_m: Video object for mobile
  preload: String, preload value
  overlay: Boolean, if want to use overlay
  overlay_class: String, overlay class
  vue_ref_mobile: String, vue ref for mobile
  vue_ref_desktop: String, vue ref for desktop
  autoplay: <PERSON>olean, if want to autoplay
  loop: <PERSON><PERSON><PERSON>, if want to loop
  muted: <PERSON><PERSON><PERSON>, if want to mute
{%- endcomment -%}

{%- liquid 
  if autoplay == false
    assign autoplay = false
  else
    assign autoplay = true
  endif
  if loop == false
    assign loop = false
  else
    assign loop = true
  endif
  if muted == false
    assign muted = false
  else
    assign muted = true
  endif
-%}


<div class="global-video-wrapper relative {{ additional_class }}">
  {%- if overlay -%}
    <div class="overlay {{ overlay_class }}"></div>
  {%- endif -%}
  {% if video %}
    {{
      video
      | video_tag:
      image_size: '1100x',
      controls: false,
      loop: loop,
      autoplay: autoplay,
      muted: muted,
      playsinline: true,
      preload: preload,
      class: 'hide-m show-t w-full',
      ref: vue_ref_desktop
    }}
  {% endif %}
  {% if video_m %}
    {{
      video_m
      | video_tag:
      image_size: '1100x',
      controls: false,
      loop: loop,
      autoplay: autoplay,
      muted: muted,
      playsinline: true,
      preload: preload,
      class: 'hide-t w-full',
      ref: vue_ref_mobile
    }}
  {% endif %}
</div>
