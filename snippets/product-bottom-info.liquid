{% comment %}
Description: This snippet displays more product informations

Variables:
- product: The product object
{% endcomment %}
{% assign variant = product.selected_or_first_available_variant %}
{% assign promo_msg = '' %}
{% if settings.product_global_promo_msg != blank %}
    {% capture global_promo_msg %}
        <div>
        {% if settings.product_global_promo_msg_title %}
            <div class="p1 bold-500">{{ settings.product_global_promo_msg_title }}</div>
        {% endif %}
        <div class="p2 c-black mt-4">{{ settings.product_global_promo_msg }}</div>
        </div>
    {% endcapture %}
    {% assign promo_msg = promo_msg | append: global_promo_msg %}
{% endif %}
 
{% for num in (1..5) %}
    {% assign tag_setting = 'product_promo_msg_tag' | append: num %}
    {% assign title_setting = 'product_promo_msg_title' | append: num %}
    {% assign msg_setting = 'product_promo_msg' | append: num %}

    {% if product.tags contains settings[tag_setting] %}
        {% capture tag_promo_msg %}
            <div>
            {% if settings[title_setting] != blank %}
                <div class="p1 bold-500">{{ settings[title_setting] }}</div>
            {% endif %}
            <div class="p2 c-black mt-4">{{ settings[msg_setting] }}</div>
            </div>
        {% endcapture %}
        {% assign promo_msg = promo_msg | append: tag_promo_msg %}
    {% endif %}
{% endfor %}

{% if promo_msg != blank %}
    <div class="flex flex-column pt-16 pb-16 rowgap-16">{{ promo_msg }}</div>
{% endif %}

<div class="bt-1 bt-black">
    <div class="flex colgap-12 pt-16 pb-20 d-pt-24 d-pb-24 ai-center">
        <div class="pt-5 pb-5">
        <div class="loyalty-icon">
            {% render 'global-image-wrapper', image: settings.product_loyalty_icon, image_alt: 'Loyalty Icon', additional_class: 'no-bg', preload: true %}
        </div>
        </div>
        <div class="loyalty-text rte p2">
            {% if settings.product_loyalty_message contains '[points]' %}
                {% assign point_value = settings.product_loyalty_value %}
                {% assign points = variant.price | divided_by: 100 | times: point_value | round %}
                {% assign loyalty_text = settings.product_loyalty_message | replace: '[points]', points %}
            {% else %}
                {% assign loyalty_text = settings.product_loyalty_message %}
            {% endif %}
            {{ loyalty_text }}
        </div>
    </div>
</div>

{% if section.blocks.size > 0 %}
    <div class="info-blocks p-16 rounded-8 bg-background">
        {% for block in section.blocks %}
            <div class="flex ai-center colgap-8 {% unless forloop.last %}mb-8{% endunless %}">
                <div class="icon flex ai-center jc-center">
                    {% render 'global-image-wrapper', image: block.settings.icon, image_alt: 'Info Icon', additional_class: 'no-bg', preload: true %}
                </div>
                <div class="text p3 rte">
                    {% if block.type == 'stock_info' %}
                        {% if variant.available %}
                            {{ block.settings.in_stock_text }}
                        {% else %}
                            {{ block.settings.out_of_stock_text }}
                        {% endif %}
                    {% else %}
                        {{ block.settings.text }}
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>
{% endif %}

<div class="pt-28 pb-36 d-pb-48 hd-pb-60 hhd-pb-72">
    <div class="bt-1 b-border">
    {% liquid
        assign accordion_title_class = "p1 bold-500 c-black"
        assign accordion_title_padding_class = "pt-14 pb-12 d-pb-16 colgap-12"
        assign accordion_desc_class = "p2 c-black rte mb-8"
        assign accordion_wrapper_class = "pb-2 d-pb-12 bb-1 b-border "
    %}

    {% comment %} Product description accordion {% endcomment %}
    {% if product.description != blank %}
        {% assign acc_title_desc = 'products.product.description' | t %}
        {% render "accordion",
            title: acc_title_desc,
            desc_html: product.description,
            unlink_expand: true,
            title_class: accordion_title_class,
            title_padding_class: accordion_title_padding_class,
            desc_class: accordion_desc_class,
            wrapper_class: accordion_wrapper_class
        %}
    {% endif %}
    {% comment %} End Product description accordion {% endcomment %}

    {% comment %} Product metafields accordion {% endcomment %}
    {% for num in (1..5) %}
        {% liquid
            assign title_setting = "info_title_" | append: num
            assign content_setting = "info_content_" | append: num

            assign acc_title = product.metafields.custom[title_setting]
            assign acc_content = product.metafields.custom[content_setting]
        %}

        {% if acc_content != blank %}
            {% render "accordion",
                title: acc_title,
                desc_html: acc_content,
                unlink_expand: true,
                title_class: accordion_title_class,
                title_padding_class: accordion_title_padding_class,
                desc_class: accordion_desc_class,
                wrapper_class: accordion_wrapper_class
            %}
        {% endif %}
    {% endfor %}
    {% comment %} End Product metafields accordion {% endcomment %}

    {% comment %} Global product accordion {% endcomment %}
    {% for num in (1..5) %}
        {% liquid
            assign title_setting = "pdp_info_title_" | append: num
            assign content_setting = "pdp_info_content_" | append: num
        %}
        {% if settings[content_setting] != blank %}
            {% render "accordion",
                title: settings[title_setting],
                desc_html: settings[content_setting],
                unlink_expand: true,
                title_class: accordion_title_class,
                title_padding_class: accordion_title_padding_class,
                desc_class: accordion_desc_class,
                wrapper_class: accordion_wrapper_class
            %}
        {% endif %}
    {% endfor %}
    {% comment %} End Global product accordion {% endcomment %}
    </div>
</div>