{%- comment -%}
Description:
Snippet for displaying product tags as label

Variables:
- product: Object. the product object
- wrapper_class: String. The class for the wrapper.
- text_class: String. The class for the text.
{%- endcomment -%}
{%- if settings.pg_enable_labels -%}
    {%- assign variant = product.selected_or_first_available_variant -%}
    {%- assign sale_label = '' -%}
    {%- if variant.compare_at_price > variant.price -%}
        {% comment %} {%- assign price_diff = variant.compare_at_price | minus: variant.price | times: 1.0 -%}
        {% assign test = price_diff | divided_by: variant.price %}
        {%- assign percent = price_diff | divided_by: variant.price | times: 100.0 | round -%}
        {%- capture sale_label -%}
            <span class="product-label bg-cream p4 bold-500 b-1 b-light-grey">Sale {{ percent }}%</span>
        {%- endcapture -%} {% endcomment %}
        {% comment %} {%- capture sale_label -%}
                <span class="relative product-label p4 bg-light-orange c-dark-blue">
                    <span role="presentation" class="ribbon bg-light-orange"></span>
                    <span class="p4 bold-700">Sale</span>
                </span>
        {%- endcapture -%} {% endcomment %}
    {%- endif -%}

    {%- if text_class == blank -%}
        {%- assign text_class = 'p4' -%}
    {%- endif -%}

    {%- assign item_htmls = '' -%}
    {%- if variant.available == false -%}
        {%- capture item_html -%}
        <span class="relative product-label {{ text_class }} b-1 b-brown-60 bg-white">
            <span class="{{ text_class }} c-brown-60">Out of Stock</span>
        </span>
        {%- endcapture -%}
        {%- assign item_htmls = item_htmls | append: item_html -%}
    {%- endif -%}
    {%- for i in (1..10) -%}
        {%- assign tag = "pg_tag_" | append: i -%}
        {%- assign bg_color = "pg_tag_bg_" | append: i -%}
        {%- assign text_color = "pg_tag_color_" | append: i -%}
        {%- assign border_color = "pg_tag_border_" | append: i -%}

        {%- if product.tags contains settings[tag] -%}
            {%- assign tag_text = settings[tag] | split: ':' | last -%}
            {%- capture item_html -%}
                <span class="relative product-label {{ text_class }}" style="color: {{ settings[text_color] }}; background-color: {{ settings[bg_color] }};">
                    <span class="{{ text_class }}">{{ tag_text }}</span>
                </span>
            {%- endcapture -%}
            {%- assign item_htmls = item_htmls | append: item_html -%}
        {%- endif -%}
    {%- endfor -%}

    {%- if sale_label != blank -%}
        {%- assign item_htmls = sale_label | append: item_htmls -%}
    {%- endif -%}

    {%- if item_htmls != blank -%}
        <div class="{{ wrapper_class }}">{{ item_htmls }}</div>
    {%- endif -%}
{%- endif -%} 
