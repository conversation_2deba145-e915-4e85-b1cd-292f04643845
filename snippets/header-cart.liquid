<style>
.rowgap-2 {
	row-gap: 2px;
}
.pt-14 {
	padding-top: 14px;
}
.pb-14 {
	padding-bottom: 14px;
}
.mb-10 {
	margin-bottom: 10px;
}
.global-form-qty.cdrawer button svg {
	width: 10px;
	height: 10px;
}
.global-form-qty.cdrawer button.global-minus,
.global-form-qty.cdrawer button.global-plus {
	padding: 6px 6px;
}

.global-form-qty.cdrawer button.global-minus {
	padding-right: 16px;
}
.global-form-qty.cdrawer button.global-plus {
	padding-left: 16px;
}
.global-form-qty.cdrawer .global-qty-text {
	height: unset;
}
.cross-sell {
	padding-bottom: 90px;
}
.cross-sell .nav-wrapper .swiper-btn-next,
.cross-sell .nav-wrapper .swiper-btn-prev {
	width: 30px;
	height: 30px;
}
.cross-sell .nav-wrapper .swiper-btn-next svg,
.cross-sell .nav-wrapper .swiper-btn-prev svg {
	width: 14px;
	height: 14px;
}
.header-cart-items .cart-item .rm-link {
	text-decoration: underline;
}
.header-cart-items .cart-item .rm-link:hover {
	text-decoration: none;
}
.header-cart-items .cart-item .image-con {
	width: 80px;
}
.header-cart-items .cart-item .details-con {
	width: calc(100% - 96px);
}
.header-cart-items .cart-item .title-wrp .title-item {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
	align-self: stretch;
	overflow: hidden;
	text-overflow: ellipsis;

}
.header-cart-items .cart-item .ws-nowrap {
	white-space: nowrap;
}
.cross-sell .product-card-2 .product-image {
	width: 125.5px;
	height: 125.5px;
}
.cross-sell .product-card-2 .details-con .title-wrp {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	align-self: stretch;
	overflow: hidden;
	text-overflow: ellipsis;
}
#cart-drawer-main .payment-img {
    width: 33px;
}
#cart-drawer-main .loyalty-wrp {
	margin-left: -20px;
	width: calc(100% + 40px);
}
#cart-drawer-main .loyalty-wrp a,
#cart-drawer-main .tnc-wrp a {
	text-decoration: underline;
	color: inherit;
}
#cart-drawer-main .loyalty-wrp a:hover,
#cart-drawer-main .tnc-wrp a:hover {
	text-decoration: none;
}
#cart-drawer-main .loyalty-wrp .loyalty-icon {
	width: 18px;
	height: 18px;
}
@media screen and (min-width: 1024px) {
	.header-cart-items .cart-item .image-con {
		width: 88px;
	}
	.header-cart-items .cart-item .details-con {
		width: calc(100% - 104px);
	}
	.header-cart-items .cart-item .item-txt-wrp {
		width: auto;
	}
	.global-form-qty.cdrawer button.global-minus {
		padding-right: 16px;
	}
	.global-form-qty.cdrawer button.global-plus {
		padding-left: 16px;
	}
	.cross-sell .nav-wrapper .swiper-btn-next,
	.cross-sell .nav-wrapper .swiper-btn-prev {
		width: 32px;
		height: 32px;
	}
	.cross-sell .product-card-2 .product-image {
		width: 120px;
		height: 120px;
	}
	#cart-drawer-main .payment-img {
		width: 40px;
	}
	#cart-drawer-main .loyalty-wrp {
		margin-left: -24px;
		width: calc(100% + 48px);
	}
	#cart-drawer-main .loyalty-wrp .loyalty-icon {
		width: 20px;
		height: 20px;
	}
}
</style>
<div id="cart-drawer-main" class="header-cart-wrapper bg-off-white w-full h-full flex flex-column relative">
	{% # overlay & loading %}
	<div class="header-cart-loading cart-loading absolute top-0 left-0 w-full h-full zi-3 bg-light-grey opacity-7">&nbsp;</div>
	{% comment %} <div class="header-cart-note-overlay absolute top-0 left-0 w-full h-full zi-3 bg-beige opacity-5" onclick="$360.handleCartNote(event);">&nbsp;</div> {% endcomment %}
	{% # END overlay & loading %}
	
	<div class="header-cart-title flex jc-between ai-center pt-16 pb-16 pl-20 pr-20 d-pt-16 d-pb-16 d-pl-20 d-pr-20 bb-1 b-border">
		<h3 class="tl title-wrapper flex jc-start ai-start">
			<span class="h6 tl">{{ 'cart.general.page_title' | t }}</span>
			{%- if cart.item_count > 0 -%}
			<span class="ml-4 p5 tl">{{ cart.item_count }}</span>
			{%- endif -%}
		</h3>
		<button type="button" class="header-cart-close flex ai-center jc-center zi-1" onclick="$360.hideHeaderCart()">{{ settings.icon_close }}</button>
	</div>
	{%- if cart.item_count == 0 -%}
		<div class="empty-cart h-full flex flex-column jc-between ai-start">
			<div class="relative top-wrp flex flex-column jc-center ai-start pt-20 pb-20 d-pt-24 d-pb-24 pl-20 pr-20 d-pl-24 d-pr-24">
				{%- if settings.cart_empty_title != blank -%}
					<h3 class="h6 w-full tc">{{ settings.cart_empty_title | newline_to_br }}</h3>
				{%- endif -%}
				{%- if settings.cart_empty_msg != blank -%}
					<div class="p2 w-full tc mt-8">{{ settings.cart_empty_msg | newline_to_br }}</div>
				{%- endif -%}
				{%- render 'cart-empty-content' -%}
			</div>
			{%- render 'cart-image-banner' -%}
		</div>
	{%- else -%}
		<div class="relative listing-cart-wrapper flex-1">
			{% comment %}
			{%- render 'free-shipping-info', 
				padding_class: 'free-shipping-info pt-16 pb-24 pl-20 pr-20 d-pl-24 d-pr-24',
				text_class: 'p3' 
			-%}
			{% endcomment %}
			<form action="{{ routes.cart_url }}" method="post" id="header-cart-form">
				<div class="relative listing-cart-inner flex jc-between ai-start flex-column">
					<div class="header-cart-middle w-full flex flex-column">
						<div class="cart-items-wrapper pb-24 pl-20 pr-20 d-pl-24 d-pr-24">
							<ul class="header-cart-items flex flex-column">
								{%- for item in cart.items -%}
									{%- assign pro = item.product -%}
									{%- assign group_id = false -%}
									{%- assign group_parent = false -%}
									{%- assign is_gwp = false -%}
									{%- if item.properties['_isGWP'] != blank -%}
										{%- assign is_gwp = true -%}
									{%- endif -%}
									{%- if item.properties['_group_id'] != blank -%}
										{%- assign group_id = item.properties['_group_id'] -%}
									{%- endif -%}
									{%- if item.properties['_group_parent'] != blank -%}
										{%- assign group_parent = item.properties['_group_parent'] -%}
									{%- endif -%}
									{%- if cart.item_count == 1 -%}
										{%- assign class = 'pt-16 pb-16' -%}
									{%- else -%}
										{%- if forloop.first -%}
											{%- assign class = 'pt-16 pb-16' -%}
										{%- else -%}
											{%- if forloop.last -%}
												{%- assign class = 'pt-16 pb-0 bt-1 b-border' -%}
											{%- else -%}
												{%- assign class = 'pt-16 pb-16 bt-1 b-border' -%}
											{%- endif -%}
										{%- endif -%}
									{%- endif -%}
									{%- render 'cart-item', 
										index: forloop.index, 
										item: item, 
										group_id: group_id, 
										group_parent: group_parent, 
										is_cart_page: false,
										class: class
									-%}
								{%- endfor -%}
							</ul>
						</div>
						{%- if settings.cart_cross_sell_collection != blank -%}
							<div class="relative w-full pt-28 d-pt-24">
								<div class="cross-sell">
									<div class="flex jc-between ai-center mb-20 d-mb-16 w-full pl-20 pr-20 d-pl-24 d-pr-24">
										{%- if settings.cart_cross_sell_title != blank -%}
											<div class="p5 c-brown-100">{{ settings.cart_cross_sell_title }}</div>
										{%- endif -%}
										<div class="nav-wrapper relative w-auto jc-between ai-center colgap-8 hide-m d-flex">
											<div class="swiper-btn-prev flex jc-center ai-center rounded-100c b-1 b-dark-blue cursor">
												{{ settings.icon_slide_left }}
											</div>
											<div class="swiper-btn-next flex jc-center ai-center rounded-100c b-1 b-dark-blue cursor">
												{{ settings.icon_slide_right }}
											</div>
										</div>
									</div>
									<div class="relative swiper pb-20 d-pb-24 pl-20 pr-20 d-pl-24 d-pr-24">
										<div class="swiper-wrapper">
											{%- for product in settings.cart_cross_sell_collection.products, limit: settings.cart_cross_sell_limit -%}
												{%- assign variant = product.selected_or_first_available_variant -%}
												{%- unless product.tags contains 'hidden' -%}
													{%- assign exclude = false -%}
													{%- for item in cart.items -%}
														{%- if item.product_id == product.id or item.product.title == product.title -%}
															{%- assign exclude = true -%}
														{%- endif -%}
													{%- endfor -%}
													{%- if exclude == false and variant.available == true -%}
														<div class="swiper-slide h-full swiper-item rounded-12">
															{%- render 'product-card-2',
																product: product,
																preload_img: true,
																padding_class: 'pb-24 d-pb-28' -%}
														</div>
													{%- endif -%}
												{%- endunless -%}
											{%- endfor -%}
										</div>
									</div>
								</div>
							</div>
						{%- endif -%}
					</div>
					<div class="header-cart-bottom flex flex-column jc-start bg-white w-full pl-20 pr-20 d-pl-24 d-pr-24 pb-16 d-pb-16 zi-2">
						{%- if settings.cart_loyalty_enabled -%}
						<div class="relative loyalty-wrp bg-background pl-20 pr-20 d-pl-24 d-pr-24 pt-14 pb-14 mb-10">
							{% comment %} {%- assign customer = false -%} {% endcomment %}
							{%- if customer -%}
								<div class="relative flex jc-between ai-center">
									<div class="flex jc-start ai-center colgap-4">
										{%- if settings.cart_loyalty_icon != blank -%}
										<div class="loyalty-icon">
											{%- render 'global-image-wrapper',
												image: settings.cart_loyalty_icon,
												additional_class: 'no-bg',
												preload: false,
												image_alt: 'Loyalty Program'
											-%}
										</div>
										{%- endif -%}
										<div class="p2 tl">{{ settings.cart_loyalty_member_msg }}</div>
									</div>
									<div class="flex jc-end p2 tr">{{ cart.total_price | divided_by: 100 | times: settings.product_loyalty_value | floor }}</div>
								</div>
							{%- else -%}
								<div class="p2 tl">{{ settings.cart_loyalty_nonmember_msg }}</div>
							{%- endif -%}
						</div>
						{%- endif -%}
						{%- render 'cart-action', cart: cart -%}
					</div>
				</div>
			</form>
		</div>
	{%- endif -%}
</div>
