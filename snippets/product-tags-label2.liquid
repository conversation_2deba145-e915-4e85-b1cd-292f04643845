{%- comment -%}
Description:
Snippet for displaying product tags as label

Variables:
- product: Object. the product object
- wrapper_class: String. The class for the wrapper.
- text_class: String. The class for the text.
{%- endcomment -%}
{%- if settings.pg_enable_labels -%}
    {%- assign variant = product.selected_or_first_available_variant -%}
    {%- assign sale_label = '' -%}
    {%- if variant.compare_at_price > variant.price -%}
        {% comment %} {%- assign price_diff = variant.compare_at_price | minus: variant.price | times: 1.0 -%}
        {% assign test = price_diff | divided_by: variant.price %}
        {%- assign percent = price_diff | divided_by: variant.price | times: 100.0 | round -%}
        {%- capture sale_label -%}
            <span class="product-label2 bg-cream p4 bold-500 b-1 b-light-grey">Sale {{ percent }}%</span>
        {%- endcapture -%} {% endcomment %}
        {% comment %} {%- capture sale_label -%}
                <span class="product-label2 relative p3 bg-light-orange c-dark-blue">
                    <span role="presentation" class="ribbon bg-light-orange"></span>
                    <span class="p3">Sale</span>
                </span>
        {%- endcapture -%} {% endcomment %}
        
    {%- endif -%}

    {%- if text_class == blank -%}
        {%- assign text_class = 'p4' -%}
    {%- endif -%}

    {%- assign item_htmls = '' -%}
    {%- for i in (1..30) -%}
        {%- assign tag = "pg2_tag_" | append: i -%}
        {%- assign label = "pg2_label_" | append: i -%}
        {%- assign bg_color = "pg2_tag_bg_" | append: i -%}
        {%- assign text_color = "pg2_tag_color_" | append: i -%}
        {%- assign border_color = "pg2_tag_border_" | append: i -%}

        {%- if product.tags contains settings[tag] -%}
            {%- if settings[tag] contains ':' -%}
                {%- assign tag_text = settings[tag] | split: ':' | last -%}
            {%- else -%}
                {%- assign tag_text = settings[tag] -%}
            {%- endif -%}
            {%- if settings[label] != blank -%}
                {%- assign tag_text = settings[label] -%}
            {%- endif -%}

            {%- capture item_html -%}
                <span class="product-label2 relative {{ text_class }}" style="color: {{ settings[text_color] }}; background-color: {{ settings[bg_color] }};{% if settings[border_color] != blank %}border: 1px solid {{ settings[border_color] }};{%endif %}">
                    <span class="{{ text_class }}">{{ tag_text }}</span>
                </span>
            {%- endcapture -%}
            {%- assign item_htmls = item_htmls | append: item_html -%}
        {%- endif -%}
    {%- endfor -%}

    {% comment %} {%- for tag in product.tags -%}
        {%- if tag contains 'discount:' -%}
            {%- assign tag_text = tag | split: ':' | last -%}
            {%- capture item_html -%}
                <span class="product-label2 relative p4 bg-orange-tint c-orange">
                    <span class="p4">{{ tag_text }}</span>
                </span>
            {%- endcapture -%}
            {%- assign item_htmls = item_htmls | append: item_html -%}
        {% endif %}
    {%- endfor -%} {% endcomment %}

    {%- if sale_label != blank -%}
        {%- assign item_htmls = sale_label | append: item_htmls -%}
    {%- endif -%}

    {%- if item_htmls != blank -%}
        <div class="{{ wrapper_class }}">{{ item_htmls }}</div>
    {%- endif -%}
{%- endif -%} 
