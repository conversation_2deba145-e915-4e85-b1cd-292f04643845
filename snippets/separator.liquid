{%- comment -%}
  
Description:
  Snippet for displaying separator / border

  Variables:
  - show: Boolean
  - weight: Number (1-5)
  - color: String
  - full_width: Boolean
  - has_container: Boolean
  - wrapper_classes: String
  - inner_classes: String

{%- endcomment -%}

{% unless show == false %}
  
  {% assign weight = weight | default: 1 %}
  {% if weight > 5 or weight < 1 %}
    {% assign weight = 1 %}
  {% endif %}

  {% assign wrapper_classes = wrapper_classes | default: "" %}
  {% unless has_container == false or full_width == true %}
    {% assign wrapper_classes = wrapper_classes | append: " container" %}
  {% endunless %}

  <div class="separator {{ wrapper_classes }}">
    <div
      class="separator-inner bt-{{ weight }} b-border {{ inner_classes }}"
      {% if color %}style="border-color: {{ color }}"{% endif %}
    ></div>
  </div>
  
{% endunless %}