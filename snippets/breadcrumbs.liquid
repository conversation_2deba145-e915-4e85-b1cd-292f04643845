{%- comment -%}
Description:
Snippet for displaying breadcrumbs

Variables:
wrapper_class: String: additional wrapper class
inner_class: String: additional inner class
text_class: String: text class
hide_home: String, value can be yes or no
first_text: String, first breadcrumb text.
first_url: String, first breadcrumb url.
bg_color: String, the wrapper background color,
with_container: String, yes/no, there will be a container class
top_padding: String: top padding class
bottom_padding: String: bottom padding class
{%- endcomment -%}

{%- if with_container == blank -%}
    {%- assign with_container = 'yes' -%}
{%- endif -%}
{%- if top_padding == blank -%}
    {%- assign top_padding = 'pt-12 d-pt-16' -%}
{%- endif -%}
{%- if bottom_padding == blank -%}
    {%- assign bottom_padding = 'pb-12 d-pb-16' -%}
{%- endif -%}
{%- if text_class == blank -%}
    {%- assign text_class = 'p5 c-grey' -%}
{%- endif -%}
{%- if wrapper_class == blank -%}
    {%- assign wrapper_class = '' -%}
{%- endif -%}

<div class="breadcrumb-wrapper {{ wrapper_class }}" {% if bg_color != blank %}style="background-color: {{ bg_color }}"{% endif %}>
    {%- if with_container == 'yes' -%}
	    <div class="container">
    {%- endif -%}
        {%- if blocks.size > 0 -%}
            <nav class="breadcrumb flex ai-center {{ top_padding }} {{ bottom_padding }}" role="navigation" aria-label="breadcrumbs">
                {%- for block in blocks -%}
                    {%- if forloop.index > 1 -%}
                        <span class="bc-item level-{{ forloop.index }} separator {{ text_class }} mr-4" aria-hidden="true">
                            /
                        </span>
                    {%- endif -%}

                    {%- unless forloop.last -%}
                        <a href="{{ block.settings.url }}" title="{{ block.settings.text | escape }}" class="bc-item level-{{ forloop.index }} text {{ text_class }}">{{ block.settings.text }}</a>
                    {%- else -%}
                        <span class="bc-item level-{{ forloop.index }} text {{ text_class }} last">{{ block.settings.text }}</span>
                    {%- endunless -%}
                {%- endfor -%}
            </nav>
        {%- else -%}
            {% if first_text == blank %}
                {% assign first_text = 'breadcrumbs.home' | t %}
            {% endif %}
            {% if first_url == blank %}
                {% assign first_url = '/' %}
            {% endif %}
            <nav class="breadcrumb flex ai-center {{ inner_class }} {{ top_padding }} {{ bottom_padding }}" role="navigation" aria-label="breadcrumbs">
                {%- if hide_home != 'yes' -%}
                    <a href="{{ first_url }}" title="{{ first_text | escape }}" class="bc-item text {{ text_class }} mr-4">{{ first_text }}</a>
                {%- endif -%}

                {%- if template contains 'page' -%}
                    {%- if hide_home != 'yes' -%}
                        <span class="bc-item level-2 separator {{ text_class }} mr-4" aria-hidden="true">
                            /
                        </span>
                    {%- endif -%}
                    <span class="bc-item level-2 text {{ text_class }} last">
                        {{ page.title }}
                    </span>
                {%- elsif template contains 'product' -%}
                    {%- assign coll_handle = '' -%}
                    {%- if product.collections.size > 0 -%}
                        {%- if hide_home != 'yes' -%}
                            <span class="bc-item level-2 separator {{ text_class }} mr-4" aria-hidden="true">
                                /
                            </span>
                        {%- endif -%}
                        {%- assign has_tag_collection = false -%}
                        {%- for collection in product.collections -%}
                            {%- if product.tags contains collection.title -%}
                                <a href="{{ collection.url }}" title="{{ collection.title | escape }}" class="bc-item level-2 text {{ text_class }} mr-4 capitalize">{{ collection.title }}</a>
                                {%- assign has_tag_collection = true -%}
                                {%- assign coll_handle = collection.title | handleize -%}
                                {%- break -%}
                            {%- endif -%}
                        {%- endfor -%}
                        {%- if has_tag_collection == false -%}
                            <a href="{{ product.collections.first.url }}" title="{{ product.collections.first.title | escape }}" class="bc-item level-2 text {{ text_class }} mr-4 capitalize">{{ product.collections.first.title }}</a>
                            {%- assign coll_handle = product.collections.first.title | escape | handleize -%}
                        {%- endif -%}
                    {%- endif -%}

                    <span class="bc-item {% if product.collections.size > 0 %}level-3{% else %}level-2{% endif %} separator {{ text_class }} mr-4" aria-hidden="true">
                        /
                    </span>
                    {% comment %} {%- assign has_product_type = false -%}
                    {%- if product.type != blank -%}
                        {%- assign type_handle = product.type | handleize -%}
                        {%- assign c = '' -%}
                        {%- assign h = product.type | downcase -%}
                        {%- for coll in product.collections -%}
                            {%- if coll.title contains product.type or coll.title contains product.title -%}
                                {%- assign has_product_type = true -%}
                                {%- assign c = coll -%}
                                {%- break -%}
                            {%- endif -%}
                        {%- endfor -%}
                        {%- if coll_handle != blank -%}
                            {%- unless type_handle == coll_handle -%}
                                <a href="{{ c.url }}" class="bc-item {% if product.collections.size > 0 %}level-3{% else %}level-2{% endif %} text {{ text_class }} mr-4 capitalize">{{ product.type }}</a>
                                <span class="bc-item {% if product.collections.size > 0 %}level-3{% else %}level-2{% endif %} separator {{ text_class }} mr-4" aria-hidden="true">
                                    /
                                </span>
                            {%- endunless -%}
                        {%- else -%}
                            <a href="{{ c.url }}" class="bc-item {% if product.collections.size > 0 %}level-3{% else %}level-2{% endif %} text {{ text_class }} mr-4 capitalize">{{ product.type }}</a>
                            <span class="bc-item {% if product.collections.size > 0 %}level-3{% else %}level-2{% endif %} separator {{ text_class }} mr-4" aria-hidden="true">
                                /
                            </span>
                        {%- endif -%}
                    {%- endif -%} {% endcomment %}

                    {%- assign level = 2 -%}
                    {%- if product.collections.size > 0  -%}
                        {%- assign level = level | plus: 1 -%}
                    {%- endif -%}
                    {%- if has_product_type == true  -%}
                        {%- assign level = level | plus: 1 -%}
                    {%- endif -%}

                    <span class="bc-item level-{{ level }} text {{ text_class }} last">{{ product.title }}</span>
                {%- elsif template contains 'collection' -%}
                    <span class="bc-item level-2 separator {{ text_class }} mr-4" aria-hidden="true">
                        /
                    </span>
                    {% if template.suffix == 'brand' %}
                        <a href="/pages/brand-directory" title="{{ 'collections.general.brands' | t | escape }}" class="bc-item level-2 text {{ text_class }}">{{ 'collections.general.brands' | t }}</a>
                        <span class="bc-item level-3 separator {{ text_class }} mr-4" aria-hidden="true">
                            /
                        </span>
                    {% endif %}
                    <span class="bc-item {% if template.suffix == 'brand' %}level-3{% else %}level-2{% endif %} text {{ text_class }} last">{{ collection.title }}</span>
                {%- elsif template == 'blog' -%}
                    <span class="bc-item level-2 separator {{ text_class }} mr-4" aria-hidden="true">
                        &nbsp;/&nbsp;
                    </span>
                    {%- if current_tags -%}
                        <a href="{{ blog.url }}" class="bc-item level-2 text {{ text_class }} mr-4">{{ blog.title }}</a>
                        <span class="bc-item level-3 separator {{ text_class }} mr-4" aria-hidden="true">
                            &nbsp;/&nbsp;
                        </span>
                        <span class="bc-item level-3 text {{ text_class }} last mr-4">
                            {{ current_tags | join: " + " }}
                        </span>
                    {%- else -%}
                        <span class="bc-item level-3 text {{ text_class }} last">
                            {{ blog.title }}
                        </span>
                    {%- endif -%}
                {%- elsif template == 'article' -%}
                    {%- if hide_home != 'yes' -%}
                        <span class="bc-item level-2 separator {{ text_class }} mr-4" aria-hidden="true">
                            &nbsp;/&nbsp;
                        </span>
                    {%- endif -%}
                    <a href="{{ blog.url }}" class="bc-item level-2 text {{ text_class }} mr-4">{{ blog.title }}</a>
                    <span class="bc-item level-3 separator {{ text_class }} mr-4" aria-hidden="true">
                        &nbsp;/&nbsp;
                    </span>
                    <span class="bc-item level-3 text {{ text_class }} last">
                        {{ article.title }}
                    </span>
                {%- elsif template == 'cart' -%}
                    {%- if hide_home != 'yes' -%}
                        <span class="bc-item level-2 separator {{ text_class }} mr-4" aria-hidden="true">
                            /
                        </span>
                    {%- endif -%}
                    <span class="bc-item level-2 text {{ text_class }} last">{{ 'breadcrumbs.cart' | t }}</span>
                {%- else -%}
                    {%- if hide_home != 'yes' -%}
                        <span class="bc-item level-2 separator {{ text_class }} mr-4" aria-hidden="true">
                            /
                        </span>
                    {%- endif -%}
                    <span class="bc-item level-2 text {{ text_class }} last">
                        {{ page_title }}
                    </span>
                {%- endif -%}
            </nav>
        {%- endif -%}
    {%- if with_container == 'yes' -%}
        </div>
    {%- endif -%}
</div>
