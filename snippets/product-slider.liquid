{% comment %}
Description:
Snippet for displaying the products inside swiper

Varaibles:
- products: The product array
- limit: The products limit
- load_inside_vue: Boolean. To indicate the snippet loaded on view element or normal html
{% endcomment %}

<div class="products swiper">
    <div class="swiper-wrapper">
        {%- for product in products limit: limit -%}
            {%- if product.featured_image != blank -%}
                <div class="swiper-slide">
                    <div class="product-list product-wrapper h-full">
                        {%- render 'product-card', product: product -%}
                    </div>
                </div>
            {%- endif -%}
        {%- endfor -%}
    </div>
</div>
