{%- comment -%}
Description:
Snippet for displaying product card

Variables:
- product. Object. the product object
- preload_img: Boolean. The variable to lazy load image or not
- hide_atc: <PERSON>olean. The variable to hide the add to cart button
- x_spacing_on_mobile: Boolean. The variable to add spacing on bottom-con on mobile
- no_rounded_on_mobile: Boolean. The variable to remove border radius on mobile
{%- endcomment -%}
{%- if hide_atc == blank -%}
    {%- assign hide_atc = true -%}
{%- endif -%}
{%- if preload_img == blank -%}
    {%- assign preload_img = false -%}
{%- endif -%}
{%- assign variant = product.selected_or_first_available_variant -%}
{%- assign image_alt = product.title | escape -%}
{%- assign pd_images = product.media | where: 'media_type', 'image' -%}
{%- assign s = pd_images | size -%}
<div class="product-card flex flex-column jc-between {% if s > 1 and settings.product_card_image_hover %}with-hover{% endif %} {% if product.tags contains 'main' %}main-product{% endif %} {% if no_rounded_on_mobile == true %}no-rounded-on-mobile{% endif %} overflow relative" data-id="swiper-{{ product.id }}">
    {%- render 'product-tags-label', 
        product: product, 
        wrapper_class: 'absolute top-8 left-8 d-top-12 d-left-12 tags-1', 
    -%}
    <a href="{{ product.url }}" title="{{ product.title | escape }}" class="top-con">
        {%- if variant.featured_image -%}
            {%- assign image = variant.featured_image -%}
        {%- else -%}
            {%- assign image = product.featured_image -%}
        {%- endif -%}
        {%- if image == blank -%}
            {%- assign image = settings.desktop_placeholder -%}
        {%- endif -%}
        <div class="relative product-image hide-m show-d">
            <div class="relative primary">
                {%- render 'global-image-wrapper', 
                    image: image,
                    additional_class: 'no-bg',
                    preload: preload_img,
                    image_alt: image_alt
                -%}
            </div>
            {% comment %} {%- assign singleVariant = 'no' -%}
            {%- if product.has_only_default_variant -%}
                {%- assign singleVariant = 'yes' -%}
            {%- endif -%} {% endcomment %}
            {% comment %} <button 
                onclick="quickBuy.handleShowPopup('{{ product.handle }}', '{{ singleVariant }}', '{{ variant.id }}', this);" 
                type="button" 
                class="absolute bg-white quick-buy btn-circle btn-m dark-btn-loading bottom-15 right-15 d-bottom-20 d-right-20"
                {% unless variant.available %}title="{{ 'products.product.unavailable' | t }}" disabled{% endunless %}
            >{{ settings.icon_plus }}</button> {% endcomment %}
            <!-- Swym Wishlist Plus EPI Button with default first variant -->
            {% comment %} <button type="button" aria-label="Add to Wishlist" data-with-epi="true" 
                class="wishlist flex ai-center jc-center absolute top-15 right-15 swym-button swym-add-to-wishlist-view-product product_{{ product.id }}" 
                data-swaction="addToWishlist" 
                data-product-id="{{ product.id | json}}" 
                data-variant-id="{{ variant.id }}" 
                data-product-url="{{ shop.url }}{{ product.url }}"
            ></button>   {% endcomment %}
            <!-- Swym Wishlist Plus EPI Button-->
        </div>
        <div class="relative galleries-wrp show-m hide-d">
            <div class="relative w-full swiper">
                <div class="swiper-wrapper">
                    {%- render 'product-color-swatches',
                        product: product,
                        type: 'galleries'
                    -%}
                </div>
                <div class="swiper-pagination tl"></div>
            </div>
        </div>
    </a>
    <div class="bottom-con flex flex-column jc-start ai-start {% if x_spacing_on_mobile == true %}pl-12 pr-12{% endif %}">
        <div class="relative w-full pc-color-swatches-wrp flex jc-start ai-center colgap-4 mb-8 mt-16">
            {%- render 'product-color-swatches',
                product: product,
                type: 'swatches'
            -%}
        </div>
        <a href="{{ product.url }}" title="{{ product.title | escape }}" class="title-wrp h8 tl">
            {{ product.title }}
        </a>
        <div class="price-tags-wrp flex jc-start ai-start flex-wrap colgap-4 rowgap-4 mt-4">
            <div class="price-varies p2 {% if product.price_varies == false %}hide-m{% endif %}">{{ 'products.product.from' | t }}</div>
            <div class="applied-price p2">{{ variant.price | money_with_currency }}</div>
            {%- assign percentage = 0 -%}
            {%- assign price_striked = '' -%}
            {%- if variant.compare_at_price > variant.price and product.price_varies == false -%}
                {%- assign price_striked = variant.compare_at_price | money_with_currency -%}
                {%- assign percentage = variant.compare_at_price | minus: variant.price | times: 100 | divided_by: variant.compare_at_price -%}
            {%- endif -%}
            <div class="striked-price striked p2 opacity-3 {% if price_striked == '' %}hide-m{% endif %}">{{ price_striked }}</div>
            <div class="discount-tag product-label2 relative p4 bg-violet c-black {% if percentage == 0 %}hide-m{% endif %}">{{ percentage | percent_with_symbol |  append: '% off'}}</div>
            {%- render 'product-tags-label2', 
                product: product, 
                wrapper_class: 'tags-2 flex jc-start ai-center flex-wrap colgap-4 rowgap-4',
            -%}
        </div>
        {%- unless hide_atc -%}
        <div class="relative quick-action">
            <input type="hidden" value="1" class="quick-qty">
            {%- if variant.available == true -%}
                <button type="button" onclick="$360.addToCartQuick(this);" class="w-full atc-button btn12 d-w-auto tc" data-variant-id="{{ variant.id }}">{{ 'products.product.add_to_bag' | t }}</button>
            {%- else -%}
                <button type="button" class="w-full atc-button btn12 d-w-auto tc disabled" data-variant-id="{{ variant.id }}">{{ 'products.product.unavailable' | t }}</button>
            {%- endif -%}
        </div>
        {%- endunless -%}
    </div>

    {%- # secondary image -%}
    {%- if s > 1 and settings.product_card_image_hover -%}
        {%- assign mood_def_filter = 'mood:' | append: variant.title -%}
        {%- assign mood_def = pd_images | where: 'alt', mood_def_filter -%}
        {%- if mood_def.size > 0 -%}
            {%- assign l = mood_def[0] -%}
        {%- else -%}
            {%- assign l = pd_images[1] -%}
        {%- endif -%%}
        <div class="relative secondary">
            {%- render 'global-image-wrapper',
                image: l,
                additional_class: 'no-bg',
                preload: preload_img,
                image_alt: image_alt 
            -%}
        </div>
    {%- endif -%}
    {%- # END secondary image -%}

    {% comment %} {% render 'okendo-reviews-product-rating-summary', product: product %} {% endcomment %}
</div>