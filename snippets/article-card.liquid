{% if article != blank %}
    {%- assign url = article.url -%}
    {% comment %} {%- assign img = article.image -%} {% endcomment %}
    {%- assign title = article.title -%}
    {%- assign excerpt = article.excerpt -%}
{% endif %}

<div class="article-card h-full bg-white p-12 rounded-12">
    <a href="{{ url }}" title="{{ title | escape }}" class="flex flex-column h-full">
        <div class="article-image">
            {% render 'global-image-wrapper',
                image: article.image,
                image_alt: title | escape
            %}
        </div>
        <div class="flex flex-1 flex-column jc-between d-rowgap-20 pt-16 pb-8 pl-4 pr-4">
            <div class="article-text mb-20 d-mb-0">
                {% if article.tags.size > 0 %}
                    <div class="flex flex-wrap colgap-4 rowgap-8 mb-12">
                        {% for tag in article.tags limit: 3 %}
                            <p class="article-tag p3 b-1 b-brown-20">{{ tag }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <h5 class="h5 mb-12">{{ title }}</h5>
                <div class="excerpt rte p2">{{ excerpt }}</div>
            </div>
            <span class="link1 w-max-cont" title="{{ title | escape }}">{{ 'blogs.article.read_more' | t }}</span>
        </div>
    </a>
</div>