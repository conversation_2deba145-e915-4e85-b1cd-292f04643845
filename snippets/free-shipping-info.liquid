{% comment %} 
	padding_class: classes for padding
    text_class: classes for text
{% endcomment %}
{%- if text_class == blank -%}
    {%- assign text_class = 'p4 tl' -%}
{%- endif -%}
{% if settings.cart_enable_fs_info %}
    <style>
    .header-cart .free-shipping-line__inner{
        display: block;
    }
    .free-shipping-line .free-shipping-line__inner{
        height: 4px;
        transition: all 1s;
        border-radius: 5px;
    }
    </style>
    {% comment %} 
    {%- assign cf = settings.cart_fs_free | times: 100 -%}
    {%- if localization.country.iso_code != 'SG' -%}
        {%- assign convert_pro = all_products['for-internal-use-converter'] -%}
        {%- assign converted_price = settings.cart_fs_free | times: convert_pro.price -%}
        {%- assign cf = converted_price | divided_by: 100 -%}
    {%- endif -%} {% endcomment %}
    <div class="free-shipping-line {% if padding_class != blank %} {{  padding_class }} {% else %} pt-0 pb-0 {% endif %}">
        <div class="fs-text-con flex flex-column">
            {%- if customer -%}
                {%- assign cf = settings.cart_fs_free | times: 100 -%}
                {%- if settings.cart_fs_discount_type == 'percent' -%}
                    {%- assign discount = settings.cart_fs_discount | append: '%' -%}
                {%- else -%}
                    {%- assign discount = settings.cart_fs_discount | times: 100 | money -%}
                {%- endif -%}
                {%- if cart.original_total_price >= cf -%}
                    {% capture fs_discount_amount %}
                        <span class="c-brown-10 bold-700">{{ discount }}</span>
                    {% endcapture %}
                    <p class="{{ text_class }}">{{ settings.cart_fs_after | replace: "[discount]", fs_discount_amount }}</p>
                {%- else -%}
                    {%- assign current = cf | minus: cart.original_total_price | money %}
                    {% capture fs_min_amount %}
                        <span class="c-brown-10 bold-700">{{ current }}</span>
                    {% endcapture %}
                    {% capture fs_discount_amount %}
                        <span class="c-brown-10 bold-700">{{ discount }}</span>
                    {% endcapture %}
                    <p class="{{ text_class }}">{{ settings.cart_fs_before | replace:"[amount]", fs_min_amount | replace: "[discount]",  fs_discount_amount  }}</p>
                {%- endif -%}
            {%- else -%}
                {%- assign cf = settings.cart_fs_free2 | times: 100 -%}
                {%- if settings.cart_fs_discount_type2 == 'percent' -%}
                    {%- assign discount = settings.cart_fs_discount2 | append: '%' -%}
                {%- else -%}
                    {%- assign discount = settings.cart_fs_discount2 | times: 100 | money -%}
                {%- endif -%}
                {%- if cart.original_total_price >= cf -%}
                    {% capture fs_discount_amount %}
                        <span class="c-brown-10 bold-700">{{ discount }}</span>
                    {% endcapture %}
                    <p class="{{ text_class }}">{{ settings.cart_fs_after2 | replace: "[discount]", fs_discount_amount }}</p>
                {%- else -%}
                    {%- assign current = cf | minus: cart.original_total_price | money %}
                    {% capture fs_min_amount %}
                        <span class="c-brown-10 bold-700">{{ current }}</span>
                    {% endcapture %}
                    {% capture fs_discount_amount %}
                        <span class="c-brown-10 bold-700">{{ discount }}</span>
                    {% endcapture %}
                    <p class="{{ text_class }}">{{ settings.cart_fs_before2 | replace:"[amount]", fs_min_amount | replace: "[discount]",  fs_discount_amount  }}</p>
                {%- endif -%}
            {%- endif -%}            

            <div class="free-shipping-line__inner w-full bg-brown-20 mt-8 overflow">
                <div class="free-shipping-line__inner bg-orange" style="width:{{ cart.original_total_price | divided_by:settings.cart_fs_free2 | round: 2 }}%;"></div>
            </div>
        </div>
    </div>
{% endif %}
