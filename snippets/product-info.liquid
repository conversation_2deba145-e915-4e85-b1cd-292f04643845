{% liquid
	assign variant = product.selected_or_first_available_variant
	assign qty = variant.inventory_quantity
%}
<div class="product-info d-mb-20">
	{%- render 'breadcrumbs', 
		wrapper_class: 'hide-m d-flex mb-20 d-mb-24'
		with_container: 'no',
		top_padding: 'pt-0 d-pt-16',
		bottom_padding: 'pb-0',
		text_class: 'p3 c-brown-80'
	-%}

	<h1 class="product-title h5">{{ product.title }}</h1>

	<!-- Start of Judge.me code --> 
	{% comment %} <div class="jdgm-widget jdgm-preview-badge mt-12" data-id='{{ product.id }}'> 
		{{ product.metafields.judgeme.badge }} 
	</div>  {% endcomment %}
	<!-- End of Judge.me code -->
	{% liquid
	assign compare_at_price = variant.compare_at_price 
	assign price = variant.price 

	assign on_sale = false
	if product.tags contains 'Sale'
		assign on_sale = true
	endif

	if compare_at_price > price
		assign on_sale = true
	endif
%}
	
	<div class="price flex jc-start ai-center mt-8 d-mt-12 pb-16 d-pb-20 bb-1 b-border">
		{% comment %} <span class="from-price p1 bold-500 mr-12">{{ 'products.product.from' | t }}</span> {% endcomment %}
		<span class="applied-price p1 bold-500  {% if on_sale %}c-black {% else %}c-black{%  endif %}">{{ variant.price |  money_with_currency }}</span>
		{% if on_sale %}
			<span class="striked striked-price p1 c-grey ml-4 mr-4">{{ variant.compare_at_price | money_with_currency }}</span>
			{% assign percentage = variant.compare_at_price | minus: variant.price | times: 100 | divided_by: variant.compare_at_price %}
			<span class="p4 c-black bg-violet rounded-4 pl-6 pr-6">{{ percentage }}% off</span>
		{% endif %}
	</div>
</div>