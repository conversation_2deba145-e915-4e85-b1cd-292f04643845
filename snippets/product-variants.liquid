{%- assign variant = product.selected_or_first_available_variant -%}
<div id="product-form" class="product-form pt-16 d-pt-0">
  <form onsubmit="productFormVue.addToCart(event)" class="product-form-wrapper" action="/cart/add">
    {% comment %} Variety options from other products {% endcomment %}
    {%- for collection in product.collections -%}
      {%- if collection.template_suffix == 'set' -%}
        {%- assign variety_coll = collection -%}
        {% break %}
      {%- endif -%}
    {%- endfor -%}

    {%- if variety_coll != blank -%}
      {% capture variety_options %} 
			<div>
				<div class="variety-opt-wrapper w-full relative">
					<div class="mb-8">
						<span class="p2 c-grey">{{ variety_coll.metafields.custom.variety_title }}</span> <span class="p2 c-black">{{ product.metafields.custom.variety_name }}</span>
					</div>
							
					<div class="product-variety-opt  hide-m d-flex colgap-4 rowgap-8 flex-wrap">
						{%- if variety_coll.metafields.custom.variety_description != blank -%}
							<div class="variety-desc p3 c-dark-grey mb-12">{{ variety_coll.metafields.custom.variety_description }}</div>
						{%- endif -%}

						{%- if variety_coll.metafields.custom.variety_image != blank -%}
							<div class="variety-image mb-12">
								{% render 'global-image-wrapper', image: variety_coll.metafields.custom.variety_image, image_alt: variety_coll.title, additional_class: 'no-bg', preload: true %}
							</div>
						{%- endif -%}
            {%- assign variety_list_values = '' -%}
            {%- assign variety_option_values = '' -%}
            {%- assign variety_handles = '' -%}
            {%- assign variety_available = '' -%}
            {%- assign variety_selected_is_oos = false -%}
              {%- for variety_prod in variety_coll.products -%}
                {% capture variety_list_values %}{{ variety_list_values }},{{ variety_prod.metafields.custom.variety_name | escape }}{% endcapture %}
                {% capture variety_option_values %}{{ variety_option_values }},{{ variety_prod.metafields.custom.variety_name | escape }}{% endcapture %}
                {% capture variety_handles %}{{ variety_handles }},{{ variety_prod.handle | escape }}{% endcapture %}
                {% assign prod_has_qty = false %}
                {% for v in variety_prod.variants %}
                  {% if v.inventory_quantity > 0 %}
                    {% assign prod_has_qty = true %}
                  {% endif %}
                {% endfor %}
                {% capture variety_available %}{{ variety_available }},{{ prod_has_qty }}{% endcapture %}
                  {% if product.handle == variety_prod.handle %}
                  {% assign selected_variety_value = variety_prod.metafields.custom.variety_name %}
                  {% if prod_has_qty == false %}
                    {% assign variety_selected_is_oos = true %}
                  {% endif %}
                {% endif %}
                {%- endfor -%} 
						{%- assign variety_list_values = variety_list_values | remove_first: ',' | split: ',' -%}
						{%- assign variety_option_values = variety_option_values | remove_first: ',' | split: ',' -%}
						{%- assign variety_handles = variety_handles | remove_first: ',' | split: ',' -%}
						{%- assign variety_available = variety_available | remove_first: ',' | split: ',' -%}
					
							
					{%- for variety_prod in variety_coll.products -%}
					  {% assign has_any_qty = false %}
					  {% for v in variety_prod.variants %}
					    {% if v.inventory_quantity > 0 %}
					      {% assign has_any_qty = true %}
					    {% endif %}
					  {% endfor %}
						<a 
								href="{{ variety_prod.url }}" 
								onclick="productFormVue.changeVariety(event, this);" 
								title="{{ variety_prod.title | escape }}" 
						    class="each color-option {% if product.handle == variety_prod.handle %}active{% endif %} {% unless has_any_qty %}oos{% endunless %}"
								data-product-handle="{{ variety_prod.handle }}"
								data-product="{{ variety_prod | json | escape }}"
                data-index="{{ forloop.index0 }}"
							>
								<div class="flex ai-center color-circle-wrp">
									{%- assign class_name = variety_prod.metafields.custom.variety_name | handle -%}
										<span class="color-circle block b-1 b-border swatch-{{ class_name }}"></span>
								</div>
							</a>
						{%- endfor -%} 


					</div>
					
					<!-- Collapsible toggle button for desktop only -->
					<button 
					type="button" 
					class="variety-toggle-btn mt-4 hide-m" 
					onclick="productFormVue.toggleVarietySwatches()"
				>
					<span class="toggle-text p4 c-grey pt-2 pb-4">+ {{ 'products.product.more_colours' | t }}</span>
				</button>
					
				</div>

				{% comment %} Mobile variety options dropdown {% endcomment %}
				{%- render 'global-select',
					id: 'variety-options-dropdown',
					default_value: selected_variety_value,
					default_text: selected_variety_value,
          variety_selected_is_oos: variety_selected_is_oos,
					selected_inline: false,
					values: variety_list_values,
					option_values: variety_option_values,
					variety_handles: variety_handles,
					variety_available: variety_available,
					global_select_class: 'pdp-variety-select pl-16 pr-16 hide-d',
					label_class: 'p2 c-black mr-4',
					selected_text_class: 'p2 flex ai-center colgap-8',
					selected_option_value: selected_variety_value,
					list_option_class: 'p2',
					onchange: 'productFormVue.changeVariety(event, this, true)',
					custom_icon: settings.icon_select,
					show_color_circles: true
				  -%}
		    </div>

			{% endcapture %}
    {%- endif -%}
    {% comment %} End Variety options from other products {% endcomment %}

    {%- unless product.has_only_default_variant -%}
      <div class="flex flex-column rowgap-12 d-rowgap-16 pdpr-options pdpr-option-all">
        {%- assign variety_options_rendered = false -%}
        {%- for opt_w_values in product.options_with_values -%}
          {%- assign option_num = forloop.index -%}
          {%- assign opt_index = forloop.index0 -%}

          {% assign vars_for_swatch = settings.product_variants_for_color_swatch %}

          {%- if vars_for_swatch contains opt_w_values.name -%}
            {% comment %} For option Color {% endcomment %}
            <div class="option-wrapper product-main" data-option-index="{{ opt_index }}">
              <div class="mb-8">
                <span class="p2 c-grey">
                  {{- opt_w_values.name }}
                  <span class="selected-option-{{ option_num }} p2 c-black"></span
                ></span>
              </div>
              <div class="flex jc-start ai-start colgap-8 rowgap-8 flex-wrap relative">
                {%- for opt_value in opt_w_values.values -%}
                  {%- assign option_name = 'product-option-' | append: opt_w_values.name -%}
                  {%- assign class_name = opt_value | handle -%}
                  {%- assign radio_class = 'prod-option' | append: option_num -%}
                  {% comment %}
                    <label for="{{ option_name }}-{{ forloop.index }}" class="color-option cursor {{ class_name }} mr-15" :class="{'active': options[{{ opt_index }}] == optValues[{{ opt_index }}][{{ forloop.index0 }}]}">
                    	<span class="p2">{{ opt_value }}</span>
                    	<input type="radio" name="{{ option_name }}" id="{{ option_name }}-{{ forloop.index }}" value="{{ opt_value }}" v-model="options[{{ opt_index }}]">
                    </label>
                  {% endcomment %}

                  <label class="radio-container color-option cursor" data-value="{{ opt_value | escape }}">
                    <input
                      type="radio"
                      name="{{ option_name }}"
                      id="{{ option_name }}-{{ forloop.index }}"
                      class="{{ radio_class }}"
                      value="{{ opt_value }}"
                      onchange="productFormVue.variantChange();"
                      {% if variant.options[opt_index] == opt_value %}
                        checked
                      {% endif %}
                    >
                    <div class="flex ai-center color-circle-wrp">
                      <span class="color-circle block b-1 b-border swatch-{{ class_name }}"></span>
                      {% comment %} <p class="p2 lh-0 ml-12">{{ opt_value }}</p> {% endcomment %}
                    </div>
                  </label>
                {%- endfor -%}
              </div>
            </div>
          {%- endif -%}

          {% if variety_coll != blank and variety_options_rendered == false %}
            {{ variety_options }}
            {%- assign variety_options_rendered = true -%}
          {% endif %}

          {%- unless vars_for_swatch contains opt_w_values.name -%}
            {% comment %} For other options {% endcomment %}
            {%- assign list_values = '' -%}
            {%- assign opt_values = '' -%}
            {%- for opt_value in opt_w_values.values -%}
              {% capture list_values %}{{ list_values }},{{ opt_value }}{% endcapture %}
              {% capture opt_values %}{{ opt_values }},{{ opt_value | escape }}{% endcapture %}
            {%- endfor -%}
            {%- assign list_values = list_values | remove_first: ',' | split: ',' -%}
            {%- assign opt_values = opt_values | remove_first: ',' | split: ',' -%}

            {%- assign option_id = 'select-' | append: product.id | append: '-' | append: forloop.index -%}
            {%- assign vue_model = 'options[' | append: opt_index | append: ']' -%}
            <div
              class="option-wrapper product-main flex flex-column {% if product.variants.size == 1 and product.variants[0].title == "Default Title" %}hide-m{% else %}flex jc-between{% endif %}"
              data-option-index="{{ opt_index }}"
            >
              <div class="flex ai-center colgap-12 mb-8">
                <div class="p2 c-grey">
                  {{ opt_w_values.name }}
                  {% unless opt_w_values.name == 'Amount' %}
                    <span class="selected-option-{{ option_num }} p2 c-black"></span>
                  {% endunless %}
                </div>
                {% comment %}
                  {%- if opt_w_values.name == 'Size' and product.metafields.custom.size_guide_tab_title != blank -%}
                  	<button onclick="pdpDetailsDrawer.handleShowDrawer('{{ product.metafields.custom.size_guide_tab_title | handleize }}');" type="button" class="p3 underline">{{ 'products.product.size_guide' | t }}</button>
                  {%- endif -%}
                {% endcomment %}
              </div>
              <div class="flex jc-start ai-start grid-cols flex-wrap relative">
                {%- for opt_value in opt_w_values.values -%}
                  {%- assign option_name = 'product-option-' | append: opt_w_values.name -%}
                  {%- assign radio_class = 'prod-option' | append: option_num -%}
                  <label class="radio-container general-option cursor" data-value="{{ opt_value | escape }}">
                    <input
                      type="radio"
                      name="{{ option_name }}"
                      id="{{ option_name }}-{{ forloop.index }}"
                      class="{{ radio_class }}"
                      value="{{ opt_value | escape }}"
                      onchange="productFormVue.variantChange();"
                      {% if variant.options[opt_index] == opt_value %}
                        checked
                      {% endif %}
                    >
                    <span class="text block p2 rounded-4">{{ opt_value }}</span>
                  </label>
                {%- endfor -%}
              </div>

              {% comment %}
                {% assign class = 'prod-option' | append: option_num %}
                {%- render 'global-select',
                	default_text: variant.options[opt_index],
                	default_value: variant.options[opt_index],
                	values: list_values,
                	option_values: opt_values,
                	id: option_id,
                	field_name: option_id,
                	selected_text_class: 'p3',
                	list_option_class: 'p3',
                	class: class,
                	onchange: 'productFormVue.variantChange();'
                -%}
              {% endcomment %}
            </div>
          {%- endunless -%}
        {%- endfor -%}
      </div>
    {%- endunless -%}

    {% # Gift card message %}
    {%- if product.gift_card? -%}
      <div id="gift-card-fields" class="mt-24">
        <p class="p3 bold-600 c-brown mb-8">{{ 'products.product.include_gift_message' | t }}</p>
        <input type="hidden" name="properties[__shopify_send_gift_card_to_recipient]" value="on">

        <div class="mb-12 d-mb-16">
          <div
            class="relative global-input"
            :class="{ 'global-input-error': giftCardForm.recEmail && giftCardForm.recEmailInvalid }"
            v-cloak
          >
            <input
              type="email"
              id="gc-recipient-email"
              name="properties[Recipient email]"
              class="p2 c-brown"
              placeholder="{{ 'products.product.recipient_email' | t }}"
              v-model="giftCardForm.recEmail"
              @blur="validateEmail"
            >
          </div>
          <p class="p4 c-brown-80 mt-8 mb-8">{{ 'products.product.only_one_email_per_gift_card' | t }}</p>

          <div v-if="giftCardForm.recEmail && giftCardForm.recEmailInvalid" class="mt-4 p3 c-red global-error-msg">
            {{ 'products.product.error_valid_email' | t }}
          </div>
          <div v-if="giftCardForm.submitted && !giftCardForm.recEmail" class="mt-4 p3 c-red global-error-msg">
            {{ 'products.product.error_recipient_email' | t }}
          </div>
        </div>

        <div class="mb-12 d-mb-16">
          <div
            class="relative global-input"
            :class="{ 'global-input-error': giftCardForm.submitted && !giftCardForm.recName }"
            v-cloak
          >
            <input
              type="text"
              id="gc-recipient-name"
              name="properties[Recipient name]"
              class="p2 c-brown"
              placeholder="{{ 'products.product.recipient_name' | t }}"
              v-model="giftCardForm.recName"
            >
          </div>
          <div v-if="giftCardForm.submitted && !giftCardForm.recName" class="mt-4 p3 c-red global-error-msg">
            {{ 'products.product.error_recipient_name' | t }}
          </div>
        </div>

        <div class="mb-12 d-mb-16">
          <div
            class="relative global-input"
            :class="{ 'global-input-error': giftCardForm.submitted && !giftCardForm.senderName }"
            v-cloak
          >
            <input
              type="text"
              id="gc-sender-name"
              name="properties[Sender name]"
              class="p2 c-brown"
              placeholder="{{ 'products.product.sender_name' | t }}"
              v-model="giftCardForm.senderName"
            >
          </div>
          <div v-if="giftCardForm.submitted && !giftCardForm.senderName" class="mt-4 p3 c-red global-error-msg">
            {{ 'products.product.error_sender_name' | t }}
          </div>
        </div>

        <div class="mb-24">
          <div
            class="relative pb-20 global-input"
            :class="{ 'global-input-error': giftCardForm.submitted && !giftCardForm.message }"
            v-cloak
          >
            <textarea
              id="gc-msg"
              name="properties[Message]"
              class="p2 c-brown"
              rows="4"
              maxlength="200"
              placeholder="{{ 'products.product.message' | t }}"
              v-model="giftCardForm.message"
            ></textarea>
            <div
              class="absolute max-chars-count p4 c-brown-80 right-12 bottom-12"
              :class="{'bottom-12': !giftCardForm.submitted && !giftCardForm.message, 'bottom-0': giftCardForm.message, 'bottom-0': giftCardForm.submitted && !giftCardForm.message}"
            >
              <span class="chars-count p4 c-brown-80">${giftCardForm.message.length}</span>/<span class="p4 c-brown-80"
                >200</span
              >
            </div>
          </div>
          <div v-if="giftCardForm.submitted && !giftCardForm.message" class="mt-4 p3 c-red global-error-msg">
            {{ 'products.product.error_message' | t }}
          </div>
        </div>

        <div class="relative mb-20 d-mb-24" v-cloak>
          <p class="p3 bold-600 c-brown mb-8">{{ 'products.product.send_date' | t }}</p>
          <div>
            <div
              class="relative global-input"
              :class="{ 'global-input-error': giftCardForm.submitted && !giftCardForm.sendDate }"
              v-cloak
            >
              <input
                type="text"
                id="gc-send-date"
                name="properties[Send on]"
                class="p2 c-brown w-full gc-send-date pointer"
                placeholder="DD / MM / YYYY"
                v-model="giftCardForm.sendDate"
              >
              {%- if settings.icon_calendar -%}
                <div
                  class="input-icon absolute flex ai-center h-full top-0 right-16 pl-20 pointer"
                  style="pointer-events: none;"
                >
                  {{ settings.icon_calendar }}
                </div>
              {%- endif -%}
              <div v-if="giftCardForm.submitted && !giftCardForm.sendDate" class="mt-10 p3 c-red global-error-msg">
                {{ 'products.product.error_send_date' | t }}
              </div>
            </div>
            <div class="p4 c-brown-80 mt-8">{{ 'products.product.send_date_info' | t }}</div>
          </div>
        </div>
      </div>
    {%- endif -%}
    {% # END Gift card message %}

    {% comment %} <p class="c-black p2">{{ 'products.product.item_code' | t }}: <span class="variant-sku">{{ variant.sku }}</span></p> {% endcomment %}

    {% # ATC %}
    <div class="atc-wrapper w-full flex ai-strecth colgap-12 mt-24 d-mt-20">
      {%- if product.metafields.custom.show_add_to_cart_note != blank -%}
        <p class="p3 c-dark-grey mb-12">{{ product.metafields.custom.show_add_to_cart_note }}</p>
      {%- endif -%}

      {%- if product.metafields.custom.show_add_to_cart_note2 != blank -%}
        <p class="p3 c-dark-grey mb-20 d-mb-24">{{ product.metafields.custom.show_add_to_cart_note2 }}</p>
      {%- endif -%}

      {%- if product.metafields.custom.show_add_to_cart_note3 != blank -%}
        <p class="p3 c-dark-grey mb-20 d-mb-24">{{ product.metafields.custom.show_add_to_cart_note3 }}</p>
      {%- endif -%}
      <div class="global-form-qty pdp {% unless variant.available %}hide-m{% endunless %} {% if product.gift_card? %}hide-m{% endif %}">
        <button type="button" class="toggle global-minus" :class="{'disabled': qty == 1}">
          {{ settings.icon_minus }}
        </button>
        <input
          id="qty-input"
          type="number"
          class="global-qty-text p2 bold-600"
          oninput="productFormVue.setQty(this.value);"
          name="product-quantity"
          value="1"
          min="1"
          {% if variant.inventory_management == 'shopify' %}
            max="{{ variant.inventory_quantity }}"
          {% endif %}
        >
        <button type="button" class="toggle global-plus">{{ settings.icon_plus }}</button>
      </div>

      {%- if product.tags contains 'Preorder' -%}
        <div class="pdp-atc-wrapper">
          <button
            class="pdp-atc w-full btn2 no-icon"
            type="submit"
            id="atc-btn"
            ref="atc-btn"
            data-variant-id="{{ variant.id }}"
          >
            {{ 'products.product.preorder' | t }}
          </button>
        </div>
      {%- else -%}
        <div class="pdp-atc-wrapper in-stock-wrapper {% unless variant.available %}hide-m{% endunless %}">
          <button
            class="pdp-atc w-full btn2 no-icon"
            type="submit"
            id="atc-btn"
            ref="atc-btn"
            data-variant-id="{{ variant.id }}"
          >
            {{ 'products.product.add_to_bag' | t }} — {{ variant.price | money }}
          </button>
        </div>
        <div class="pdp-atc-wrapper oos-wrapper {% if variant.available %}hide-m{% endif %}">
          <button class="pdp-atc w-full btn2 no-icon disabled" type="button" disabled>
            {{ 'products.product.oos' | t }}
          </button>
        </div>
      {%- endif -%}
    </div>
    {% # ATC %}
  </form>

  {% render 'product-bottom-info', product: product %}
</div>
