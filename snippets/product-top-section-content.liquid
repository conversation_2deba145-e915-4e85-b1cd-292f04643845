{%- assign variant = product.selected_or_first_available_variant -%}
{%- render 'breadcrumbs', 
wrapper_class: 'hide-d'
with_container: 'yes',
top_padding: 'pt-12',
bottom_padding: 'pb-12',
text_class: 'p5 c-grey'
-%}


  <div class="cont-wrapper">
    <div class="d-flex">
      <div class="left-side col-12 pl-0 pr-0 col-d-8 col-hd-9 d-pl-0 d-pr-0 hd-pl-0 hd-pr-0">
        <div class="left-side-content">
          {%- render 'product-images' -%}
        </div>
      </div>


      <div class="container col-d-4 d-ml-0 d-mr-0 d-pl-32 d-pr-32 hd-pl-32 hd-pr-32 col-hd-3">
      <div class="right-side col-12 pt-28 pl-0 pr-0 d-pt-0 ">
        <div class="right-side-inner h-full">
          <div class="right-side-content">
            {%- render 'product-info', section: section -%}
            {%- render 'product-variants', section: section -%}
          </div>
        </div>
      </div>
      </div>
    </div>
  </div>

{% # sticky atc %}

<div class="sticky-atc bg-white w-full zi-2 d-flex d-flex-column d-rowgap-8 d-p-12">
    <div class="hide-m d-flex d-colgap-12 ">
        <div class="image-con rounded-6">
            {% assign image_alt = product.title | escape %}
            {% render 'global-image-wrapper',
                image: product.featured_image,
                image_alt: image_alt
            %}
        </div>
        <div class="flex flex-column ai-start jc-center">
          <div class="text-con ">
            <h3 class="h8 c-black">{{ product.title }}</h3>
            {% comment %} {%- if product.metafields.info.tagline != blank -%}
              <p class="p3">{{ product.metafields.info.tagline }}</p>
            {%- endif -%} {% endcomment %}
          </div>
          <div class="price">
            {% comment %} <span class="from-price p1 mr-10">{{ 'products.product.from' | t }}</span> {% endcomment %}
            <span class="applied-price p2 c-black">{{ variant.price | money_without_trailing_zeros }}</span>
            <span class="striked striked-price p2 c-grey ml-4 {% if variant.compare_at_price <= variant.price %}hide-m{%  endif %}">
              {{- variant.compare_at_price | money_without_trailing_zeros -}}
            </span>
          </div>
        </div>

          {% comment %} <button
            type="button"
            id="sticky-atc-wishlist-btn"
            class="btn3 no-icon wishlist-btn swym-button swym-add-to-wishlist"
            data-with-epi="true"
            data-swaction="addToWishlist"
            data-product-id="{{ product.id }}"
            data-variant-id="{{ product.variants[0].id }}"
            data-product-url="{{ shop.url }}{{ product.url }}"
          ><span class="text">{{ 'products.product.add_to_wishlist' | t }}</span></button> {% endcomment %}
      </div>
    <button class="flex-1 btn2 no-icon w-full" onclick="$360.scrollTo('#product-form', 150);">
      {{ 'products.product.select_options' | t }}
    </button>
</div>


{% # END sticky atc %}

{% comment %} Product data {% endcomment %}
{%- assign prices = '' -%}
{%- assign v_settings_all = '' -%}
{%- for variant in product.variants -%}
  {%- assign compare_at_price = variant.compare_at_price -%}
  {%- if compare_at_price == blank -%}
    {%- assign compare_at_price = 'null' -%}
  {%- endif -%}
  {%- capture prices %}{{ prices }}|{{ variant.id }}:{{ variant.price }}:{{ compare_at_price }}{% endcapture %}
  {%- capture v_qty %}{{ v_qty }}|{{ variant.id }}::{{ variant.inventory_quantity }}::{{ variant.inventory_policy }}::{{ variant.inventory_management }}{% endcapture %}

  {%- capture v_setting_item -%}{"id":{{ variant.id }}, "settings": {{ variant.metafields.shipping | json }}}{%- endcapture -%}
  {%- assign v_settings_all = v_settings_all | append: v_setting_item | append: ',' -%}
{%- endfor -%}
{%- assign prices = prices | remove_first: '|' -%}
{%- assign v_qty = v_qty | remove_first: '|' -%}

{%- assign opt_val_arr = '' -%}
{%- for opt_w_values in product.options_with_values -%}
  {%- assign opt_val_string = '' -%}
  {%- for opt_value in opt_w_values.values -%}
    {% capture opt_val_string %}{{ opt_val_string }},"{{ opt_value | escape }}"{% endcapture %}
  {%- endfor -%}
  {%- assign opt_val_string = opt_val_string | remove_first: ',' -%}
  {% capture opt_val_single_arr %}[{{ opt_val_string }}]{% endcapture %}
  {% capture opt_val_arr %}{{ opt_val_arr }},{{ opt_val_single_arr }}{% endcapture %}
{%- endfor -%}
{%- assign opt_val_arr = opt_val_arr | remove_first: ',' -%}
{% capture opt_values %}[{{ opt_val_arr }}]{% endcapture %}

<div class="product-data-json hide-m">{{ product | json }}</div>
<div class="product-data-prices hide-m">{{ prices }}</div>
<div class="product-data-v-qty hide-m">{{ v_qty }}</div>
<div class="product-data-opt-values hide-m">{{ opt_values }}</div>
{% comment %} End Product data {% endcomment %}
