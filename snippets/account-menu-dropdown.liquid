<div class="sticky-below-header zi-1 hide-d">
    <style>
        .account-menu-dropdown .global-select-div .options {
            outline-color: var(--brown-20);
        }
        .account-menu-dropdown .global-select-div .option {
            padding: 16px 20px;
        }
        @media only screen and (min-width:600px) {
            .account-menu-dropdown .global-select-div .option {
                padding: 16px 38px;
            }
        }
    </style>
    <div class="account-menu-dropdown bg-off-white">
        {%- assign active_text = 'Orders' -%}
        {%- assign active_value = 'history' -%}
        {%- if request.path contains 'addresses' -%}
            {%- assign active_text = 'Addresses' -%}
            {%- assign active_value = 'addresses' -%}
        {%- endif -%}

        {%- assign values = 'Orders,Addresses,Log Out' | split: ',' -%}
        {%- assign option_values = 'history,addresses,logout' | split: ',' -%}

        {%- if use_vue -%}
            <component :is="'style'" type="text/css">
            </component>
        {%- else -%}
            <style>
            </style>
        {%- endif -%}
        {%- render 'global-select',
            id: 'account_link',
            field_name: 'account_link',
            class: 'account-link',
            selected_text_class: 'p1 bold-700',
            list_option_class: 'p1',
            default_text: active_text,
            default_value: active_value,
            values: values,
            option_values: option_values,
            onchange: '$360.accountRedirect(this)',
            global_select_class: 'pt-16 pb-16 b-0'
        -%}
    </div>
</div>