<style>
{%- paginate shop.metaobjects["shopify--color-pattern"].values by 100 -%}
    {%- for color in shop.metaobjects["shopify--color-pattern"].values -%}
        {%- assign color_name = color.label | handleize -%}
        {%- assign color_hex = color.color -%}
        {%- assign color_image = color.image -%}
        .swatch-{{ color_name }}{
            {%- unless color_hex == blank -%} background-color: {{ color_hex }};{%- endunless -%}
            {%- unless color_image == blank -%}background-image: url({{ color_image | image_url }});background-size:contain;{%- endunless -%}
        }
        .swatch.--pc.circle {
            width: 22px;
            height: 22px;
            padding: 3px;
            border-radius: 100%;
            border: 1px solid transparent;
            background-clip: content-box;
            box-sizing: border-box;
        }
        .swatch.--pc.circle.selected {
            border: 1px solid var(--black);
        }
        .swatch.--pc.circle.selected.white {
            border: 1px solid var(--white);
        }
        .swatch.--pc.circle.oos {
            position: relative;
            overflow: hidden;
        }
        .swatch.--pc.circle.oos::after {
            content: "";
            position: absolute;
            top: calc(50% - 1px);
            left: 3px;
            width: 16px;
            height: 1px;
            background: var(--white);
            transform: rotate(-45deg);
            transform-origin: center;
            pointer-events: none;
        }
        @media screen and (min-width: 1024px) {
            .swatch.--pc.circle {
                width: 24px;
                height: 24px;
            }            
        }
    {%- endfor -%}
{%- endpaginate -%}
</style>