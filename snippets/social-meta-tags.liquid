<!-- /snippets/social-meta-tags.liquid -->
{%- assign og_title = page_title -%}
{%- assign og_url = canonical_url -%}
{%- assign og_type = 'website' -%}
{%- assign og_description = page_description | default: shop.description | default: shop.name -%}
{% if settings.share_image %}
  {%- capture og_image_tags -%}<meta property="og:image" content="http:{{ settings.share_image | image_url: width: '1200' }}">{%- endcapture -%}
  {%- capture og_image_secure_url_tags -%}<meta property="og:image:secure_url" content="https:{{ settings.share_image | image_url: width: '1200' }}">{%- endcapture -%}
{% endif %}

{% comment %} Template specific overides {% endcomment %}
{% if template.name == 'product' %}
  {%- assign og_title = product.title | strip_html -%}
  {%- assign og_type = 'product' -%}
  {% if product.images.size > 0 %}
    {%- capture og_image_tags -%}{% for image in product.images limit:3 -%}<meta property="og:image" content="http:{{ page_image | image_url: width: '1200' }}">{% endfor -%}{% endcapture -%}
    {%- capture og_image_secure_url_tags -%}{% for image in product.images limit:3 -%}<meta property="og:image:secure_url" content="https:{{ page_image | image_url: width: '1200' }}">{% endfor -%}{% endcapture -%}
  {% endif %}

{% elsif template.name == 'article' %}
  {%- assign og_title = article.title | strip_html -%}
  {%- assign og_type = 'article' -%}
  {%- assign og_description = article.excerpt_or_content | strip_html -%}
  {% if article.image %}
    {%- capture og_image_tags -%}<meta property="og:image" content="http:{{ page_image | image_url: width: '1200' }}">{%- endcapture -%}
    {%- capture og_image_secure_url_tags -%}<meta property="og:image:secure_url" content="https:{{ page_image | image_url: width: '1200' }}">{%- endcapture -%}
  {% endif %}

{% elsif template.name == 'collection' %}
  {%- assign og_title = collection.title | strip_html -%}
  {%- assign og_type = 'product.group' -%}
  {% if collection.image %}
    {%- capture og_image_tags -%}<meta property="og:image" content="http:{{ page_image | image_url: width: '1200' }}">{%- endcapture -%}
    {%- capture og_image_secure_url_tags -%}<meta property="og:image:secure_url" content="https:{{ page_image | image_url: width: '1200' }}">{%- endcapture -%}
  {% endif %}

{% elsif template.name == 'password' %}
  {%- assign og_title = shop.name -%}
  {%- assign og_url = shop.url -%}
  {%- assign og_description = shop.description | default: shop.name -%}
{% endif %}

<meta property="og:site_name" content="{{ shop.name }}">
<meta property="og:url" content="{{ og_url }}">
<meta property="og:title" content="{{ og_title }}">
<meta property="og:type" content="{{ og_type }}">
<meta property="og:description" content="{{ og_description }}">
{% if template.name == 'product' %}
  <meta property="og:price:amount" content="{{ product.price | money_without_currency | strip_html }}">
  <meta property="og:price:currency" content="{{ shop.currency }}">
{% endif %}
{{ og_image_tags }}
{{ og_image_secure_url_tags }}

{% unless settings.social_twitter_link == blank %}
  <meta name="twitter:site" content="{{ settings.social_twitter_link | split: 'twitter.com/' | last | prepend: '@' }}">
{% endunless %}
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ og_title }}">
<meta name="twitter:description" content="{{ og_description }}">
