{% comment %}
Snippets to showing color swatches
Input:
- product: Object - product object
- type: String - swatches/galleries
{% endcomment %}

{%- if type == blank -%}
    {%- assign type = 'swatches' -%}
{%- endif -%}
{%- assign variant = product.selected_or_first_available_variant -%}
{%- assign max_number = 5 -%}
{%- assign more_number = 0 -%}
{%- assign count_number = 0 -%}

{%- #Check collection set first -%}
{%- for collection in product.collections -%}
    {%- if collection.template_suffix == 'set' -%}
        {%- assign variety_coll = collection -%}
        {% break %}
    {%- endif -%}
{%- endfor -%}
{%- #END check collection -%}

{%- if type == 'swatches' -%}
    {%- if variety_coll != blank -%}
        {% # Collection set %}
        {%- assign more_number = variety_coll.products.size | minus: max_number -%}
        <ul class="relative flex jc-start ai-stretch colgap-4">
            {%- assign pd_images = product.media | where: 'media_type', 'image' -%}
            {%- assign primary_def = pd_images[0] | image_url | escape -%}
            {%- assign mood_def = pd_images[1] | image_url | escape -%}
            {%- assign percentage = variant.compare_at_price | minus: variant.price | times: 100 | divided_by: variant.compare_at_price -%}
            {%- if percentage > 0 -%}
                {%- assign percentage_text = percentage | percent_with_symbol | append: '% off' -%}                            
            {%- endif -%}
            <li class="cursor swatch swatch-{{ product.metafields.custom.variety_name | handleize | escape }} --pc circle selected {% if product.available == false %}oos{% endif %}"
                data-title="{{ product.title | escape }}"
                data-price-varies="{{ product.price_varies }}"
                data-price="{{ variant.price | escape }}"
                data-price-compare="{{ variant.compare_at_price }}"
                data-discount="{{ percentage }}"
                data-discount-text="{{ percentage_text }}"
                data-url="{{ product.url }}"
                data-handle="{{ product.handle | escape }}" 
                data-primary="{{ pd_images[0] | image_url | escape }}"
                data-mood="{{ pd_images[1] | image_url | escape}}"
                data-primary-def="{{ primary_def }}"
                data-mood-def="{{ mood_def }}"
                data-color="{{ product.metafields.custom.variety_name | handleize | escape }}"
                onclick="$360.selectProductCard(this)"
            ></li>
            {%- assign count_number = count_number | plus: 1 -%}
            {%- for vr_prod in variety_coll.products -%}
                {% unless product.handle == vr_prod.handle %}
                    {%- assign vr_handle = vr_prod.metafields.custom.variety_name | handleize | escape -%}
                    {%- assign pd_images = vr_prod.media | where: 'media_type', 'image' -%}
                    {%- assign vr_variant = vr_prod.selected_or_first_available_variant -%}
                    {%- assign percentage = vr_variant.compare_at_price | minus: vr_variant.price | times: 100 | divided_by: vr_variant.compare_at_price -%}
                    {%- if percentage > 0 -%}
                        {%- assign percentage_text = percentage | percent_with_symbol | append: '% off' -%}                            
                    {%- endif -%}

                    <li class="cursor swatch swatch-{{ vr_handle }} --pc circle {% if vr_prod.available == false %}oos{% endif %}"
                        data-title="{{ vr_prod.title | escape }}"
                        data-price-varies="{{ vr_prod.price_varies }}"
                        data-price="{{ vr_variant.price }}"
                        data-price-compare="{{ vr_variant.compare_at_price }}"
                        data-discount="{{ percentage }}"
                        data-discount-text="{{ percentage_text }}"
                        data-url="{{ vr_prod.url }}"
                        data-handle="{{ vr_prod.handle | escape }}"
                        data-primary="{{ pd_images[0] | image_url | escape }}"
                        data-mood="{{ pd_images[1] | image_url | escape }}"
                        data-primary-def="{{ primary_def }}"
                        data-mood-def="{{ mood_def }}"
                        data-color="{{ vr_handle }}"
                        onclick="$360.selectProductCard(this)"
                    ></li>
                    {%- assign count_number = count_number | plus: 1 -%}
                    {%- if count_number >= max_number -%}
                        {% break %}
                    {%- endif -%}                
                {% endunless %}
            {%- endfor -%}
        </ul>
        {%- if more_number > 0 -%}
            <span class="more-info ml-5 p4 tl">+ {{ more_number }}</span>
        {%- endif -%}
        {% # END Collection set %}
    {%- else -%}
        {% # Variant swatches %}
        {%- unless product.has_only_default_variant -%}
            {%- assign vars_for_swatch = settings.product_variants_for_color_swatch -%}
            {%- assign pd_images = product.media | where: 'media_type', 'image' -%}
            {%- assign mood_def_filter = 'mood:' | append: variant.title -%}
            {%- assign mood_def_img = pd_images | where: 'alt', mood_def_filter -%}
            {%- assign primary_def = variant.featured_image.src | image_url | escape -%}
            {%- assign mood_def = mood_def_img[0].src | image_url | escape -%}
            <ul class="relative flex jc-start ai-stretch colgap-4">
            {%- for option in product.options_with_values -%}
                {%- assign opt_num = forloop.index -%}
                {%- assign opt_index = forloop.index0 -%}
                {%- if vars_for_swatch contains option.name -%}
                    {%- assign more_number = option.values.size | minus: max_number -%}
                    {%- for opt_value in option.values -%}
                        {%- assign var_handle = opt_value | handleize | escape -%}
                        {%- assign var_opt = 'option' | append: opt_num -%}
                        {%- assign var_selected = product.variants | where: var_opt, opt_value -%}
                        {%- assign mood_filter = 'mood:' | append: opt_value -%}
                        {%- if variant.options[opt_index] == opt_value -%}
                            {%- assign myVariant = variant -%}
                            {%- assign selected = 'selected' -%}
                            {%- assign primary = myVariant.featured_image.src -%}
                            {%- assign mood = pd_images | where: 'alt', mood_filter -%}
                        {%- else -%}
                            {%- assign myVariant = var_selected[0] -%}
                            {%- assign selected = '' -%}
                            {%- assign primary = myVariant.featured_image.src -%}
                            {%- assign mood = pd_images | where: 'alt', mood_filter -%}
                        {%- endif -%}
                        {%- if primary == blank -%}
                            {%- assign primary = '' -%}
                        {%- else -%}
                            {%- assign primary = primary | image_url | escape %}
                        {%- endif -%}
                        {%- if mood == blank -%}
                            {%- assign mood = '' -%}
                        {%- else -%}
                            {%- assign mood = mood[0].src | image_url | escape %}
                        {%- endif -%}
                        {%- assign percentage = myVariant.compare_at_price | minus: myVariant.price | times: 100 | divided_by: myVariant.compare_at_price -%}
                        {%- if percentage > 0 -%}
                            {%- assign percentage_text = percentage | percent_with_symbol | append: '% off' -%}                            
                        {%- endif -%}

                        <li class="cursor swatch swatch-{{ var_handle }} --pc circle {{ selected }} {% if myVariant.available == false or myVariant.inventory_quantity < 1 %}oos{% endif %}"
                            data-title="{{ product.title | escape }}"
                            data-price-varies="{{ product.price_varies }}"
                            data-price="{{ myVariant.price }}"
                            data-price-compare="{{ myVariant.compare_at_price }}"
                            data-discount="{{ percentage }}"
                            data-discount-text="{{ percentage_text }}"
                            data-url="{{ product.url | append: '?variant=' | append: myVariant.id | escape }}"
                            data-handle="{{ product.handle | escape }}"
                            data-primary="{{ primary }}"
                            data-mood="{{ mood }}"
                            data-primary-def="{{ primary_def }}"
                            data-mood-def="{{ mood_def }}"
                            data-color="{{ var_handle }}"
                            onclick="$360.selectProductCard(this)"
                        ></li>
                        {%- if forloop.index >= max_number -%}
                            {% break %}
                        {%- endif -%}
                    {%- endfor -%}
                {%- endif -%}
            {%- endfor -%}
            </ul>
            {%- if more_number > 0 -%}
                <span class="more-info ml-5 p4 tl">+ {{ more_number }}</span>
            {%- endif -%}
        {%- endunless -%}
        {% # END Variant swatches %}
    {%- endif -%}
{%- endif -%}

{%- if type == 'galleries' -%}
    {% assign has_image = false %}
    {%- assign preload_img = false -%}
    {%- assign image_alt = product.title | escape -%}
    {%- if variety_coll != blank -%}
        {% assign has_image = true %}
        {% # Collection set %}
        {%- assign more_number = variety_coll.products.size | minus: max_number -%}
        {%- assign pd_images = product.media | where: 'media_type', 'image' -%}
        {%- assign primary_def = pd_images[0] | image_url | escape -%}
        {%- assign mood_def = pd_images[1] | image_url | escape -%}
        <div class="swiper-slide {{ product.metafields.custom.variety_name | handleize | escape }}">
            {%- render 'global-image-wrapper', 
                image: pd_images[0],
                additional_class: 'no-bg',
                preload: preload_img,
                image_alt: image_alt
            -%}
        </div>
        <div class="swiper-slide {{ product.metafields.custom.variety_name | handleize | escape }}">
            {%- render 'global-image-wrapper', 
                image: pd_images[1],
                additional_class: 'no-bg',
                preload: preload_img,
                image_alt: image_alt
            -%}
        </div>
        {%- assign count_number = count_number | plus: 1 -%}
        {%- for vr_prod in variety_coll.products -%}
            {% unless product.handle == vr_prod.handle %}
                {% assign has_image = true %}
                {%- assign vr_handle = vr_prod.metafields.custom.variety_name | handleize | escape -%}
                {%- assign pd_images = vr_prod.media | where: 'media_type', 'image' -%}
                {%- assign vr_variant = vr_prod.selected_or_first_available_variant -%}

                <div class="swiper-slide {{ vr_handle }} hide-m">
                    {%- render 'global-image-wrapper', 
                        image: pd_images[0],
                        additional_class: 'no-bg',
                        preload: preload_img,
                        image_alt: image_alt
                    -%}
                </div>
                <div class="swiper-slide {{ vr_handle }} hide-m">
                    {%- render 'global-image-wrapper', 
                        image: pd_images[1],
                        additional_class: 'no-bg',
                        preload: preload_img,
                        image_alt: image_alt
                    -%}
                </div>
                {%- assign count_number = count_number | plus: 1 -%}
                {%- if count_number >= max_number -%}
                    {% break %}
                {%- endif -%}                
            {% endunless %}
        {%- endfor -%}
        {% # END Collection set %}
    {%- else -%}
        {% # Variant swatches %}
        {%- unless product.has_only_default_variant -%}
            {%- assign vars_for_swatch = settings.product_variants_for_color_swatch -%}
            {%- assign pd_images = product.media | where: 'media_type', 'image' -%}
            {%- assign mood_def_filter = 'mood:' | append: variant.title -%}
            {%- assign mood_def_img = pd_images | where: 'alt', mood_def_filter -%}
            {%- assign primary_def = variant.featured_image.src | image_url | escape -%}
            {%- assign mood_def = mood_def_img[0].src | image_url | escape -%}

            {%- for option in product.options_with_values -%}
                {%- assign opt_num = forloop.index -%}
                {%- assign opt_index = forloop.index0 -%}
                {%- if vars_for_swatch contains option.name -%}
                    {%- assign more_number = option.values.size | minus: max_number -%}
                    {%- for opt_value in option.values -%}
                        {%- assign var_handle = opt_value | handleize | escape -%}
                        {%- assign var_opt = 'option' | append: opt_num -%}
                        {%- assign var_selected = product.variants | where: var_opt, opt_value -%}
                        {%- assign mood_filter = 'mood:' | append: opt_value -%}
                        {%- if variant.options[opt_index] == opt_value -%}
                            {%- assign myVariant = variant -%}
                            {%- assign selected = 'selected' -%}
                            {%- assign primary = myVariant.featured_image -%}
                            {%- assign mood = pd_images | where: 'alt', mood_filter -%}
                        {%- else -%}
                            {%- assign myVariant = var_selected[0] -%}
                            {%- assign selected = '' -%}
                            {%- assign primary = myVariant.featured_image -%}
                            {%- assign mood = pd_images | where: 'alt', mood_filter -%}
                        {%- endif -%}

                        {%- if primary != blank -%}
                            {% assign has_image = true %}
                            <div class="swiper-slide {{ var_handle }} {% if selected == '' %}hide-m{% endif %}">
                                {%- render 'global-image-wrapper', 
                                    image: primary,
                                    additional_class: 'no-bg',
                                    preload: preload_img,
                                    image_alt: image_alt
                                -%}
                            </div>
                        {%- endif -%}
                        {%- if mood.size > 0 -%}
                            {% assign has_image = true %}
                            <div class="swiper-slide {{ var_handle }} {% if selected == '' %}hide-m{% endif %}">
                                {%- render 'global-image-wrapper', 
                                    image: mood[0],
                                    additional_class: 'no-bg',
                                    preload: preload_img,
                                    image_alt: image_alt
                                -%}
                            </div>
                        {%- endif -%}
                        {%- if forloop.index >= max_number -%}
                            {% break %}
                        {%- endif -%}
                    {%- endfor -%}
                {%- endif -%}
            {%- endfor -%}
        {%- endunless -%}
        {% # END Variant swatches %}
    {%- endif -%}

    {% if has_image == false %}
        {% assign variant = product.selected_or_first_available_variant %}
        {%- if variant.featured_image -%}
            {%- assign image = variant.featured_image -%}
        {%- else -%}
            {%- assign image = product.featured_image -%}
        {%- endif -%}

        <div class="swiper-slide">
            {%- render 'global-image-wrapper', 
                image: image,
                additional_class: 'no-bg',
                preload: preload_img,
                image_alt: image_alt
            -%}
        </div>
    {% endif %}
{%- endif -%}
