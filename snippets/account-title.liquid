<div class="container {{ wrapper_class }}">
    <div class="pt-32 pb-24 d-pt-60 d-pb-40 tc d-bb-1">
        <h1 class="h2">
            Welcome back, {{ customer.name }}
        </h1>
    </div>
</div>
<div class="sticky sticky-below-header bg-beige-1 pl-20 pr-20 zi-1 hide-d">
	<div class="bb-1 b-maroon-3">
		{%- assign active_text = 'Order History' -%}
		{%- assign active_value = 'history' -%}
		{%- if request.path contains 'addresses' -%}
			{%- assign active_text = 'Address Book' -%}
			{%- assign active_value = 'addresses' -%}
		{%- endif -%}
		{%- if request.path contains 'wishlist' -%}
			{%- assign active_text = 'Wishlist' -%}
			{%- assign active_value = 'wishlist' -%}
		{%- endif -%}

		{%- assign values = 'Order History,Address Book,Wishlist,Log Out' | split: ',' -%}
		{%- assign option_values = 'history,addresses,wishlist,logout' | split: ',' -%}
		{%- render 'global-select',
			class: 'account-link',
			default_text: active_text,
			default_value: active_value,
			values: values,
			option_values: option_values,
			onchange: '$360.accountRedirect(this)',
			global_select_class: 'pl-0 pr-0'
		-%}
	</div>
</div>