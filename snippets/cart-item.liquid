{% comment %} 
Description:
Snippet for displaying cart item

Variables:
- index: Number, item index
- item: Object, line item object 
- group_id: String. The group_id
- group_parent: Boolean, is group_parent 
- is_cart_page: Boolean, is it from cart page.
- class:  additional class
{% endcomment %}
{%- assign product = item.product -%}
{% assign show_header_cart_function = '$360.showHeaderCart({update: true})' %}
{% if is_cart_page == true %}
	{% assign show_header_cart_function = '$360.showHeaderCart({update: true, from_cart: true})' %}
{% endif %}

{%- assign parent_group = false -%}
{%- assign group_id = 0 -%}
{%- for prop in item.properties -%}
	{%- if prop[0] == '_parent_group' -%}
		{%- assign parent_group = true -%}
	{%- endif -%}
	{%- if prop[0] == '_group_id' -%}
		{%- assign group_id = prop[1] -%}
	{%- endif -%}
{%- endfor -%}

{%- capture remove_function -%}
{%- if parent_group == true -%}
$360.updateItemQtyFromCart({el: this, index: {{ index }}, qty: 0, groupId: {{ group_id }}, removeSameGroup: true }, function() { {{ show_header_cart_function }}; $360.refreshCartPage(); })
{%- else -%}
$360.updateItemQtyFromCart({el: this, index: {{ index }}, qty: 0 }, function() { {{ show_header_cart_function }}; $360.refreshCartPage(); })
{%- endif -%}
{%- endcapture -%}

<li class="relative cart-item flex flex-wrap jc-start ai-stretch {{ class }}">
	<div class="relative image-con mr-16">
		{%- capture item_img -%}
			{%- assign image_alt = product.title | append: ' image' -%}
			{%- if item.image == blank -%}
				<img src="{{ item.image | img_url: '200x' }}" alt="{{ item.image.alt }}" class="image">
			{%- else -%}
				{%- render 'global-image-wrapper', image: item.image, size: 200, preload: true, additional_class: 'rounded-4', image_alt: image_alt -%}
			{%- endif -%}
		{%- endcapture -%}
		{%- if product.tags contains "hidden" -%}
			{{ item_img }}
		{%- else -%}
			<a href="{{ item.url }}" title="{{ item.title | escape }}" class="relative flex jc-start ai-center">{{ item_img }}</a>
		{%- endif -%}
	</div>
	<div class="details-con flex flex-column jc-between">
		<div class="relative top-wrp flex jc-between ai-start colgap-8 rowgap-4">
			<div class="title-wrp flex flex-column jc-start ai-start rowgap-2">
				{%- if product.tags contains "hidden" -%}
					<h4 class="title-item h8 tl bold-500">{{ product.title }}</h4>
				{%- else -%}
					<h4 class="title-item tl flex jc-start"><a href="{{ item.url }}" class="h8 tl bold-500">{{ product.title }}</a></h4>
				{%- endif -%}

				{% # variant and metafields %}
				{%- assign variant_html = '' -%}
				{%- if product.metafields.custom.color_name != blank -%}
					{%- capture var_item -%}
						<div class="p2 c-grey tl">{{ product.metafields.custom.color_name }}</div>
					{%- endcapture -%}
					{%- assign variant_html = variant_html | append: var_item -%}
				{%- endif -%}
				{%- unless product.has_only_default_variant %}
					{%- capture var_item -%}
						<div class="p2 tl c-grey">
							{%- for option in item.options_with_values -%}
								{% unless forloop.first %} / {% endunless %}{{ option.value }}
							{%- endfor -%}
						</div>
					{%- endcapture -%}
					{%- assign variant_html = variant_html | append: var_item -%}
				{%- endunless -%}
				{%- unless variant_html == '' -%}
				{{ variant_html }}
				{%- endunless -%}
				{% # END variant and metafields %}
				
				{% # properties %}
				{%- assign prop_html = '' -%}
				{%- for prop in item.properties -%}
					{%- capture prop_item -%}
						{%- unless prop[0] contains '_' -%}
							{%- if prop[0] == 'Send on' -%}
								{%- assign sendDate = prop[1] |  replace: '-', '' -%}
								<div class="p2 tl c-grey">
									{{ prop[0] }}: {{ sendDate | slice: -2, 2 }} / {{ sendDate | slice: -4, 2 }} / {{ sendDate | slice: 0, 4 }}
								</div>
							{%- else -%}
								<div class="p2 tl c-grey">{{ prop[0] }}: {{ prop[1] }}</div>
							{%- endif -%}
						{%- endunless -%}
					{%- endcapture -%}
					{%- assign prop_html = prop_html | append: prop_item -%}
				{%- endfor -%}
				{%- unless prop_html == '' -%}
					{{ prop_html }}
				{%- endunless -%}
				{% # END properties %}
			</div>
			<div class="price-wrp flex flex-column jc-start ai-end rowgap-2">
				{% # price %}
				{% liquid
					assign compare_at_price = item.variant.compare_at_price | times: item.quantity
					assign price = item.line_price

					assign on_sale = false
					if product.tags contains 'Sale'
						assign on_sale = true
					endif

					#if compare_at_price > price
					#	assign on_sale = true
					#endif

					if item.total_discount > 0
						assign on_sale = true
					endif
				%}
				<div class="p2 tr ws-nowrap">{{ price | money_with_currency }}</div>
				{%- if on_sale -%}
					{%- if item.original_line_price > item.line_price -%}
						<div class="p2 tr ws-nowrap striked-price striked c-grey">{{ item.original_line_price | money_with_currency }}</div>
					{%- else -%}
						<div class="p2 tr ws-nowrap striked-price striked c-grey">{{ compare_at_price | money_with_currency }}</div>
					{%- endif -%}
				{%- endif -%}
				{% # price %}
			</div>
		</div>
		<div class="relative bot-wrp flex jc-between ai-stretch colgap-8 rowgap-4">
			<div class="relative qty-wrp flex jc-start ai-center">
				<div class="qty-wrp btn-plus-minus global-form-qty cdrawer flex jc-between ai-stretch">
					<button type="button" class="toggle global-minus normal-flex">{{ settings.icon_minus }}</button> 
					<input 
						type="number" 
						class="global-qty-text p2" 
						name="quantity" 
						value="{{ item.quantity }}" 
						min="1" 
						{% if item.variant.inventory_policy == "continue" %}
						{% else %}
						max="{{ item.variant.inventory_quantity }}"
						{% endif %} 
						oninput="$360.updateItemQtyFromCart({el: this, index: {{ index }}, qty: this.value, groupId: {{ group_id }}, removeSameGroup: {{ group_parent }}}, function() { {{ show_header_cart_function }}; $360.refreshCartPage(); })" readonly
					>
					<button 
						type="button" 
						class="toggle global-plus normal-flex"
						data-max="{{ item.variant.inventory_quantity }}"
						{% if item.quantity == item.variant.inventory_quantity %}disabled{% endif %}
					>{{ settings.icon_plus }}</button> 
				</div>
			</div>
			<div class="trash-wrp flex jc-end ai-center">
				<div class="rm-link p3 cursor c-grey" onclick="{{ remove_function }}">{{ 'cart.general.remove' | t }}</div>				
			</div>
		</div>
	</div>
</li>
