{% comment %} <div class="global-pagination flex ai-center jc-center colgap-24">
	{%- if paginate.previous -%}
		<a class="pagination-page arrow arrow-left active" href="{{ paginate.previous.url }}" data-go="prev">{{ settings.icon_arrow_left }}</a>
	{%- else -%}
		<span class="pagination-page arrow arrow-left disabled">{{ settings.icon_arrow_left }}</span>
	{%- endif -%}
	<div class="p2">{{ 'general.pagination.current_page' | t: current: paginate.current_page, total: paginate.pages }}</div>
	{%- if paginate.next -%}
		<a class="pagination-page arrow arrow-right active" href="{{ paginate.next.url }}" data-go="next">{{ settings.icon_arrow_right }}</a>
	{%- else -%}
		<span class="pagination-page arrow arrow-right disabled">{{ settings.icon_arrow_right }}</span>
	{%- endif -%}
</div> {% endcomment %}

{% assign pagesize = pagesize | default: 8 %}

<div class="global-pagination">
	{% paginate collection.products by pagesize %}
		<a
			href="{{ paginate.previous.url }}"
			class="pagination-page arrow arrow-left"
			data-nav
			data-go="prev"
			{% if paginate.previous == blank %}data-disabled{% endif %}
		>
			<i>{{ settings.icon_arrow_left }}</i>
			{{ "general.pagination.previous" | t }}
		</a>
		{% for part in paginate.parts -%}
			{% if part.is_link %}
				<a href="{{ part.url }}" class="pagination-page" data-page="{{ part.title }}">{{ part.title }}</a>
			{% else %}
				<button
					{% if paginate.current_page == part.title %}data-active{% endif %}
				>
					{{ part.title }}
				</button>
			{% endif %}
		{% endfor %}
		<a
			href="{{ paginate.next.url }}"
			class="pagination-page arrow arrow-right"
			data-nav
			data-go="next"
			{% if paginate.next == blank %}data-disabled{% endif %}
		>
			{{ "general.pagination.next" | t }}
			<i>{{ settings.icon_arrow_right }}</i>
		</a>
	{% endpaginate %}
</div>