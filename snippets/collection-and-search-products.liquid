{% comment %}
Description:
Snippet for displaying collection and search result product list
{% endcomment %}

{% capture no_result_html %}
  <div class="w-full tc h7 d-pt-40">{{ 'general.search.products.not_found' | t }}</div>
{% endcapture %}

{%- if request.page_type == 'collection' -%}
  {%- if collection.products.size > 0 -%}
    {%- assign bipl = collection.metafields.product_list_banner -%}
    {%- if bipl.image != blank -%}
      {%- assign bipl_position = bipl.position -%}
      {% capture banner_html %}
        <a href="{{ bipl.link_url }}" title="{{ bipl.title |  escape }}" class="relative block bipl-banner t-h-full rounded-12">
          <div class="image-con t-h-full">
            {% assign image_alt = bipl.title | escape %}
            {% render 'global-image-wrapper', image: bipl.image.value, image_alt: image_alt, additional_class: 'no-bg' %}
          </div>
          <div class="absolute bottom-0 left-0 w-full pb-20 pl-20 pr-20 text-con d-pb-24 d-pl-24 d-pr-24 zi-1">
            <h3 class="the-title c-white h4 mb-12 d-mb-16 {% if bipl.style == '2 columns' %}d-mb-32{% endif %}">{{ bipl.title }}</h3>
            {%- if bipl.link_label != blank -%}
              <span class="link2 c-white">{{ bipl.link_label }}</span>
            {%- endif -%}
          </div>
        </a>
      {% endcapture %}
    {%- endif -%}
    
    {%- assign block_position = 0 -%}
    {%- assign item_per_page = settings.collection_products_number -%}
    {%- if bipl.image != blank -%}
      {%- assign item_per_page = settings.collection_products_number | plus: 2 -%}
    {%- endif  -%}
    
    {%- paginate collection.products by item_per_page -%}
      <div class="cns__products flex flex-wrap rowgap-20 d-rowgap-24">
        {%- for product in collection.products -%}
          {%- assign block_position = block_position | plus: 1 -%}
          <div class="product-card-wrapper col-6 col-d-3" data-total-products="{{ collection.products_count }}" data-position="{{ block_position }}" data-bipl-position="{{ bipl_position }}">
            {%- render 'product-card', product: product, x_spacing_on_mobile: true, no_rounded_on_mobile: true -%}
          </div>
          
          {% comment %} BIPL content {% endcomment %}
          {%- assign next_block_position = block_position | plus: 1 -%}
          {%- if next_block_position == bipl_position -%}
            <div class="banner-wrapper col-12 col-hd-6">{{ banner_html }}</div>
          {%- endif -%}
          {% comment %} End BIPL content {% endcomment %}
        {%- endfor -%}
      </div>
      
      {%- if settings.pagination_type == 'pagination' -%}
        {%- if paginate.pages > 1 -%}
          <div class="coll-pagination col-12">
            <div class="mt-36 d-mt-60">
              <p class="p2 tc">{{ 'collections.general.showing_text_html' | t }}</p>
            </div>
            <div class="mt-12 d-mt-16">
              {%- render 'global-pagination',
                paginate: paginate,
                pagesize: settings.collection_products_number
              -%}
            </div>
          </div>
        {%- endif -%}
      {%- endif -%}
    {%- endpaginate -%}
  {%- else -%}
    {{ no_result_html }}
  {%- endif -%}
{%- else -%}
  {%- if search.results.size > 0 -%}
    {%- paginate search.results by settings.search_products_number -%}
      <div class="cns__products flex flex-wrap rowgap-20 d-rowgap-24">
        {%- for product in search.results -%}
          {%- if product.variants.size > 0 -%}
            <div class="product-card-wrapper col-6 col-d-3" data-total-products="{{ search.results_count }}">
              {%- render 'product-card', product: product, x_spacing_on_mobile: true, no_rounded_on_mobile: true -%}
            </div>
          {%- else -%}
            <div class="product-card-wrapper col-6 col-d-3" data-total-products="{{ search.results_count }}">
              {%- render 'article-card', 
                article: product,
                image_rounded: true,
                use_tag: true,
                use_link: true
                -%}
              </div>
            {%- endif -%}
          {%- endfor -%}
        </div>
        
        {%- if settings.pagination_type == 'pagination' -%}
          {%- if paginate.pages > 1 -%}
            <div class="coll-pagination col-12">
              <div class="mt-36 d-mt-60">
                <p class="p2 tc">{{ 'collections.general.showing_text_html' | t }}</p>
              </div>
              <div class="mt-12 d-mt-16">
                {%-
                  render 'global-pagination',
                  paginate: paginate,
                  pagesize: settings.collection_products_number
                -%}
              </div>
            </div>
          {%- endif -%}
        {%- endif -%}
      {%- endpaginate -%}
    {%- else -%}
      {{ no_result_html }}
    {%- endif -%}
  {%- endif -%}