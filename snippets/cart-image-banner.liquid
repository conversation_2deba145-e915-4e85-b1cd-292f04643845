{%- assign banner_html = '' -%}
{%- for i in (1..7) -%}
    {%- assign vimg = 'cart_banner_image' | append: forloop.index -%}
    {%- assign vtitle = 'cart_banner_title' | append: forloop.index -%}
    {%- assign vurl = 'cart_banner_link' | append: forloop.index -%}
    {%- assign voverlay = 'cart_banner_overlay' | append: forloop.index -%}
    {%- assign vcolor = 'cart_banner_color' | append: forloop.index -%}
    {%- if settings[vimg] != blank and settings[vtitle] != blank and settings[vurl] != blank -%}
        {% capture banner_item %}
            <div class="swiper-slide img-banner-item">
                <a href="{{ settings[vurl] }}" alt="{{ settings[vtitle] | escape }}" title="{{ settings[vtitle] | escape }}" class="relative block">
                    {% assign image_alt = settings[vtitle] | escape %}
                    {%- render 'global-image-wrapper',
                        image: settings[vimg],
                        additional_class: 'no-bg rounded-8',
                        preload: true,
                        image_alt: image_alt,
                        overlay: settings[voverlay],
                        overlay_class: ''
                    -%}
                    <div class="absolute left-16 bottom-16 d-left-20 d-bottom-20 h6 tl {% if settings[vcolor] == 'light' %}c-white{% else %}c-black{% endif %} zi-2">
                        {{ settings[vtitle] }}
                    </div>
                </a>
            </div>
        {% endcapture %}
        {%- assign banner_html = banner_html | append: banner_item -%}        
    {%- endif -%}
{%- endfor -%}
{%- if banner_html != '' -%}
<div class="relative bot-wrp w-full overflow pb-16 d-pb-20 hd-pb-40">
    <div class="relative image-banner-wrp swiper">
        <div class="swiper-wrapper">
            {{ banner_html }}
        </div>
    </div>
</div>
{%- endif -%}