{%- assign variant = product.selected_or_first_available_variant -%}
{%- assign purchase_type = 'onetime' -%}
{%- if product.metafields.info.box_selection_option_name == blank
  and product.metafields.info.subscription_products_selection != blank
-%}
  {%- assign purchase_type = 'subscription' -%}
{%- endif -%}

<script>
const productFormVue = Vue.createApp({
	delimiters: ['${','}'], 
	data() {
		return {
			product: {{ product | json }},
			variant: null,
			variantId: {{ variant.id }},
			subscriptionVariants: [],
			subscriptionVariant: null,
			options: ['', '', ''],
			qty: 1,
			optionsChangeCount: 0,
			appliedPrice: null,
			appliedPriceFormatted: null,
			strikedPrice: null,
			strikedPriceFormatted: null,
			optValues: [],
			variantList: [],
			prices: [],
			prodImagesSwiperMainVar: null,
			prodImagesSwiperMainThumbsVar: null,
			prodImagesSwiperPopupVar: null,
			giftCardForm: {
				senderName: '',
				recName: '',
				recEmail: '',
				recEmailInvalid: false,
				message: '',
				sendDate: '',
				sendDateIso: '',
				submitted: false
			},
			purchaseType: '{{ purchase_type }}',
			isDesktopView: window.innerWidth >= 1024,
			isTabletView: window.innerWidth >= 600 && window.innerWidth < 1024,
			sendDatePicker: null,
			// Variety swatch toggle properties
			showAllVariety: false,
			initialVarietyCount: 3, // Number of variety to show initially on desktop
			varietySwatchContainers: [],
			varietySwatchesExpanded: false,
			showVarietyToggle: false,
			userPrefersExpandedVariety: true, // Will be loaded from sessionStorage
			enableViewTransition: false, // Enable View Transition API for page transition
		}
	},
	updated() {
		const thisObj = this;
		/*hide all error message when form is empty*/
		if(!this.giftCardForm.senderName && !this.giftCardForm.recName && !this.giftCardForm.recEmail && !this.giftCardForm.message) {
			this.giftCardForm.submitted = false;
			this.giftCardForm.recEmailInvalid = false;
		}
		/*END hide all error message when form is empty*/
		/*init DatePicker*/
		// if(this.giftCardForm.senderName || this.giftCardForm.recName || this.giftCardForm.recEmail || this.giftCardForm.message) {
		{% if product.gift_card? == false %}
			this.initDatePicker();
		{% endif %}
		// }
		/*END init DatePicker*/
	},
	mounted() {
		// this.setupPopupAfterAjax(); 
		this.updateProductData();

		this.removeQueryString();
		const allVariants = this.product.variants;
		const variants = allVariants.filter((v) => v.id == '{{ variant.id }}');
		if(variants.length) {
			this.variant = variants[0];
			this.options = [this.variant.option1, this.variant.option2, this.variant.option3];
		}

		// Ensure initial availability UI is correct on load
		this.handleAvailability();

		this.swiperMain();
		this.setDefaultDate();
		this.slideToImageByVariantFeaturedMediaId();

		// Initialize color swatch toggle for desktop
		this.isDesktopView = window.innerWidth >= 1024;
		
		// Load user preference from sessionStorage
		this.loadVarietyPreference();
		
		// Immediately hide button if user prefers expanded variety to prevent flickering
		if (this.userPrefersExpandedVariety) {
			const toggleBtn = document.querySelector('.variety-toggle-btn');
			if (toggleBtn) {
				toggleBtn.classList.add('hide-m');
			}
		}
		
		this.initVarietySwatchToggle();
		

		// Add debounced resize handler
		let resizeTimeout;
		window.addEventListener('resize', () => {
			clearTimeout(resizeTimeout);
			resizeTimeout = setTimeout(() => {
				this.handleResize();
			}, 250);
		});

		/* listen click on video and avoid it*/
		document.addEventListener('click', (e) => {
			const target = e.target;
			if(target.classList.contains('video')) {
				if(target.paused) {
					e.preventDefault();
				}
			}
		});
		/* END listen click on video and avoid it*/
	},
	created() {
		document.addEventListener('DOMContentLoaded', function(e) {
			if(document.querySelector('.purchase-box[data-type="subscription"]')) {
				const subscriptionBtnEl = document.querySelector('.product-selection[data-type="subscription"]');
				if(subscriptionBtnEl) {
					subscriptionBtnEl.click();
				}
			}
		});
	},
	methods: {
		updateProductData() {
			this.product = JSON.parse(document.querySelector('.product-top-section .product-data-json').innerHTML);
			this.generatePrices();
			this.generateVariantList();
			this.generateOptValues();
			const customEvent = new CustomEvent('pdp:productDataUpdated');
    		window.dispatchEvent(customEvent);
		},
		generatePrices() {
			const priceString = document.querySelector('.product-top-section .product-data-prices').textContent;
			const priceStringArr = priceString.split('|');
			this.prices = [];
			priceStringArr.forEach(ps => {
				const psArr = ps.split(':');
				const variantId = psArr[0];
				const price = parseFloat(psArr[1]);
				const compareAtPrice = psArr[2] == 'null' ? null : parseFloat(psArr[2]);

				this.prices.push({variantId, price, compareAtPrice});
			});
		},
		generateVariantList() {
			const vQtyListRaw = document.querySelector('.product-top-section .product-data-v-qty').textContent.split('|');
			this.variantList = [];
			vQtyListRaw.forEach(eQty => {
				const qtyData = eQty.split('::');
				const variantId = qtyData[0];
				const inventoryQuantity = qtyData[1];
				const inventoryPolicy = qtyData[2];
				const inventoryManagement = qtyData[3];

				this.variantList.push({'id' : variantId, 'qty': inventoryQuantity, 'policy': inventoryPolicy, 'management':inventoryManagement });
			});
			console.log('vlist',this.variantList);
			console.log('allVariants',this.product.variants);
		},
		generateOptValues() {
			this.optValues = document.querySelector('.product-top-section .product-data-opt-values').textContent;
		},
		slideTo(index) {
			if(this.prodImagesSwiperMainVar) {
				this.prodImagesSwiperMainVar.slideTo(index);
				document.querySelector('.thumbnail-wrapper .thumbnail.active').classList.remove('active');
				document.querySelector(`.thumbnail-wrapper .thumbnail[data-num="${index}"]`).classList.add('active');
			}
		},
		swiperMain() {
			const imageEl = document.querySelector('.product-top-section .product-images');
			
			if(this.isDesktopView) {
				// Desktop view - destroy swiper and don't initialize
				if(this.prodImagesSwiperMainVar) {
					this.prodImagesSwiperMainVar.destroy(true, true);
					this.prodImagesSwiperMainVar = null;
				}
			} else {
				// Mobile/tablet view - initialize swiper
				if(!this.prodImagesSwiperMainVar) {
					this.prodImagesSwiperMainVar = new Swiper('.product-images-main', {
						slidesPerView: 1,
						loop: false,
						spaceBetween: 0,
						slidesOffsetBefore: 0,
						slidesOffsetAfter: 0,
						scrollbar: {
							el: `.product-images  .swiper-scrollbar`
						},
						autoHeight: true,
						// navigation: {
						// 	prevEl: `.product-images-main-swiper-button-prev`,
						// 	nextEl: `.product-images-main-swiper-button-next`,
						// },
						pagination: {
							el: `.product-images-main-pagination.swiper-pagination`,
							clickable: true,
						},
						// thumbs: {
						// 	swiper: this.prodImagesSwiperMainThumbsVar,
						// },
						on: {
							init: function(swiper) {
								swiper.emit('slideChange');
								// get all the slides and exclude the slides that is not matching currect selected color value.
							  
								// if(swiper.slides.length > 1) {
								// 	swiper.slides.forEach(slide => {
								// 		slide.classList.add('cursor-zoom-in');
								// 	});
								// }
								$360.lazyLoadInstance.update();
							},
							slideChange: function(swiper) {
								const currentSlide = swiper.realIndex + 1;
								const totalSlides = swiper.slides.length;
     						  	document.querySelector('.swiper-pagination-fraction-custom').textContent = `${currentSlide} / ${totalSlides}`;
							}
						},
						
					});
				}
			}
		},
		swiperPopup() {
			// First destroy any existing instances
			if (this.prodImagesSwiperPopupVar) {
				this.prodImagesSwiperPopupVar.destroy(true, true);
				this.prodImagesSwiperPopupVar = null;
			} 
			
			if (this.prodImagesSwiperThumbsVar) {
				this.prodImagesSwiperThumbsVar.destroy(true, true);
				this.prodImagesSwiperThumbsVar = null;
			} 

			// Check if required DOM elements exist
			const popupSwiperEl = document.querySelector('.product-popup-swiper');
			const thumbsSwiperEl = document.querySelector('.product-images-popup .swiper-thumbs');
			
			
			if (!popupSwiperEl || !thumbsSwiperEl) {
				console.warn('Required swiper elements not found');
				return;
			}

			// Initialize thumbs swiper
			this.prodImagesSwiperThumbsVar = new Swiper('.product-images-popup .swiper-thumbs', {
				slidesPerView: 'auto',
				spaceBetween: 12,
				direction: 'horizontal',
				centeredSlides: false,
			}); 
			
			this.prodImagesSwiperPopupVar = new Swiper('.product-popup-swiper', {
				mousewheel: {
					enabled: true,
					thresholdDelta: 100
				},
				grabCursor: true,
				navigation: {
					nextEl: ".product-images-popup .swiper-button-next",
					prevEl: ".product-images-popup .swiper-button-prev",
				},
				 thumbs: {
					swiper: this.prodImagesSwiperThumbsVar,
				},

			});
		},
		handleShowGalleryPopup(index) {
			this.pauseAllVideos(`.video-${index}`);
			$360.disableScroll();
			document.querySelector('.product-images-popup').classList.remove('hide-m');

			const activeIndex = index - 1;
			this.prodImagesSwiperPopupVar.slideToLoop(activeIndex, 0);
			
			//  if(this.prodImagesSwiperThumbsVar) {	
			// 	this.prodImagesSwiperThumbsVar.slideToLoop(activeIndex, 0);
			// } 
		},
		handleHideGalleryPopup() {
			document.querySelector('.product-images-popup').classList.add('hide-m');
			this.pauseAllVideos('.product-images-popup');
			$360.enableScroll();
		},
		handleHideGalleryPopupOverlay(event) {
			if(!event.target.classList.contains('popup-content')) return;
			this.handleHideGalleryPopup();
		},
		pauseAllVideos(el) {
			const videos = document.querySelectorAll(`${el} .video`);
			videos.forEach(video => {
				if (video.tagName === 'VIDEO') {
					video.pause();
				}
			});
		},
		handleToggleZoomMode(){
			const popupInnerWrapper = document.querySelector('.product-images-popup .popup-content .inner-wrapper');
			const popupInner = document.querySelector('.product-images-popup .popup-content .inner-wrapper .inner');
			if(popupInnerWrapper.classList.contains('scroll-active')){
				popupInnerWrapper.classList.remove('scroll-active');
			} else {
				popupInnerWrapper.classList.add('scroll-active');
			}
			
			if(popupInner.classList.contains('zoom-mode')) {
				popupInner.classList.remove('zoom-mode');
			} else {
				popupInner.classList.add('zoom-mode');
			}
		},
		async changeVariety(event, el, isDropdown = false) {	
			let prodHandle;

			if(isDropdown == false) {
				event.preventDefault();
				prodHandle = el.getAttribute('data-product-handle');
			} else {
				// get active option value for dropdown
				const selectEl = event.target;
				if(selectEl) {
					const selectedOption = selectEl.querySelector('[selected]');
					if(selectedOption) {
						prodHandle = selectedOption.getAttribute('data-product-handle');
					}
				}
			}
		
		 
		
			// Get the section ID from the wrapper DOM element
			const sectionEl = document.querySelector('.product-section-wrapper');
			const sectionId = sectionEl.getAttribute('data-section-id');
			
			if (!sectionId) {
				console.error('Section ID not found');
				return;
			}
			
			// Check if View Transition API is supported and enabled
			if (!document.startViewTransition || !this.enableViewTransition) {
				// Fallback to direct update without transitions
				if(prodHandle) {
					return this.performColorChange(prodHandle, sectionEl, sectionId, isDropdown);
				}
			}

			// Use View Transition API for smooth animations
			if(prodHandle) {
				const transition = document.startViewTransition(async () => {
					await this.performColorChange(prodHandle, sectionEl, sectionId, isDropdown);
				});

				return transition.finished;
			}
		
		},
		async performColorChange(prodHandle, sectionEl, sectionId, isDropdown = false) {
			try {
				
				// Destroy current swiper instances to prevent memory leaks
				if (this.prodImagesSwiperMainVar) {
					this.prodImagesSwiperMainVar.destroy(true, true);
					this.prodImagesSwiperMainVar = null;
				}
				if (this.prodImagesSwiperPopupVar) {
					this.prodImagesSwiperPopupVar.destroy(true, true);
					this.prodImagesSwiperPopupVar = null;
				}

				// Use Section Rendering API to maintain section settings and blocks
				const {data} = await axios.get(`/products/${prodHandle}?section_id=${sectionId}`);
				
				if (!data) {
					throw new Error(`HTTP error! status: ${data.status}`);
				}
				
				// Parse the response to extract the wrapper content
				const parser = new DOMParser();
				const doc = parser.parseFromString(data, 'text/html');
				const newWrapperContent = doc.querySelector('.product-section-wrapper').innerHTML;
				
				// Replace only the innerHTML to preserve the Vue mounting point
				sectionEl.innerHTML = newWrapperContent;

				if (typeof history.pushState === 'function') {
					const variantId = document.getElementById('atc-btn').getAttribute('data-variant-id');
					history.pushState(null, null, `/products/${prodHandle}?variant=${variantId}`);
				}

				// Reinitialize everything
				this.updateProductData();
				this.swiperMain();
				
				if (window.$360 && $360.lazyLoadInstance) {
					$360.lazyLoadInstance.update();
				}

				// Restore form state
				if (document.getElementById('qty-input')) {
					document.getElementById('qty-input').value = this.qty;
				}
				
				this.updateSelectedOption(0);
				this.updateSelectedOption(1);
				this.updateSelectedOption(2);
				this.handleUpdatedOptions(this.options);
				
				// Reinitialize external scripts
				this.reinitializeExternalScripts();
				
				// Setup popup after everything else is done
				this.setupPopupAfterAjax();
				
				// Reload user preference from sessionStorage after AJAX update
				this.loadVarietyPreference();
				
				// Immediately hide button if user prefers expanded variety (before any other processing)
				if (this.userPrefersExpandedVariety) {
					// Check for button immediately and hide it
					const toggleBtn = document.querySelector('.variety-toggle-btn');
					if (toggleBtn) {
						toggleBtn.classList.add('hidden');
						toggleBtn.style.display = 'none';
					}
					
					// Also check in a loop for a short time to catch it if it appears later
					let checkCount = 0;
					const hideButtonInterval = setInterval(() => {
						const toggleBtn = document.querySelector('.variety-toggle-btn');
						if (toggleBtn) {
							toggleBtn.classList.add('hidden');
							toggleBtn.style.display = 'none';
						}
						checkCount++;
						if (checkCount > 50) { // Stop after 500ms (50 * 10ms)
							clearInterval(hideButtonInterval);
						}
					}, 10);
				}
				
				// Reinitialize color toggle for desktop
				if (this.isDesktopView) {
					this.initVarietySwatchToggle();
				}
				
				
			} catch (error) {
				console.error('Error fetching section:', error);
				// If Section Rendering API fails, reload the page to ensure proper state
				// window.location.href = `/products/${prodHandle}`;
			}
		},
		// Method to reinitialize external scripts
		reinitializeExternalScripts() {
		
			// Reinitialize external functionality
			if (typeof initAddToWishlist === 'function') {
				// initAddToWishlist();
			}
			
			if (typeof initProductTabs === 'function') {
				// initProductTabs();
			}
			
			// Dispatch events
			const customEvent = new CustomEvent('pdp:productDataUpdated');
			window.dispatchEvent(customEvent);
			
			document.dispatchEvent(new CustomEvent('shopify:section:load', {
				detail: { sectionId: document.querySelector('.product-section-wrapper').getAttribute('data-section-id') }
			}));
		},
		setupPopupAfterAjax() {
			let popupWrapperEl = document.querySelector('.product-images-popup');
			
			if (!popupWrapperEl) {
				popupWrapperEl = document.createElement('div');
				popupWrapperEl.className = 'product-images-popup zi-7 fixed top-0 left-0 w-full h-full hide-m';
				document.body.appendChild(popupWrapperEl);
			}
			
			// Find the gallery content
			const galleryEl = document.querySelector('.product-top-section .product-images-popup-content');
			
			if (galleryEl) {
				// Clear popup wrapper and move content
				popupWrapperEl.innerHTML = '';
				popupWrapperEl.appendChild(galleryEl);
				
				// Now initialize the swiper
				this.swiperPopup();
			} 
		},
		updateSelectedOption(optionIndex) {
			const varOptEl = document.querySelector(`.prod-option${optionIndex + 1}`);

			if(varOptEl) {
				if(varOptEl.getAttribute('type') == 'radio') {
					const targetEl = document.querySelector(`.prod-option${optionIndex + 1}[value="${this.options[optionIndex]}"]`);
					if(targetEl) {
						targetEl.checked = true;
					} else {
						const checkedRadioEl = document.querySelector(`.prod-option${optionIndex + 1}:checked`);
						if(checkedRadioEl) {
							this.options[optionIndex] = checkedRadioEl.getAttribute('value');
						}
					}
				} else {
					const varSel = varOptEl.closest('.global-select-div');
					const targetSelected = varSel.querySelector(`.options .option[data-value="${this.options[optionIndex]}"]`);
					if(targetSelected) {
						varOptEl.value = this.options[optionIndex];
						varSel.querySelector('.inner .text').textContent = this.options[optionIndex];
						varSel.querySelectorAll('.options .option').forEach(optEl => {
							optEl.classList.remove('active');
						});
						varSel.querySelector(`.options .option[data-value="${this.options[optionIndex]}"]`).classList.add('active');
					} else {
						this.options[optionIndex] = varSel.querySelector(`.options .option`).getAttribute('data-value');
					}
				}
			} else {
				this.options[optionIndex] = null;
			}
		},
		variantChange() {
			for (let i = 0; i < 3; i++) {
				const num = i + 1;
				if(document.querySelector(`.product-form .prod-option${num}`)) {
					if(document.querySelector(`.product-form .prod-option${num}`).getAttribute('type') == 'radio') {
						if(document.querySelector(`.product-form .prod-option${num}:checked`)) {
							this.options[i] = this.unescapeString(document.querySelector(`.product-form .prod-option${num}:checked`).value);
						}
					} else {
						if(document.querySelector(`.product-form .prod-option${num}`)) {
							this.options[i] = this.unescapeString(document.querySelector(`.product-form .prod-option${num}`).value);
						}
					}

					// if(this.options[i] != null && this.options[i] != '') {
					//     document.querySelector(`.product-form .choosen-option${num} .text`).textContent = this.options[i];
					//     document.querySelector(`.product-form .choosen-option${num}`).classList.add('active');
					// }
				}
			}

		},
		slideToImageByVariantFeaturedMediaId() {
			let selectedVariantFeaturedMediaId;
			if(!this.variant.featured_media?.id) return;
			selectedVariantFeaturedMediaId = this.variant.featured_media.id;
			
			if (selectedVariantFeaturedMediaId && this.prodImagesSwiperMainVar) {
				const slides = document.querySelectorAll('.product-images-main .swiper-slide');
				
				slides.forEach((slide, index) => {
					if (slide.getAttribute('data-image-id') === selectedVariantFeaturedMediaId) {
						if(this.prodImagesSwiperMainVar) {
							this.prodImagesSwiperMainVar.slideTo(index);
						}
						return;
					}
				});
			}
		},
		setQty(newQty){
			this.qty = newQty
		},
		formatMoney(num) {
			return $360.formatMoney(num, true);
		},
		addToCart(e) {
			e.preventDefault();
			{%- if product.gift_card? -%}
			if(this.giftCardForm.senderName || this.giftCardForm.recName || this.giftCardForm.recEmail || this.giftCardForm.message) {
				this.giftCardForm.submitted = true;
			}
			if(this.giftCardForm.submitted) {
				if(!this.giftCardForm.senderName || !this.giftCardForm.recName || !this.giftCardForm.recEmail || !this.giftCardForm.message || !this.giftCardForm.sendDate || this.giftCardForm.recEmailInvalid) {
					return false;
				}
			}
			{%- endif -%}
			
			if(this.options[0] == '' || this.options[1] == '' || this.options[2] == '') {
				if(this.product.options.length == 1) {
					if(this.product.options[0] == 'Title') {
						// no action
					} else {
						if(this.options[0] == '') {
							alert(`{{ 'products.product.please_select' | t }} option: ${this.product.options[0]}!`);
							return false;
						}
					}
				}
				if(this.product.options.length == 2 && (this.options[0] == '' || this.options[1] == '')) {
					alert(`{{ 'products.product.please_select' | t }} options: ${this.product.options[0]} and ${this.product.options[1]}!`);
					return false;
				}
				if(this.product.options.length == 3 && (this.options[0] == '' || this.options[1] == '' || this.options[2] == '')) {
					alert(`{{ 'products.product.please_select' | t }} options: ${this.product.options[0]}, ${this.product.options[1]} and ${this.product.options[2]}!`);
					return false;
				}
			}

			const purchaseBoxSubscriptionEl = document.querySelector('.purchase-box[data-type="subscription"]');
			let args = {};
			if(purchaseBoxSubscriptionEl) {
				if(this.purchaseType == 'subscription') {
					const selectedVarId = this.subscriptionVariant.id;
					const subscriptionOptionsEl = document.getElementById('subscription-options');
					args = {
						qty: this.qty, 
						variantId: selectedVarId, 
						sellingPlanId: subscriptionOptionsEl.value,
						properties: {},
						encodeProperties: {},
						button: document.getElementById('atc-btn'),
					};
				} else {
					args = {
						qty: this.qty, 
						variantId: this.variant.id, 
						properties: {},
						encodeProperties: {},
						button: document.getElementById('atc-btn'),
					};
				}
			} else {
				args = {
					qty: this.qty, 
					variantId: this.variant.id, 
					properties: {},
					encodeProperties: {},
					button: document.getElementById('atc-btn'),
				};
			}

			args = this.buildProperties(args);
			$360.addToCart(args);
		},
		buildProperties(args) {
			args.properties = args.properties || {};

			if(document.getElementById('gift-card-fields') && this.giftCardForm.submitted) {
				args.properties['__shopify_send_gift_card_to_recipient'] = 'on';
				args.properties['Sender name'] = this.giftCardForm.senderName;
				args.properties['Recipient name'] = this.giftCardForm.recName;
				args.properties['Recipient email'] = this.giftCardForm.recEmail;
				args.properties['Message'] = this.giftCardForm.message;				
				/*if send date is today, no need 'Send on' properties to make the email send immediately*/
				if(!this.isToday(this.giftCardForm.sendDateIso)) {
					args.properties['Send on'] = this.giftCardForm.sendDateIso;
				}
			}

			if (Object.keys(args.properties).length === 0) {
				delete args.properties;
			}
			
			if (Object.keys(args.encodeProperties).length === 0) {
				delete args.encodeProperties;
			}
			
			return args;
		},
		unescapeString(string) {
			if(!string) return null;
			const parser = new DOMParser();
			return parser.parseFromString(`<!doctype html><body>${string}`, 'text/html').body.textContent;
		},
		// Inventory helpers
		getVariantListEntryById(variantId) {
			// IDs in variantList are strings; normalize both sides to strings for comparison
			const idStr = `${variantId}`;
			return this.variantList.find(v => `${v.id}` === idStr) || null;
		},
		getVariantQuantityById(variantId) {
			const entry = this.getVariantListEntryById(variantId);
			if(!entry) return 0;
			const qty = parseInt(entry.qty, 10);
			return isNaN(qty) ? 0 : qty;
		},
		isVariantTrulyAvailable(variantObj) {
			if(!variantObj) return false;
			if(this.purchaseType === 'subscription') {
				// Subscription variants don't use main product's variantList quantities here
				return !!variantObj.available;
			}
			const qty = this.getVariantQuantityById(variantObj.id);
			const entry = this.getVariantListEntryById(variantObj.id);
			const allowBackorder = !!(entry && entry.management === 'shopify' && entry.policy === 'continue');
			return !!variantObj.available && (qty > 0 || qty === -1 || allowBackorder);
		},
		removeQueryString() {
			if (window.history.replaceState && window.location.search) {
				const newUrl = window.location.origin + window.location.pathname;
				window.history.replaceState({}, null, newUrl);
			}
		},
		handleUpdatedOptions(newOptions) {
			this.optionsChangeCount++;
			let allVariants = this.product.variants;
			if(this.purchaseType == 'subscription') {
				allVariants = this.subscriptionVariants;
			}
			const options = newOptions.map(nv => this.unescapeString(nv));
			const option1 = options[0];
			const option2 = options[1];
			const option3 = options[2];

			// Set selected option text
			for (let i = 0; i < 2; i++) {
				const selectedOptTextEl = document.querySelector(`.option-wrapper .selected-option-${i+1}`);
				if(selectedOptTextEl) {
					selectedOptTextEl.textContent = options[i];
				}
			}
			// End set selected option text

			// Check variant
			const variants = allVariants.filter((v) => {
				if(this.purchaseType == 'subscription') {
					if(this.variant.option3 == null) {
						if(v.option1 == option1) {
							return true;
						}
					} else {
						if(v.option1 == option1 && v.option2 == option2) {
							return true;
						}
					}
				} else {
					if(v.option1 == option1 && v.option2 == option2 && v.option3 == option3) {
						return true;
					}
				}
			});
			// End Check variant
			
			// Handling selectable options
			// Clear previous oos state
			document.querySelectorAll('.product-form .option-wrapper.product-main .radio-container').forEach(opt => {
				opt.classList.remove('oos');
			});

			// Option group 0 (first option)
			document.querySelectorAll('.product-form .option-wrapper.product-main[data-option-index="0"] .radio-container').forEach(opt => {
				const dataValue = this.unescapeString(opt.getAttribute('data-value'));
				const anyAvailable = allVariants.some(v => v.option1 == dataValue && this.isVariantTrulyAvailable(v));
				if(!anyAvailable) {
					opt.classList.add('oos');
				}
			});

			// Build arrays of option values that exist (for hide/disable), independent of availability
			const relVariantsByOption1 = allVariants.filter(v => v.option1 == option1);
			const option2Arr = [...new Set(relVariantsByOption1.map(v => v.option2))];

			const relVariantsByOption1and2 = allVariants.filter(v => v.option1 == option1 && v.option2 == option2);
			const option3Arr = [...new Set(relVariantsByOption1and2.map(v => v.option3))];

			// Option group 1 (second option)
			document.querySelectorAll('.product-form .option-wrapper.product-main[data-option-index="1"] .radio-container').forEach(opt => {
				const dataValue = this.unescapeString(opt.getAttribute('data-value'));
				// Hide options that don't exist for selected option1
				if(!option2Arr.includes(dataValue)) {
					opt.classList.add('hide-m');
				} else {
					opt.classList.remove('hide-m');
				}
				// Mark as oos if no truly available variant exists for this option combination
				const anyAvailable = allVariants.some(v => v.option1 == option1 && v.option2 == dataValue && this.isVariantTrulyAvailable(v));
				if(!anyAvailable) {
					opt.classList.add('oos');
				} else {
					opt.classList.remove('oos');
				}
			});

			// Option group 2 (third option)
			document.querySelectorAll('.product-form .option-wrapper.product-main[data-option-index="2"] .radio-container').forEach(opt => {
				const dataValue = this.unescapeString(opt.getAttribute('data-value'));
				// Hide options that don't exist for selected option1+option2
				if(!option3Arr.includes(dataValue)) {
					opt.classList.add('hide-m');
				} else {
					opt.classList.remove('hide-m');
				}
				// Mark as oos if not truly available for this exact combination
				const anyAvailable = allVariants.some(v => v.option1 == option1 && v.option2 == option2 && v.option3 == dataValue && this.isVariantTrulyAvailable(v));
				if(!anyAvailable) {
					opt.classList.add('oos');
				} else {
					opt.classList.remove('oos');
				}
			});

			// Handle custom select dropdown states if present
			document.querySelectorAll('.product-form .option-wrapper.product-main').forEach(el => {
				const optionIndex = parseInt(el.getAttribute('data-option-index'));
				let optionArr;
				switch(optionIndex) {
					case 1:
						optionArr = option2Arr;
						break;
					case 2:
						optionArr = option3Arr;
						break;
					default:
						optionArr = null;
				}
				if(optionArr) {
					el.querySelectorAll('.global-select-div .option').forEach(opt => {
						const dataValue = this.unescapeString(opt.getAttribute('data-value'));
						if(!optionArr.includes(dataValue)) {
							opt.classList.add('disable');
						} else {
							opt.classList.remove('disable');
						}
					});
				}
			});
			// End Handling selectable options

			// Update variant selection and availability UI
			const inStockBtnWrapper = document.querySelector('.product-form .atc-wrapper .in-stock-wrapper');
			const oosBtnWrapper = document.querySelector('.product-form .atc-wrapper .oos-wrapper');
			const qtyEl = document.querySelector('.product-form .global-form-qty');
			if(variants.length) {
				// Always set selected variant (even if OOS). setVariant() will call handleAvailability()
				this.setVariant(variants[0]);
			} else {
				// No exact variant for selected combination -> force OOS UI here
				if(inStockBtnWrapper) inStockBtnWrapper.classList.add('hide-m');
				if(oosBtnWrapper) oosBtnWrapper.classList.remove('hide-m');
				if(qtyEl) qtyEl.classList.add('hide-m');
			}
		},
		setVariant(variant) {
			this.variant = variant;
			this.variantId = variant.id;
			this.setSubscriptionVariant();
			// $360.updateQueryString('variant', this.variant.id);
			this.slideToImageByVariantFeaturedMediaId();
			
			// Wishlist button
			// document.querySelector('.wishlist-btn.swym-button').setAttribute('data-variant-id', this.variant.id);
			window.dispatchEvent(new CustomEvent('pdp:productDataUpdated'));
			let mainProductVariant = this.variant;
			if(this.purchaseType == 'subscription' && this.variant.option1 != null && this.variant.option2 != null) {
				// getting variant from main product
				const mpVariants = this.product.variants.filter(v => v.option1 == this.variant.option1);
				if(mpVariants.length) {
					mainProductVariant = mpVariants[0];
				}
			}

			if(mainProductVariant.featured_media && this.optionsChangeCount > 1) {
				const slideIndex = document.querySelector(`.product-images-main .swiper-slide[data-image-id="${mainProductVariant.featured_media.id}"]`).getAttribute('data-index');
				if(this.prodImagesSwiperMainVar) {
					this.prodImagesSwiperMainVar.slideTo(slideIndex);
				}
			}

			this.handleAvailability();

			// Update qty max
			// const variantListFiltered = this.variantList.filter(vl => vl.id == this.variant.id);
			// if(variantListFiltered.length) {
			// 	let maxQty = parseInt(variantListFiltered[0].qty);
			// 	if(variantListFiltered[0].management == 'shopify' && variantListFiltered[0].policy == 'deny') {
			// 		document.getElementById('qty-input').setAttribute('max', variantListFiltered[0].qty);
			// 		if(this.qty > maxQty && maxQty > 0) {
			// 			this.qty = maxQty;
			// 			qtyEl.querySelector('#qty-input').value = maxQty;
			// 		}
			// 	} else {
			// 		document.getElementById('qty-input').setAttribute('max', null);
			// 	}
			// }

			if(this.purchaseType == 'subscription') {
				return;
			}

			const variantPrice = this.prices.find(p => p.variantId == this.variant.id);
			this.appliedPrice = variantPrice.price;
			this.appliedPriceFormatted = this.formatMoney(variantPrice.price);
			this.strikedPrice = variantPrice.compareAtPrice;
			this.strikedPriceFormatted = this.formatMoney(this.strikedPrice);

			// Update product-info pricings
			const infoAppliedPriceEl = document.querySelector('.product-info .price .applied-price');
			const infoStrikedPriceEl = document.querySelector('.product-info .price .striked-price');
			const purchaseBoxAppliedPriceEl = document.querySelector('.purchase-box .main-price .applied-price');
			const purchaseBoxStrikedPriceEl = document.querySelector('.purchase-box .main-price .striked-price');
			const infoFromPrice = document.querySelector('.product-info .price .from-price');

			infoAppliedPriceEl.textContent = this.appliedPriceFormatted;
			infoStrikedPriceEl.textContent = this.strikedPriceFormatted;

			if(purchaseBoxAppliedPriceEl) {
				purchaseBoxAppliedPriceEl.textContent = this.appliedPriceFormatted;
				purchaseBoxStrikedPriceEl.textContent = this.strikedPriceFormatted;
			}

			if(infoFromPrice) {
				if(!infoFromPrice.classList.contains('hide-m')) {
					infoFromPrice.classList.add('hide-m');
				}
			}

			if(this.strikedPrice) {
				if(!infoAppliedPriceEl.classList.contains('c-dark-gold')) {
					infoAppliedPriceEl.classList.add('c-dark-gold');
				}
			} else {
				if(infoAppliedPriceEl.classList.contains('c-dark-gold')) {
					infoAppliedPriceEl.classList.remove('c-dark-gold');
				}
			}

			if(this.variant.compare_at_price > this.variant.price) {
				if(infoStrikedPriceEl.classList.contains('hide-m')){
					infoStrikedPriceEl.classList.remove('hide-m');
				}
				if(purchaseBoxAppliedPriceEl) {
					purchaseBoxAppliedPriceEl.classList.remove('hide-m');
				}
			} else {
				if(!infoStrikedPriceEl.classList.contains('hide-m')){
					infoStrikedPriceEl.classList.add('hide-m');
				}
				if(purchaseBoxAppliedPriceEl) {
					purchaseBoxAppliedPriceEl.classList.add('hide-m');
				}
			}
			// End update product-info pricings
		},
		handleAvailability() {
			let isAvailable = false;
			if(this.purchaseType === 'onetime') {
				const hasVariant = !!this.variant;
				const qty = hasVariant ? this.getVariantQuantityById(this.variant.id) : 0;
				const entry = hasVariant ? this.getVariantListEntryById(this.variant.id) : null;
				const allowBackorder = !!(entry && entry.management === 'shopify' && entry.policy === 'continue');
				isAvailable = hasVariant && !!this.variant.available && (qty > 0 || qty === -1 || allowBackorder);
			} else {
				isAvailable = !!(this.subscriptionVariant && this.subscriptionVariant.available);
			}
			let qtyEl = document.querySelector('.product-form .global-form-qty');
			const inStockBtnWrapper = document.querySelector('.product-form .atc-wrapper .in-stock-wrapper');
			const oosBtnWrapper = document.querySelector('.product-form .atc-wrapper .oos-wrapper');

			if(isAvailable) {
				if(qtyEl) {
					qtyEl.classList.remove('hide-m');
				}
				if(inStockBtnWrapper) {
					inStockBtnWrapper.classList.remove('hide-m');
				}
				if(oosBtnWrapper) {
					oosBtnWrapper.classList.add('hide-m');
				}
			} else {
				if(qtyEl) {
					qtyEl.classList.add('hide-m');
				}
				if(inStockBtnWrapper) {
					inStockBtnWrapper.classList.add('hide-m');
				}
				if(oosBtnWrapper) {
					oosBtnWrapper.classList.remove('hide-m');
				}
			}
		},
		validateEmail() {
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			this.giftCardForm.recEmailInvalid = !emailRegex.test(this.giftCardForm.recEmail);
		},
		initDatePicker() {
			const thisObj = this;
			const elTarget = 'gc-send-date';
			const stickyEl = document.querySelector('.right-side .inner');
			
			// Only initialize if it doesn't exist
			if(!thisObj.sendDatePicker) {
				// Set default date immediately when initializing
				thisObj.setDefaultDate();
				
				thisObj.sendDatePicker = new Datepicker(`#${elTarget}`, {
					min: (function(){
						var date = new Date();
						date.setDate(date.getDate() - 1);
						return date;
					})(),
					max: (function(){
						var date = new Date();
						date.setDate(date.getDate() + 90);
						return date;
					})(),
					onRender: function(el) {
						let sendDateEl = document.getElementById(`${elTarget}`);
						sendDateEl.blur();
						if(stickyEl) {
							stickyEl.classList.remove('sticky-below-header');
						}
					},
					onChange: function(date) {
						if (typeof date != "undefined") {
							let sendDateEl = document.getElementById(`${elTarget}`);
							let sendDateVal = thisObj.formatDate(date, elTarget);
							sendDateEl.value = sendDateVal;
							thisObj.giftCardForm.sendDate = sendDateVal;
							thisObj.giftCardForm.sendDateIso = thisObj.formatDate(date, elTarget, true);
						}
						if(stickyEl) {
							stickyEl.classList.add('sticky-below-header');
						}
					}
				});

				// Set default date again after a short delay to ensure it's applied
				setTimeout(() => {
					const sendDateEl = document.getElementById(elTarget);
					if (sendDateEl && !sendDateEl.value) {
						thisObj.setDefaultDate();
						sendDateEl.value = thisObj.giftCardForm.sendDate;
					}
				}, 100);
			}
		},
		formatDate(dateString, id, iso=false) {
			const date = new Date(dateString);
			// Check if the Date object is valid
			if (isNaN(date.getTime())) {
				if(id){
					document.getElementById(`${id}`).value == '';
				}
				return '';
			}
			// Extract year, month, and day components
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');

			// Format the date in the desired output format
			if(iso) {
				return `${year}-${month}-${day}`;
			} else {
				return `${day} / ${month} / ${year}`;
			}
		},
		isToday(dateString) {
			const inputDate = new Date(dateString);
			const today = new Date();
			return (
				inputDate.getFullYear() === today.getFullYear() &&
				inputDate.getMonth() === today.getMonth() &&
				inputDate.getDate() === today.getDate()
			);
		},
		setDefaultDate() {
			const today = new Date();
			const year = today.getFullYear();
			const month = String(today.getMonth() + 1).padStart(2, '0');
			const day = String(today.getDate()).padStart(2, '0');

			this.giftCardForm.sendDate = `${day} / ${month} / ${year}`;
			this.giftCardForm.sendDateIso = `${year}-${month}-${day}`;
		},
		handleChangePurchaseType(type) {
			this.purchaseType = type;
			document.querySelector('.purchase-box.selected').classList.remove('selected');
			document.querySelector(`.purchase-box[data-type="${type}"]`).classList.add('selected');
			this.setSubscriptionVariant();
			this.handleAvailability();
			this.handleUpdatedOptions(this.options);
		},
		setSubscriptionVariant() {
			let subsVariant = this.subscriptionVariants[0];
			let subsVariants = [];
			if(this.variant.option2 == null && this.variant.option3 == null) {
				subsVariants = this.subscriptionVariants;
			} else if(this.variant.option3 == null) {
				subsVariants = this.subscriptionVariants.filter(v => v.option1 == this.variant.option1);
			} else {
				subsVariants = this.subscriptionVariants.filter(v => v.option1 == this.variant.option1 && v.option2 == this.variant.option2);
			}

			if(!subsVariants.length) {
				return null;
			}
			this.subscriptionVariant = subsVariants[0];
			return subsVariants[0];
		},
		handleClickProductBox(btn) {
			const purchaseType = btn.getAttribute('data-type');
			this.subscriptionVariants = JSON.parse(btn.getAttribute('data-variants'))

			document.querySelectorAll(`.purchase-box[data-type="${purchaseType}"] .product-selection`).forEach(el => {
				el.classList.remove('selected');
			});
			btn.classList.add('selected');

			const qty = btn.getAttribute('data-qty');
			const packingType = btn.getAttribute('data-packing-type');
			const planNames = btn.getAttribute('data-selling-plan-names').split('|');
			const planIds = btn.getAttribute('data-selling-plan-ids').split('|');
			const subscribeDiscount = btn.getAttribute('data-subscribe-discount');

			subsVariant = this.setSubscriptionVariant();
			if(subsVariant) {
				const varId = subsVariant.id;
				const price = subsVariant.price;
				const formattedPrice = this.formatMoney(price);
				const compareAtPrice = subsVariant.compare_at_price;
				const formattedCompareAtPrice = compareAtPrice > 0 ? this.formatMoney(compareAtPrice) : '';

				const appliedPriceEl = document.querySelector(`.purchase-box[data-type="${purchaseType}"] .applied-price`);
				const compareAtPriceEl = document.querySelector(`.purchase-box[data-type="${purchaseType}"] .striked-price`);

				if(planNames[0] != '') {
					let listOptionsEl = '';
					let selectOptionEl = '';
					for (let i = 0; i < planNames.length; i++) {
						const isActive = i == 0 ? 'active' : '';
						const isSelected = i == 0 ? 'selected' : '';

						if(i == 0) {
							document.querySelector('.subscription-options .inner .text').innerHTML = planNames[i];
						}

						listOptionsEl += `<li class="option p2 ${isActive}" data-value="${planIds[i]}">${planNames[i]}</li>`;
						selectOptionEl += `<option value="${planIds[i]}" ${isSelected}>${planNames[i]}</option>`;
					}
					
					document.querySelector('.subscription-options .inner .label').classList.add('hide-m');
					document.querySelector('.subscription-options .options').innerHTML = listOptionsEl;
					document.querySelector('.subscription-options select').innerHTML = selectOptionEl;
				}

				appliedPriceEl.textContent = formattedPrice;
				compareAtPriceEl.textContent = formattedCompareAtPrice;
				if(formattedCompareAtPrice != '') {
					compareAtPriceEl.classList.remove('hide-m');
				} else {
					compareAtPriceEl.classList.add('hide-m');
				}

				this.handleAvailability();
			}
		},	
		
		// Variety swatch toggle methods
		loadVarietyPreference() {
			try {
				const storedPreference = sessionStorage.getItem('userPrefersExpandedVariety');
				this.userPrefersExpandedVariety = storedPreference === 'true';
			} catch (error) {
				this.userPrefersExpandedVariety = false;
			}
		},
		
		initVarietySwatchToggle() {
			// Reset variety toggle state
			this.resetVarietyToggleState();
			
			// Immediately hide button if user prefers expanded variety to prevent flickering
			if (this.userPrefersExpandedVariety) {
				const toggleBtn = document.querySelector('.variety-toggle-btn');
				if (toggleBtn) {
					toggleBtn.classList.add('hide-m');
				}
			}
			
			if (window.innerWidth >= 1024) {
				// Small delay to ensure DOM is fully updated after AJAX
				setTimeout(() => {	
					this.setupVarietyToggle();
				}, 0);
			}
		},
		
		setupVarietyToggle() {
			const colorContainers = document.querySelectorAll('.product-variety-opt');
			const toggleBtn = document.querySelector('.variety-toggle-btn');
			
			// If user prefers expanded variety, hide button and show all options
			if (this.userPrefersExpandedVariety && toggleBtn) {
				toggleBtn.classList.add('hide-m');
			}
			
			colorContainers.forEach(container => {
				const colorOptions = container.querySelectorAll('.product-variety-opt .color-option');
				if (colorOptions.length > this.initialVarietyCount && toggleBtn) {
					// Check if user prefers expanded variety
					const shouldShowAll = this.userPrefersExpandedVariety;
					
					colorOptions.forEach((option, index) => {
						if (index >= this.initialVarietyCount) {
							option.style.display = shouldShowAll ? 'block' : 'none';
						}
					});
					
					// Only show toggle button if user doesn't prefer expanded variety
					if (!shouldShowAll) {
						toggleBtn.classList.remove('hide-m');
						this.showVarietyToggle = true;
					} else {
						// Keep button hidden if user prefers expanded variety
						toggleBtn.classList.add('hide-m');
						this.showVarietyToggle = false;
					}
					
					// Set expanded state based on user preference
					this.varietySwatchesExpanded = shouldShowAll;
					
				}
			});
		},
		
		toggleVarietySwatches() {
			this.varietySwatchesExpanded = !this.varietySwatchesExpanded;
			
			// Set session state when user expands variety
			if (this.varietySwatchesExpanded) {
				this.userPrefersExpandedVariety = true;
				// Save to sessionStorage
				try {
					sessionStorage.setItem('userPrefersExpandedVariety', 'true');
				} catch (error) {
				}
			} else {
				// Clear session state when user collapses variety
				this.userPrefersExpandedVariety = false;
				try {
					sessionStorage.removeItem('userPrefersExpandedVariety');
				} catch (error) {
				}
			}
			
			const colorContainers = document.querySelectorAll('.product-variety-opt');
			const toggleBtn = document.querySelector('.variety-toggle-btn');
			
			colorContainers.forEach(container => {
				const colorOptions = container.querySelectorAll('.product-variety-opt .color-option');
				
				if (colorOptions.length > this.initialVarietyCount && toggleBtn) {
					colorOptions.forEach((option, index) => {
						if (index >= this.initialVarietyCount) {
							option.style.display = this.varietySwatchesExpanded ? 'block' : 'none';
						}
					});
					
					// Show/hide button based on expanded state
					if (this.varietySwatchesExpanded) {
						toggleBtn.classList.add('hide-m');
						this.showVarietyToggle = false;
					} else {
						toggleBtn.classList.remove('hide-m');
						this.showVarietyToggle = true;
					}
					
					// Update button text using JavaScript
					const toggleText = toggleBtn.querySelector('.toggle-text');
					if (toggleText) {
						toggleText.textContent = this.varietySwatchesExpanded ? '' : '+ More colours';
					}
				}
			});
		},
		
		resetVarietySwatches() {
			const colorContainers = document.querySelectorAll('.product-variety-opt');
			const toggleBtn = document.querySelector('.variety-toggle-btn');
			
			colorContainers.forEach(container => {
				const colorOptions = container.querySelectorAll('.product-variety-opt .color-option');
				
				// Show all color options on mobile
				colorOptions.forEach(option => {
					option.style.display = 'block';
				});
			});
			
			this.varietySwatchesExpanded = false;
			this.showVarietyToggle = false;
		},
		
		resetVarietyToggleState() {
			
			// Reset Vue state but preserve session preference
			this.showVarietyToggle = false;
			
			// Reset all color options to show all initially (handle both current and cached DOM)
			const colorContainers = document.querySelectorAll('.product-variety-opt');
			
			colorContainers.forEach(container => {
				const colorOptions = container.querySelectorAll('.product-variety-opt .color-option');
				colorOptions.forEach(option => {
					option.style.display = 'block';
				});
			});
			
		},
		
		handleResize() {
			const wasDesktop = this.isDesktopView;
			const isDesktop = window.innerWidth >= 1024;
			
			if (isDesktop && !wasDesktop) {
				// Switched to desktop
				this.isDesktopView = true;
				this.swiperMain();
				this.initVarietySwatchToggle();
			} else if (!isDesktop && wasDesktop) {
				// Switched to mobile/tablet
				this.isDesktopView = false;
				this.swiperMain();
				this.resetVarietySwatches();
			}
		},
	},
	watch: {
		options: {
			handler(newValue, oldValue) {
				this.handleUpdatedOptions(newValue);
			},
			deep: true
		},
		// 'giftCardForm': {
		// 	handler(newValue) {
		// 		const hasContent = newValue.senderName || newValue.recName || 
		// 					 newValue.recEmail || newValue.message;
				
				// Reset datepicker if no content
				// if (!hasContent) {
				// 	this.sendDatePicker = null;
				// }
				
				// this.$nextTick(() => {
					// if (hasContent) {
					// 	this.initDatePicker();
					// }
				// });
		// },
		// deep: true
		// }
	}
}).mount('.product-section-wrapper');
window.productFormVue = productFormVue;
</script>
