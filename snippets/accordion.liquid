{%- comment -%}
Description:
Snippet for displaying accordion

Variables:
- icon_rotate: Boolean. If true, the icon will be rotating if expanded
- wrapper_class: String. Class for the wrapper
- desc_class: String. Class for the description
- title_padding_class: String. Class for the title wrapper padding
- title_class: String. Class for the title
- title: String. text for title
- description: String. text for description
- desc_html: HTML string for description
- desc_no_pt: Boolean. If true, description will not have padding top
- icon_enable: Boolean. If true, icon will not displayed
- icon_type: String 'plus/chevron'. Default is 'plus'
- open: Boolean. If true, the accordion will be open
- vue_onclick: String. Onclick event for vue function
- icon_reverse: Boolean. To adjust the icon position
- unlink_expand: Boolean. If true, only 1 accordion would expand
{%- endcomment -%}
{%- if icon_type == blank -%}
    {%- assign icon_type = 'plus' -%}
{%- endif -%}

{%- if title_padding_class == blank -%}
    {%- assign title_padding_class = 'pt-20 pb-20' -%}
{%- endif -%}

{%- if icon_enable == blank -%}
    {%- assign icon_enable = true -%}
{%- endif -%}

<div class="accordion {{ wrapper_class }} {% if icon_rotate == true %}icon-rotate{% endif %} {% if icon_reverse%}flex flex-column jc-start ai-start column-reverse{% endif %}">
    <div class="title-icon accordion-title relative cursor flex {% if icon_reverse %}row-reverse jc-end colgap-10{% else %}jc-between{% endif %} {{ title_padding_class }} {% if open == true %}active{% endif %}" {% if vue_onclick != blank %} @click="{{ vue_onclick }}" {% else %}onclick="{% if unlink_expand == true %}$360.accordionToggle(event){% else %}$360.accordionToggle(event, '.description'){% endif %}"{% endif %}>
        <div class="title {{ title_class }}">{{ title }}</div>
        {%- if icon_enable -%}
        <div class="icon-wrapper flex ai-center jc-center">
            {%- if icon_type == 'plus' -%}
                <div class="icon icon-plus ai-center jc-center">{{ settings.icon_plus }}</div>
                <div class="icon icon-minus ai-center jc-center">{{ settings.icon_minus }}</div>
            {%- else -%}
                <div class="icon icon-chevron ai-center jc-center flex">{{ settings.icon_select }}</div>
            {%- endif -%}
        </div>
        {%- endif -%}
    </div>
    <div class="description {% if open != true %}hide-m{% endif %} {% if desc_no_pt != true %}pt-25{% endif %} {{ desc_class }}">
        {%- if description != blank -%}
            <div class="p2">{{ description | newline_to_br }}</div>
        {%- endif -%}
        {%- if desc_html != blank -%}
            {{ desc_html }}
        {%- endif -%}
    </div>
</div>
