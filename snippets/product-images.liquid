<div id="product-images" class="product-images {{ class }}">
	{%- if product.images.size > 0 -%}
		{% assign enable_zoom_view = false %}
		{% if enable_zoom_view == true and product.images.size == 1 %}
			{% assign no_zoom = true %}
		{% elsif enable_zoom_view == true and product.images.size > 1 %}
			{% assign no_zoom = false %}
		{% elsif enable_zoom_view == false %}
			{% assign no_zoom = true %}
		{% endif %}
		<div class="product-images-main swiper relative flex-1 ml-0 d-pb-0 d-pr-0">
			{%- render 'product-tags-label', product: product, wrapper_class: 'flex colgap-8 absolute p-12 d-p-16 zi-1', text_class: 'p4' -%}
			
			<div class="swiper-wrapper">
				{%- assign image_num = 1 -%}
				{%- for m in product.media -%}
					{%- if m.media_type == 'image' -%}
						{%- assign image_alt = m.alt | escape -%}
						{%- if image_alt == blank -%}
							{%- assign image_alt = 'Image' | append: image_num -%}
						{%- endif -%}
						{%- assign image_alt_mood = image_alt | remove_first: 'mood:' | strip -%}
						{% assign image_is_mood =  %}
						<div {% unless no_zoom %}onclick="productFormVue.handleShowGalleryPopup({{ image_num }})"{% endunless %} class="each swiper-slide" data-image-id="{{ m.id }}" {% if image_alt contains 'mood:' %}data-mood-id="{{ image_alt_mood }}"{% endif %} data-index="{{ image_num | minus: 1 }}">
							{%- render 'global-image-wrapper',
								additional_class: 'no-bg', 
								image: m, 
								size: '2000x', 
								image_alt: image_alt,
								preload: true
							-%}
						</div>
						{%- assign image_num = image_num | plus: 1 -%}
					{%- elsif m.media_type == 'video' or m.media_type == 'external_video' -%}
						<div {% unless no_zoom %} onclick="productFormVue.handleShowGalleryPopup({{ image_num }})"{% endunless %} class="each swiper-slide video-{{ image_num }}" data-image-id="{{ m.id }}" data-index="{{ image_num | minus: 1 }}">
							{%- assign image_alt = 'Image' | append: image_num -%}
							{%- assign data_id = 'video-' | append: image_num -%}
						{% if m.sources[0].width != blank %} 
							{% assign height = m.sources[0].width | append: 'px' %}
						{% else %} 
							{% assign height = '100%' %}
						{% endif %}
							<div class="flex jc-center ai-stretch flex-column w-full mx-auto" style="height:{{ height }}">
								{{ m | video_tag: autoplay: true, controls: true, loop: true, muted: true, class: 'video h-full w-auto', id: data_id  }}
							</div>
						</div>
						{%- assign image_num = image_num | plus: 1 -%}
					{%- endif -%}
				{%- endfor -%}
				{% comment %}
				{%- for image in product.images -%}
					<div onclick="productFormVue.handleShowGalleryPopup({{ image_num }})" class="each swiper-slide" data-image-id="{{ image.id }}" data-index="{{ image_num | minus: 1 }}">
						{%- assign image_alt = 'Image' | append: image_num -%}
						{%- render 'global-image-wrapper',
							additional_class: 'no-bg', 
							image: image, 
							size: '2000x', 
							image_alt: image_alt,
							preload: true
						-%}
					</div>
					{%- assign image_num = image_num | plus: 1 -%}
				{%- endfor -%}
				{% endcomment %}
			</div>

			 {% comment %} 
			<div class="thumbs-wrapper zi-1">

				<div class="swiper-thumbs pt-12 d-pt-0">
					<div class="swiper-wrapper pb-12 d-pb-4">
						{%- for m in product.media -%}
							{%- if m.media_type == 'image' -%}
								<div class="swiper-slide">
									<div class="image-box-square overflow">
									{%- assign image_alt = 'Thumbnail Image' | append: image_num -%}
									{%- render 'global-image-wrapper', preload: true, image: m, size: '100', image_alt: image_alt -%}
									</div>
								</div>
							{%- elsif m.media_type == 'video' -%}
								<div class="swiper-slide">
									<div class="image-box-square overflow">
									{%- assign image_alt = 'Thumbnail Image' | append: image_num -%}
									{%- render 'global-image-wrapper', preload: true, image: m.preview_image, size: '100', image_alt: image_alt -%}
									</div>
								</div>
							{%- endif -%}
						{%- endfor -%}
						{%- for image in product.images -%}
							<div class="swiper-slide">
								<div class="image-box-square overflow">
								{%- assign image_alt = 'Thumbnail Image' | append: image_num -%}
								{%- render 'global-image-wrapper', preload: true, image: image, size: '100', image_alt: image_alt -%}
								</div>
							</div>
						{%- endfor -%}
					</div>
				</div>
			</div>   
			{% endcomment %}

			{% if product.images.size > 1 %}
				
				<div class="product-images-main-pagination swiper-pagination"></div>
				<div class="swiper-pagination-fraction-custom hide-d p4 absolute top-0 right-0 zi-1 p-16"></div>
				{% comment %} <div class="product-images-main-swiper-button-next swiper-button-next">{{ settings.icon_slide_right }}</div> {% endcomment %}
				{% comment %} <div class="product-images-main-swiper-button-prev swiper-button-prev">{{ settings.icon_slide_left }}</div>  {% endcomment %}
			{% endif %}
		
		
			

		</div>



	{%- endif -%}
</div>
