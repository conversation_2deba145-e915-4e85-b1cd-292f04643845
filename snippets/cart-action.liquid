{% comment %}
Description:
Snippet for cart action
Fake button is for displaying loading spinner on click it. 
This is because it cannot be done on the actual checkout button
{% endcomment %}
{%- if item.total_discount > 0 -%}
<div class="relative flex jc-between ai-center">
    <div class="p2 tl c-grey">{{ 'customer.order.discount' | t }}</div>
    <div class="p2 tr c-grey">{{ cart.total_discount | money_with_currency | prepend: '-' }}</div>
</div>
{%- endif -%}
<div class="relative flex jc-between ai-center mt-4">
    <div class="p2 bold-500 tl">{{ 'cart.general.subtotal' | t }}</div>
    <div class="p1 bold-500 tr">{{ cart.total_price | money_with_currency }}</div>
</div>
<div class="relative mt-16">
    <button type="button" class="fake-checkout-btn btn-checkout btn1 w-full tc">
        <span>{{ 'cart.general.checkout' | t }}</span>
    </button>
    <button type="submit" name="checkout" class="checkout-btn tc hide-m">
        <span>{{ 'cart.general.checkout' | t }}</span>
    </button>
    {%- if settings.cart_tnc_msg != blank -%}
    <div class="relative tnc-wrp tc p3 c-grey mt-8">{{ settings.cart_tnc_msg }}</div>
    {%- endif -%}
</div>
{%- assign payment_image_html = '' -%}
{%- for i in (1..5) -%}
    {%- assign imgVar = 'pay_logo_' | append: forloop.index -%}
    {%- assign image_alt = 'Payment ' |  append: forloop.index -%}
    {%- if settings[imgVar] != blank -%}
        {% capture payment_item %}
            <div class="payment-img">
                {%- render 'global-image-wrapper',
                    image: settings[imgVar],
                    additional_class: 'no-bg',
                    preload: false,
                    image_alt: image_alt 
                -%}
            </div>
        {% endcapture %}
        {%- assign payment_image_html = payment_image_html |  append: payment_item -%}
    {%- endif -%}
{%- endfor -%}
{%- if payment_image_html != '' -%}
    <div class="relative flex jc-center ai-center mt-16 colgap-4">
    {{ payment_image_html }}
</div>
{%- endif -%}
