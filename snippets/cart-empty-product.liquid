{%- comment -%}
Description:
Snippet for displaying product card in empty cart

Variables:
- image: Image object / product image
- image_alt: String. Alt image
- preload_img: Boolean. The variable to lazy load image or not
- link: String. Link url
- title: String. Product title
- text: String. Product text
- link_icon: String. SVG image
{%- endcomment -%}
<div class="relative empty-ctn-item bg-light-grey rounded-4">
    <a href="{{ link }}" class="ctn-inner w-full flex jc-start ai-center">
        <div class="img-con p-16">
            {%- render 'global-image-wrapper',
                image: image,
                preload: preload_img,
                overlay: false,
                overlay_class: '',
                additional_class: 'rounded-4',
                image_alt: image_alt,
            -%}
        </div>
        <div class="relative tl rside-wrp pr-20 d-pr-24">
            <p class="p2 tl bold-700">{{ title }}</p>
            {%- if text != blank -%}
            <p class="p4 tl">{{ text }}</p>
            {%- endif -%}
            <div class="icon-wrp absolute top-0 right-20">{{ link_icon }}</div>
        </div>
    </a>
</div>
