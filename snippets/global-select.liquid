{%- comment -%}
Description:
Snippet for displaying select options

Variables:
- label: Displayed label
- label_class: The label class
- custom_label: Custom label
- default_value: Default value
- default_text: Default value
- selected_text_class: The selected text class
- list_option_class: The list option class
- values: values array
- option_values: Array, the option values
- variety_handles: Array of variety product handles for data attributes
- id: Select element id
- global_select_class: Additional class for .global-select-div
- class: Select element classes
- field_name: Select field name
- selected_inline: Is the selected value displayed inline with label?
- onchange: String. js onchange event function
- vue_onchange: String. onchange event vue method
- vue_model: String. vue v-model
- vue_class: String. vue :class binding
- custom_icon: String. SVG icon for the select
- show_color_circles: Boolean. Whether to show color circles beside options
- selected_option_value: String. Passed value to be selected
- extra_option_value: String. Extra option value to be added to the select
{%- endcomment -%}
<div {%if vue_class != blank %}:class="{{vue_class}}"{% endif %} class="global-select-div {{ global_select_class }} {% if selected_inline != blank %}selected-inline{% endif %}">
    <div class="inner relative">
        <div class="label {{ label_class }}">{{ label }}</div>
        <div class="text {{ selected_text_class }}">
            {%- if show_color_circles == true and default_value != blank -%}
                {%- assign default_class_name = default_text | handle -%}
                {% assign default_wrp_class = 'flex ai-center colgap-8' %}
                {%- if variety_selected_is_oos == true -%}
                    {% assign default_wrp_class = default_wrp_class | append: ' opacity-5' %}
                {%- endif -%}
                <div class="{{ default_wrp_class }}">
                    <span class="color-circle pdp-dropdown-circle block b-1 b-border swatch-{{ default_class_name }}"></span>
                    <span>{{ default_text }}
                        {%- if variety_selected_is_oos == true -%}
                                <span class="oos"> - {{ 'products.product.oos' | t }}</span>
                        {%- endif -%}
                    </span>
                </div>
            {%- else -%}
                {{ default_text }}
            {%- endif -%}
        </div>
        <div class="icon">
            {% if custom_icon == blank %}
                {{ settings.icon_select }}
            {% else %}
                {{ custom_icon }}
            {% endif %}
        </div>
    </div>
    <ul class="options">
        {%- for option in values -%}
            {%- assign option_value = option -%}
            {%- if option_values != blank -%}
                {%- assign option_value = option_values[forloop.index0] -%}
            {%- endif -%}
            {%- assign variety_avail = '' -%}
            {%- if variety_available != blank -%}
                {%- assign variety_avail = variety_available[forloop.index0] -%}
            {%- endif -%}

            {% capture test_value %}{{ default_value | json }}{% endcapture %}
            {%- unless test_value contains '"' -%}
              {%- if test_value != "null" -%}
                {%- assign option_value = option_value | times: 1 -%}
              {%- endif -%}
            {%- endunless -%}

            
            <li class="option {{ list_option_class }} {% if option_value == default_value %}active{% endif %}" 
                data-value="{{ option_value | escape }}">
                
                {%- if show_color_circles == true -%}
                    {%- assign class_name = option | handle -%}
                    <div class="flex ai-center colgap-8 option-text-cont">
                        <span class="color-circle pdp-dropdown-circle block b-1 b-border swatch-{{ class_name }}"></span>
                        <span class="option-text">
                            {{ option }} 
                           
                            {%- if variety_avail != blank -%}
                                {%- if variety_avail == 'false' -%}
                                    <span class="oos"> - {{ 'products.product.oos' | t }}</span>
                                {%- endif -%}
                            {%- endif -%}
                        </span>
                    </div>
                {%- else -%}
                    {{ option }}
                {%- endif -%}
            </li>
        {%- endfor -%}
    </ul>
    <select
        name="{{ field_name }}"
        id="{{ id }}"
        class="{{ class }}"
        {% if onchange != blank %}
            onchange="{{ onchange }}"
        {% endif %}
        {% if vue_onchange != blank %}
            @change="{{ vue_onchange }}"
        {% endif %}
        {% if vue_model != blank %}
            v-model="{{ vue_model }}"
        {% endif %}
        {% if required == true %}required{% endif %}
    >
        <option value="">-- Please select --</option>
        {%- for option in values -%}
            {%- assign option_value = option -%}
            {%- if option_values != blank -%}
                {%- assign option_value = option_values[forloop.index0] -%}
            {%- endif -%}
            {%- assign variety_handle = '' -%}
            {%- if variety_handles != blank -%}
                {%- assign variety_handle = variety_handles[forloop.index0] -%}
            {%- endif -%}
            <option
                value="{{ option_value | escape }}"
                {% if selected_option_value != blank %}
                    {% if selected_option_value == option_value %}
                        selected="selected"
                    {% endif %}
                {% else %}
                    {% if option_value == default_value %}
                        selected="selected"
                    {% endif %}
                {% endif %}
                {% if variety_handle != blank %}data-product-handle="{{ variety_handle }}"{% endif %}
            >{{ option }}</option>
        {%- endfor -%}
    </select>
</div>
