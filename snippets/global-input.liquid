{% liquid
  assign element = "input"
  if rows != blank
    assign element = "textarea"
  endif
%}

{% capture input_prefix %}
  {% unless prefix == blank %}
    <div class="global-input__prefix {{ prefix_classes }}">
      {{ prefix }}
    </div>
  {% endunless %}
{% endcapture %}

{% capture input_suffix %}
  {% unless suffix == blank %}
    <div class="global-input__suffix {{ suffix_classes }}">
      {{ suffix }}
    </div>
  {% endunless %}
{% endcapture %}

<div
  class="global-input__wrapper {{ wrapper_classes }}"
  {% if vue_wrapper_classes %}:class="{{ vue_wrapper_classes }}"{% endif %}
>
  <div class="global-input {{ inner_classes }}">
    {{ input_prefix }}
    <{{ element }}
      id="{{ id }}"
      name="{{ name }}"
      title="{{ title }}"
      class="p2 {{ input_classes }}"
      placeholder="{{ placeholder }}"
      {% if rows == blank %}
        type="{{ type }}"
      {% else %}
        rows="{{ rows }}"
      {% endif %}
      {% if required == true %}required{% endif %}
      {% if vue_model != blank %}v-model="{{ vue_model }}"{% endif %}
      {% if type == "number" %}inputmode="numeric"{% endif %}
      {% if pattern != blank %}pattern="{{ pattern }}"{% endif %}
    ></{{ element }}>
    {{ input_suffix }}
  </div>
</div>