{%- comment -%}
Description:
Snippet for displaying input checkbox

Variables:
- extra_class: Adding class to the input container
- text: String. text label for checkbox
- url: URL. If not blank, give extra text as link
- url_label: String. text for link
- id: String. Lowercase. Give ID to input and vue model
- name: String. Name for input checkbox
- required: <PERSON>olean. If true, will show error if need to be checked
- check: Boolean. If true, auto checked on first time
- error_message: String. Error text
- input_class: String. Class for input
- onchange: String. Function to call on change
{%- endcomment -%}
<div class="global-checkbox {{ extra_class }}">
    <label class="checkbox-label p3">
        {{ text }}
        {%- if url != blank and url_label != blank -%}
            <a href="{{ url }}" class="{{ url_class }}">{{ url_label }}</a>
        {%- endif -%}
        <input type="checkbox" name="{{ name }}" id="{{ id }}"  class="{{ input_class }}"  {% if required %}v-model="{{ id }}" :class="{'global-input-account-error' : {{ id }}_error}"{% endif %} {% if check %}checked="checked"{% endif %} 
         {% if onchange != blank %} onchange="{{ onchange }}" {% endif %}
        />
        <span class="checkmark"></span>
    </label>
    {% if required %}
        <div v-if="{{ id }}_error" class="global-error-msg p3 mt-8">*{{ error_message }}</div>
    {% endif %}
</div>