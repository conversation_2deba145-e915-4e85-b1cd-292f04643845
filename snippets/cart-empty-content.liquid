<style>
.empty-ctn-wrapper .col-12 {
    width: calc(100% - 60px);
}
@media screen and (min-width: 1024px) {
    .empty-ctn-wrapper .d-col-6 {
        width: calc(50% - 8px);
    }
}
</style>
{% # button %}
<div class="empty-ctn-wrapper container-max w-full flex flex-wrap rowgap-12 colgap-16 mt-16 d-mt-20">
    {%- if settings.cart_empty_btn_label1 != blank and settings.cart_empty_btn_url1 != blank and settings.cart_empty_btn_label2 != blank and settings.cart_empty_btn_url2 != blank %}
        <div class="col-12 d-col-6 tc d-tr mx-auto">
            <a href="{{ settings.cart_empty_btn_url1 }}" class="btn1 w-full max-170 d-max-185 tc" alt="{{ settings.cart_empty_btn_label1 | escape }}">{{ settings.cart_empty_btn_label1 }}</a>
        </div>
        <div class="col-12 d-col-6 tc d-tl mx-auto">
            <a href="{{ settings.cart_empty_btn_url2 }}" class="btn4 w-full max-170 d-max-185 tc" alt="{{ settings.cart_empty_btn_label2 | escape }}">{{ settings.cart_empty_btn_label2 }}</a>
        </div>
    {%- else -%}
        {%- if settings.cart_empty_btn_label1 != blank and settings.cart_empty_btn_url1 != blank %}
        <div class="col-12 tc mx-auto">
            <a href="{{ settings.cart_empty_btn_url1 }}" class="btn1 tc" alt="{{ settings.cart_empty_btn_label1 | escape }}">{{ settings.cart_empty_btn_label1 }}</a>
        </div>
        {%- endif -%}
        {%- if settings.cart_empty_btn_label2 != blank and settings.cart_empty_btn_url2 != blank %}
            <div class="col-12 tc mx-auto">
                <a href="{{ settings.cart_empty_btn_url2 }}" class="btn4 tc" alt="{{ settings.cart_empty_btn_label2 | escape }}">{{ settings.cart_empty_btn_label2 }}</a>
            </div>
        {%- endif -%}
    {%- endif -%}
</div>
{% # END button %}