{% comment %}
Description:
Snippet to display social links with icon

Variables:
- additional_class: String. Class for the wrapper
{% endcomment %}
<div class="global-social flex {{ additional_class }}">
    {%- assign social = "facebook,instagram,whatsapp,tiktok,youtube,twitter,pinterest,linkedin,telegram" | split:"," -%}
    {%- for s in social -%}
        {%- assign icon = s | append:"-icon" -%}
        {%- assign url = s | append:"-url" -%}
        {%- if settings[url] != blank -%}
            <a href="{{ settings[url] }}" class="{{ s }} flex ai-center jc-center {% unless forloop.last %}mr-25{% endunless %}" target="_blank">
                {{ settings[icon] }}
            </a>
        {%- endif -%}
    {%- endfor -%}
</div>