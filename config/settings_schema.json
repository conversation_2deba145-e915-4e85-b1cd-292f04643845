[
  {
    "name": "<PERSON> Settings",
    "settings": [
      {
        "type": "color",
        "id": "body_bg_color",
        "label": "Body background color",
        "default": "#faf6f5"
      }
    ]
  },
  {
    "name": "Image placeholder",
    "settings": [
      {
        "type": "image_picker",
        "id": "mobile_placeholder",
        "label": "Mobile"
      },
      {
        "type": "image_picker",
        "id": "desktop_placeholder",
        "label": "Desktop"
      }
    ]
  },
  {
    "name": "Favicon",
    "settings": [
      {
        "type": "image_picker",
        "id": "favicon",
        "label": "Favicon"
      }
    ]
  },
  {
    "name": "Typography",
    "settings": [
      {
        "type": "textarea",
        "id": "typography",
        "label": "Scripts for fonts"
      }
    ]
  },
  {
    "name": "Header",
    "settings": [
      {
        "type": "checkbox",
        "id": "header_sticky",
        "label": "Sticky header"
      },
      {
        "type": "checkbox",
        "id": "header_hide_bar_on_scroll",
        "label": "Hide bar on scroll",
        "info": "Hide announcement bar on user scroll the page"
      },
      {
        "type": "textarea",
        "id": "header_sticky_disable_page_types",
        "label": "Disable sticky header on certain page types",
        "info": "Enter comma separated values. Page type can check here https:\/\/shopify.dev\/docs\/api\/liquid\/objects\/request#request-page_type"
      },
      {
        "type": "textarea",
        "id": "header_transparen_pages",
        "label": "Transparen header pages",
        "info": "Enter 1 per line. For example \"\/\", \"\/pages\/contact\"",
        "default": "\/"
      },
      {
        "type": "header",
        "content": "Announcement bar"
      },
      {
        "type": "checkbox",
        "id": "show_announcement",
        "label": "Show announcement",
        "default": false
      },
      {
        "type": "color",
        "id": "announcement_bg",
        "label": "Background",
        "default": "#471F1D"
      },
      {
        "type": "color",
        "id": "announcement_color",
        "label": "Text",
        "default": "#FFFFFF"
      },
      {
        "type": "text",
        "id": "announcement_text",
        "label": "Message"
      },
      {
        "type": "url",
        "id": "announcement_link",
        "label": "Link for the text"
      },
      {
        "type": "text",
        "id": "announcement_end_time",
        "label": "Promo end date and time",
        "info": "Input format dd-mm-yyyy hh:mm:ss"
      }
    ]
  },
  {
    "name": "Homepage Json-LD",
    "settings": [
      {
        "type": "image_picker",
        "id": "home_jsonld_logo",
        "label": "Logo"
      },
      {
        "type": "header",
        "content": "Video"
      },
      {
        "type": "video",
        "id": "home_jsonld_video",
        "label": "Video"
      },
      {
        "type": "image_picker",
        "id": "home_jsonld_video_thumb",
        "label": "Video Image Thumbnail"
      },
      {
        "type": "text",
        "id": "home_jsonld_video_name",
        "label": "Video Name"
      },
      {
        "type": "textarea",
        "id": "home_jsonld_video_description",
        "label": "Video Description"
      }
    ]
  },
  {
    "name": "Icons",
    "settings": [
      {
        "type": "textarea",
        "id": "icon_menu",
        "label": "Menu"
      },
      {
        "type": "textarea",
        "id": "icon_search",
        "label": "Search"
      },
      {
        "type": "textarea",
        "id": "icon_account",
        "label": "Account"
      },
      {
        "type": "textarea",
        "id": "icon_cart",
        "label": "Cart"
      },
      {
        "type": "textarea",
        "id": "icon_close",
        "label": "Close"
      },
      {
        "type": "textarea",
        "id": "icon_accordion",
        "label": "Icon used for accordion"
      },
      {
        "type": "textarea",
        "id": "icon_slide_right",
        "label": "Right Arrow for slider"
      },
      {
        "type": "textarea",
        "id": "icon_slide_left",
        "label": "Left Arrow for slider"
      },
      {
        "type": "textarea",
        "id": "icon_slide_up",
        "label": "Up Arrow for slider"
      },
      {
        "type": "textarea",
        "id": "icon_slide_down",
        "label": "Down Arrow for slider"
      },
      {
        "type": "textarea",
        "id": "icon_select",
        "label": "Arrow for select element"
      },
      {
        "type": "textarea",
        "id": "icon_arrow_right",
        "label": "Arrow right"
      },
      {
        "type": "textarea",
        "id": "icon_arrow_left",
        "label": "Arrow left"
      },
      {
        "type": "textarea",
        "id": "icon_arrow_up_right",
        "label": "Arrow up right"
      },
      {
        "type": "textarea",
        "id": "icon_plus",
        "label": "Plus"
      },
      {
        "type": "textarea",
        "id": "icon_minus",
        "label": "Minus"
      },
      {
        "type": "textarea",
        "id": "icon_play",
        "label": "Play"
      },
      {
        "type": "textarea",
        "id": "icon_pause",
        "label": "Pause"
      },
      {
        "type": "textarea",
        "id": "icon_breadcrumb",
        "label": "Breadcrumb"
      },
      {
        "type": "textarea",
        "id": "icon_link_icon",
        "label": "Link Icon"
      },
      {
        "type": "textarea",
        "id": "icon_filter",
        "label": "Filter"
      },
      {
        "type": "textarea",
        "id": "icon_trash",
        "label": "Trash"
      },
      {
        "type": "textarea",
        "id": "icon_copy",
        "label": "Copy"
      },
      {
        "type": "textarea",
        "id": "icon_edit",
        "label": "Edit"
      },
      {
        "type": "textarea",
        "id": "icon_question",
        "label": "Question"
      },
      {
        "type": "textarea",
        "id": "icon_phone",
        "label": "Phone"
      },
      {
        "type": "textarea",
        "id": "icon_email",
        "label": "Email"
      },
      {
        "type": "textarea",
        "id": "icon_link",
        "label": "Link"
      },
      {
        "type": "textarea",
        "id": "icon_star_hollow",
        "label": "Star hollow"
      },
      {
        "type": "textarea",
        "id": "icon_star",
        "label": "Star"
      },
      {
        "type": "textarea",
        "id": "icon_secure_checkout",
        "label": "Secure checkout"
      },
      {
        "type": "textarea",
        "id": "icon_sale",
        "label": "Sale"
      },
      {
        "type": "textarea",
        "id": "icon_wishlist",
        "label": "Wishlist"
      },
      {
        "type": "textarea",
        "id": "icon_wishlist_added",
        "label": "Wishlist Added"
      },
      {
        "type": "textarea",
        "id": "icon_loading",
        "label": "Loading"
      },
      {
        "type": "textarea",
        "id": "icon_notification",
        "label": "Notification"
      },
      {
        "type": "textarea",
        "id": "icon_caret_left",
        "label": "Caret left"
      },
      {
        "type": "textarea",
        "id": "icon_caret_right",
        "label": "Caret right"
      },
      {
        "type": "textarea",
        "id": "icon_share",
        "label": "Share"
      },
      {
        "type": "textarea",
        "id": "icon_tag",
        "label": "Tag list"
      },
      {
        "type": "textarea",
        "id": "icon_checklist",
        "label": "Check list"
      },
      {
        "type": "textarea",
        "id": "icon_pointer",
        "label": "Pointer on Image"
      },
      {
        "type": "textarea",
        "id": "icon_pointer_active",
        "label": "Pointer Active on Image"
      },
      {
        "type": "textarea",
        "id": "icon_calendar",
        "label": "Calendar"
      },
      {
        "type": "textarea",
        "id": "icon_calendar_gold",
        "label": "Calendar Gold"
      },
      {
        "type": "textarea",
        "id": "icon_clock",
        "label": "Clock"
      },
      {
        "type": "textarea",
        "id": "icon_chef_hat",
        "label": "Chef Hat"
      },
      {
        "type": "textarea",
        "id": "icon_show_password",
        "label": "Show Password"
      },
      {
        "type": "textarea",
        "id": "icon_hide_password",
        "label": "Hide Password"
      },
      {
        "type": "textarea",
        "id": "icon_map_pin",
        "label": "Map Pin"
      },
      {
        "type": "textarea",
        "id": "icon_email_outline",
        "label": "Email (Outline)"
      },
      {
        "type": "textarea",
        "id": "icon_support_outline",
        "label": "Support (Outline)"
      }
    ]
  },
  {
    "name": "Social Media",
    "settings": [
      {
        "type": "textarea",
        "id": "facebook-icon",
        "label": "Facebook Icon (SVG)"
      },
      {
        "type": "textarea",
        "id": "facebook-url",
        "label": "Facebook URL"
      },
      {
        "type": "textarea",
        "id": "instagram-icon",
        "label": "Instagram Icon (SVG)"
      },
      {
        "type": "textarea",
        "id": "instagram-url",
        "label": "Instagram URL"
      },
      {
        "type": "textarea",
        "id": "tiktok-icon",
        "label": "Tiktok Icon (SVG)"
      },
      {
        "type": "textarea",
        "id": "tiktok-url",
        "label": "Tiktok URL"
      },
      {
        "type": "textarea",
        "id": "pinterest-icon",
        "label": "Pinterest Icon (SVG)"
      },
      {
        "type": "textarea",
        "id": "pinterest-url",
        "label": "Pinterest URL"
      },
      {
        "type": "textarea",
        "id": "youtube-icon",
        "label": "Youtube Icon (SVG)"
      },
      {
        "type": "textarea",
        "id": "youtube-url",
        "label": "Youtube URL"
      },
      {
        "type": "textarea",
        "id": "twitter-icon",
        "label": "Twitter Icon (SVG)"
      },
      {
        "type": "textarea",
        "id": "twitter-url",
        "label": "Twitter URL"
      },
      {
        "type": "textarea",
        "id": "linkedin-icon",
        "label": "Linkedin Icon (SVG)"
      },
      {
        "type": "textarea",
        "id": "linkedin-url",
        "label": "Linkedin URL"
      },
      {
        "type": "textarea",
        "id": "whatsapp-icon",
        "label": "Whatsapp Icon (SVG)"
      },
      {
        "type": "textarea",
        "id": "whatsapp-url",
        "label": "Whatsapp URL"
      },
      {
        "type": "textarea",
        "id": "telegram-icon",
        "label": "Telegram Icon (SVG)"
      },
      {
        "type": "textarea",
        "id": "telegram-url",
        "label": "Telegram URL"
      },
      {
        "type": "textarea",
        "id": "email-icon",
        "label": "Email Icon (SVG)"
      }
    ]
  },
  {
    "name": "Product Labels (Tag)",
    "settings": [
      {
        "type": "checkbox",
        "id": "pg_enable_labels",
        "label": "Enable Labels"
      },
      {
        "type": "text",
        "id": "pg_tag_1",
        "label": "Tag 1",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "color",
        "id": "pg_tag_bg_1",
        "label": "Background color for tag 1"
      },
      {
        "type": "color",
        "id": "pg_tag_color_1",
        "label": "Text color for tag 1"
      },
      {
        "type": "color",
        "id": "pg_tag_border_1",
        "label": "Border color for tag 1"
      },
      {
        "type": "text",
        "id": "pg_tag_2",
        "label": "Tag 2",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "color",
        "id": "pg_tag_bg_2",
        "label": "Background color for tag 2"
      },
      {
        "type": "color",
        "id": "pg_tag_color_2",
        "label": "Text color for tag 2"
      },
      {
        "type": "color",
        "id": "pg_tag_border_2",
        "label": "Border color for tag 2"
      },
      {
        "type": "text",
        "id": "pg_tag_3",
        "label": "Tag 3",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "color",
        "id": "pg_tag_bg_3",
        "label": "Background color for tag 3"
      },
      {
        "type": "color",
        "id": "pg_tag_color_3",
        "label": "Text color for tag 3"
      },
      {
        "type": "color",
        "id": "pg_tag_border_3",
        "label": "Border color for tag 3"
      },
      {
        "type": "text",
        "id": "pg_tag_4",
        "label": "Tag 4",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "color",
        "id": "pg_tag_bg_4",
        "label": "Background color for tag 4"
      },
      {
        "type": "color",
        "id": "pg_tag_color_4",
        "label": "Text color for tag 4"
      },
      {
        "type": "color",
        "id": "pg_tag_border_4",
        "label": "Border color for tag 4"
      },
      {
        "type": "text",
        "id": "pg_tag_5",
        "label": "Tag 5",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "color",
        "id": "pg_tag_bg_5",
        "label": "Background color for tag 5"
      },
      {
        "type": "color",
        "id": "pg_tag_color_5",
        "label": "Text color for tag 5"
      },
      {
        "type": "color",
        "id": "pg_tag_border_5",
        "label": "Border color for tag 5"
      },
      {
        "type": "text",
        "id": "pg_tag_6",
        "label": "Tag 6",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "color",
        "id": "pg_tag_bg_6",
        "label": "Background color for tag 6"
      },
      {
        "type": "color",
        "id": "pg_tag_color_6",
        "label": "Text color for tag 6"
      },
      {
        "type": "color",
        "id": "pg_tag_border_6",
        "label": "Border color for tag 6"
      },
      {
        "type": "text",
        "id": "pg_tag_7",
        "label": "Tag 7",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "color",
        "id": "pg_tag_bg_7",
        "label": "Background color for tag 7"
      },
      {
        "type": "color",
        "id": "pg_tag_color_7",
        "label": "Text color for tag 7"
      },
      {
        "type": "color",
        "id": "pg_tag_border_7",
        "label": "Border color for tag 7"
      },
      {
        "type": "text",
        "id": "pg_tag_8",
        "label": "Tag 8",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "color",
        "id": "pg_tag_bg_8",
        "label": "Background color for tag 8"
      },
      {
        "type": "color",
        "id": "pg_tag_color_8",
        "label": "Text color for tag 8"
      },
      {
        "type": "color",
        "id": "pg_tag_border_8",
        "label": "Border color for tag 8"
      },
      {
        "type": "text",
        "id": "pg_tag_9",
        "label": "Tag 9",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "color",
        "id": "pg_tag_bg_9",
        "label": "Background color for tag 9"
      },
      {
        "type": "color",
        "id": "pg_tag_color_9",
        "label": "Text color for tag 9"
      },
      {
        "type": "color",
        "id": "pg_tag_border_9",
        "label": "Border color for tag 9"
      },
      {
        "type": "text",
        "id": "pg_tag_10",
        "label": "Tag 10",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "color",
        "id": "pg_tag_bg_10",
        "label": "Background color for tag 10"
      },
      {
        "type": "color",
        "id": "pg_tag_color_10",
        "label": "Text color for tag 10"
      },
      {
        "type": "color",
        "id": "pg_tag_border_10",
        "label": "Border color for tag 10"
      }
    ]
  },
  {
    "name": "Product Labels (Tag) 2",
    "settings": [
      {
        "type": "checkbox",
        "id": "pg2_enable_labels",
        "label": "Enable Labels"
      },
      {
        "type": "text",
        "id": "pg2_tag_1",
        "label": "Tag 1",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_1",
        "label": "Label 1",
        "info": "Label for Tag 1"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_1",
        "label": "Background color for tag 1"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_1",
        "label": "Text color for tag 1"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_1",
        "label": "Border color for tag 1"
      },
      {
        "type": "text",
        "id": "pg2_tag_2",
        "label": "Tag 2",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_2",
        "label": "Label 2",
        "info": "Label for Tag 2"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_2",
        "label": "Background color for tag 2"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_2",
        "label": "Text color for tag 2"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_2",
        "label": "Border color for tag 2"
      },
      {
        "type": "text",
        "id": "pg2_tag_3",
        "label": "Tag 3",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_3",
        "label": "Label 3",
        "info": "Label for Tag 3"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_3",
        "label": "Background color for tag 3"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_3",
        "label": "Text color for tag 3"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_3",
        "label": "Border color for tag 3"
      },
      {
        "type": "text",
        "id": "pg2_tag_4",
        "label": "Tag 4",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_4",
        "label": "Label 4",
        "info": "Label for Tag 4"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_4",
        "label": "Background color for tag 4"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_4",
        "label": "Text color for tag 4"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_4",
        "label": "Border color for tag 4"
      },
      {
        "type": "text",
        "id": "pg2_tag_5",
        "label": "Tag 5",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_5",
        "label": "Label 5",
        "info": "Label for Tag 5"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_5",
        "label": "Background color for tag 5"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_5",
        "label": "Text color for tag 5"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_5",
        "label": "Border color for tag 5"
      },
      {
        "type": "text",
        "id": "pg2_tag_6",
        "label": "Tag 6",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_6",
        "label": "Label 6",
        "info": "Label for Tag 6"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_6",
        "label": "Background color for tag 6"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_6",
        "label": "Text color for tag 6"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_6",
        "label": "Border color for tag 6"
      },
      {
        "type": "text",
        "id": "pg2_tag_7",
        "label": "Tag 7",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_7",
        "label": "Label 7",
        "info": "Label for Tag 7"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_7",
        "label": "Background color for tag 7"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_7",
        "label": "Text color for tag 7"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_7",
        "label": "Border color for tag 7"
      },
      {
        "type": "text",
        "id": "pg2_tag_8",
        "label": "Tag 8",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_8",
        "label": "Label 8",
        "info": "Label for Tag 8"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_8",
        "label": "Background color for tag 8"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_8",
        "label": "Text color for tag 8"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_8",
        "label": "Border color for tag 8"
      },
      {
        "type": "text",
        "id": "pg2_tag_9",
        "label": "Tag 9",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_9",
        "label": "Label 9",
        "info": "Label for Tag 9"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_9",
        "label": "Background color for tag 9"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_9",
        "label": "Text color for tag 9"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_9",
        "label": "Border color for tag 9"
      },
      {
        "type": "text",
        "id": "pg2_tag_10",
        "label": "Tag 10",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_10",
        "label": "Label 10",
        "info": "Label for Tag 10"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_10",
        "label": "Background color for tag 10"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_10",
        "label": "Text color for tag 10"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_10",
        "label": "Border color for tag 10"
      },
      {
        "type": "text",
        "id": "pg2_tag_11",
        "label": "Tag 11",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_11",
        "label": "Label 11",
        "info": "Label for Tag 11"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_11",
        "label": "Background color for tag 11"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_11",
        "label": "Text color for tag 11"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_11",
        "label": "Border color for tag 11"
      },
      {
        "type": "text",
        "id": "pg2_tag_12",
        "label": "Tag 12",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_12",
        "label": "Label 12",
        "info": "Label for Tag 12"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_12",
        "label": "Background color for tag 12"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_12",
        "label": "Text color for tag 12"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_12",
        "label": "Border color for tag 12"
      },
      {
        "type": "text",
        "id": "pg2_tag_13",
        "label": "Tag 13",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_13",
        "label": "Label 13",
        "info": "Label for Tag 13"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_13",
        "label": "Background color for tag 13"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_13",
        "label": "Text color for tag 13"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_13",
        "label": "Border color for tag 13"
      },
      {
        "type": "text",
        "id": "pg2_tag_14",
        "label": "Tag 14",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_14",
        "label": "Label 14",
        "info": "Label for Tag 14"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_14",
        "label": "Background color for tag 14"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_14",
        "label": "Text color for tag 14"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_14",
        "label": "Border color for tag 14"
      },
      {
        "type": "text",
        "id": "pg2_tag_15",
        "label": "Tag 15",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_15",
        "label": "Label 15",
        "info": "Label for Tag 15"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_15",
        "label": "Background color for tag 15"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_15",
        "label": "Text color for tag 15"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_15",
        "label": "Border color for tag 15"
      },
      {
        "type": "text",
        "id": "pg2_tag_16",
        "label": "Tag 16",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_16",
        "label": "Label 16",
        "info": "Label for Tag 16"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_16",
        "label": "Background color for tag 16"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_16",
        "label": "Text color for tag 16"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_16",
        "label": "Border color for tag 16"
      },
      {
        "type": "text",
        "id": "pg2_tag_17",
        "label": "Tag 17",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_17",
        "label": "Label 17",
        "info": "Label for Tag 17"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_17",
        "label": "Background color for tag 17"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_17",
        "label": "Text color for tag 17"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_17",
        "label": "Border color for tag 17"
      },
      {
        "type": "text",
        "id": "pg2_tag_18",
        "label": "Tag 18",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_18",
        "label": "Label 18",
        "info": "Label for Tag 18"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_18",
        "label": "Background color for tag 18"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_18",
        "label": "Text color for tag 18"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_18",
        "label": "Border color for tag 18"
      },
      {
        "type": "text",
        "id": "pg2_tag_19",
        "label": "Tag 19",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_19",
        "label": "Label 19",
        "info": "Label for Tag 19"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_19",
        "label": "Background color for tag 19"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_19",
        "label": "Text color for tag 19"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_19",
        "label": "Border color for tag 19"
      },
      {
        "type": "text",
        "id": "pg2_tag_20",
        "label": "Tag 20",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_20",
        "label": "Label 20",
        "info": "Label for Tag 20"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_20",
        "label": "Background color for tag 20"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_20",
        "label": "Text color for tag 20"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_20",
        "label": "Border color for tag 20"
      },
      {
        "type": "text",
        "id": "pg2_tag_21",
        "label": "Tag 21",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_21",
        "label": "Label 21",
        "info": "Label for Tag 21"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_21",
        "label": "Background color for tag 21"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_21",
        "label": "Text color for tag 21"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_21",
        "label": "Border color for tag 21"
      },
      {
        "type": "text",
        "id": "pg2_tag_22",
        "label": "Tag 22",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_22",
        "label": "Label 22",
        "info": "Label for Tag 22"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_22",
        "label": "Background color for tag 22"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_22",
        "label": "Text color for tag 22"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_22",
        "label": "Border color for tag 22"
      },
      {
        "type": "text",
        "id": "pg2_tag_23",
        "label": "Tag 23",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_23",
        "label": "Label 23",
        "info": "Label for Tag 23"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_23",
        "label": "Background color for tag 23"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_23",
        "label": "Text color for tag 23"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_23",
        "label": "Border color for tag 23"
      },
      {
        "type": "text",
        "id": "pg2_tag_24",
        "label": "Tag 24",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_24",
        "label": "Label 24",
        "info": "Label for Tag 24"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_24",
        "label": "Background color for tag 24"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_24",
        "label": "Text color for tag 24"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_24",
        "label": "Border color for tag 24"
      },
      {
        "type": "text",
        "id": "pg2_tag_25",
        "label": "Tag 25",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_25",
        "label": "Label 25",
        "info": "Label for Tag 25"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_25",
        "label": "Background color for tag 25"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_25",
        "label": "Text color for tag 25"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_25",
        "label": "Border color for tag 25"
      },
      {
        "type": "text",
        "id": "pg2_tag_26",
        "label": "Tag 26",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_26",
        "label": "Label 26",
        "info": "Label for Tag 26"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_26",
        "label": "Background color for tag 26"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_26",
        "label": "Text color for tag 26"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_26",
        "label": "Border color for tag 26"
      },
      {
        "type": "text",
        "id": "pg2_tag_27",
        "label": "Tag 27",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_27",
        "label": "Label 27",
        "info": "Label for Tag 27"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_27",
        "label": "Background color for tag 27"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_27",
        "label": "Text color for tag 27"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_27",
        "label": "Border color for tag 27"
      },
      {
        "type": "text",
        "id": "pg2_tag_28",
        "label": "Tag 28",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_28",
        "label": "Label 28",
        "info": "Label for Tag 28"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_28",
        "label": "Background color for tag 28"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_28",
        "label": "Text color for tag 28"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_28",
        "label": "Border color for tag 28"
      },
      {
        "type": "text",
        "id": "pg2_tag_29",
        "label": "Tag 29",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_29",
        "label": "Label 29",
        "info": "Label for Tag 29"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_29",
        "label": "Background color for tag 29"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_29",
        "label": "Text color for tag 29"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_29",
        "label": "Border color for tag 29"
      },
      {
        "type": "text",
        "id": "pg2_tag_30",
        "label": "Tag 30",
        "info": "Label will show if the product tags have this tag."
      },
      {
        "type": "text",
        "id": "pg2_label_30",
        "label": "Label 30",
        "info": "Label for Tag 30"
      },
      {
        "type": "color",
        "id": "pg2_tag_bg_30",
        "label": "Background color for tag 30"
      },
      {
        "type": "color",
        "id": "pg2_tag_color_30",
        "label": "Text color for tag 30"
      },
      {
        "type": "color",
        "id": "pg2_tag_border_30",
        "label": "Border color for tag 30"
      }
    ]
  },
  {
    "name": "Product Promo Message",
    "settings": [
      {
        "type": "text",
        "id": "product_global_promo_msg_title",
        "label": "Global Promo Message Title"
      },
      {
        "type": "richtext",
        "id": "product_global_promo_msg",
        "label": "Global Promo Message"
      },
      {
        "type": "header",
        "content": "Promo Message by Product Tag"
      },
      {
        "type": "text",
        "id": "product_promo_msg_tag1",
        "label": "Product Tag 1"
      },
      {
        "type": "text",
        "id": "product_promo_msg_title1",
        "label": "Promo Message 1"
      },
      {
        "type": "richtext",
        "id": "product_promo_msg1",
        "label": "Promo Message 1"
      },
      {
        "type": "text",
        "id": "product_promo_msg_tag2",
        "label": "Product Tag 2"
      },
      {
        "type": "text",
        "id": "product_promo_msg_title2",
        "label": "Promo Message 2"
      },
      {
        "type": "richtext",
        "id": "product_promo_msg2",
        "label": "Promo Message 2"
      },
      {
        "type": "text",
        "id": "product_promo_msg_tag3",
        "label": "Product Tag 3"
      },
      {
        "type": "text",
        "id": "product_promo_msg_title3",
        "label": "Promo Message 3"
      },
      {
        "type": "richtext",
        "id": "product_promo_msg3",
        "label": "Promo Message 3"
      },
      {
        "type": "text",
        "id": "product_promo_msg_tag4",
        "label": "Product Tag 4"
      },
      {
        "type": "text",
        "id": "product_promo_msg_title4",
        "label": "Promo Message 4"
      },
      {
        "type": "richtext",
        "id": "product_promo_msg4",
        "label": "Promo Message 4"
      },
      {
        "type": "text",
        "id": "product_promo_msg_tag5",
        "label": "Product Tag 5"
      },
      {
        "type": "text",
        "id": "product_promo_msg_title5",
        "label": "Promo Message 5"
      },
      {
        "type": "richtext",
        "id": "product_promo_msg5",
        "label": "Promo Message 5"
      }
    ]
  },
  {
    "name": "Product Accordion",
    "settings": [
      {
        "type": "text",
        "id": "pdp_info_title_1",
        "label": "Info - Title #1"
      },
      {
        "type": "richtext",
        "id": "pdp_info_content_1",
        "label": "Info - Content #1"
      },
      {
        "type": "text",
        "id": "pdp_info_title_2",
        "label": "Info - Title #2"
      },
      {
        "type": "richtext",
        "id": "pdp_info_content_2",
        "label": "Info - Content #2"
      },
      {
        "type": "text",
        "id": "pdp_info_title_3",
        "label": "Info - Title #3"
      },
      {
        "type": "richtext",
        "id": "pdp_info_content_3",
        "label": "Info - Content #3"
      },
      {
        "type": "text",
        "id": "pdp_info_title_4",
        "label": "Info - Title #4"
      },
      {
        "type": "richtext",
        "id": "pdp_info_content_4",
        "label": "Info - Content #4"
      },
      {
        "type": "text",
        "id": "pdp_info_title_5",
        "label": "Info - Title #5"
      },
      {
        "type": "richtext",
        "id": "pdp_info_content_5",
        "label": "Info - Content #5"
      }
    ]
  },
  {
    "name": "Cart",
    "settings": [
      {
        "type": "header",
        "content": "General Message"
      },
      {
        "type": "textarea",
        "id": "cart_empty_title",
        "label": "Cart Empty Title"
      },
      {
        "type": "textarea",
        "id": "cart_empty_msg",
        "label": "Cart Empty Message"
      },
      {
        "type": "text",
        "id": "cart_empty_btn_label1",
        "label": "Cart Empty Button Label 1"
      },
      {
        "type": "url",
        "id": "cart_empty_btn_url1",
        "label": "Cart Empty Button URL 1"
      },
      {
        "type": "text",
        "id": "cart_empty_btn_label2",
        "label": "Cart Empty Button Label 2"
      },
      {
        "type": "url",
        "id": "cart_empty_btn_url2",
        "label": "Cart Empty Button URL 2"
      },
      {
        "type": "header",
        "content": "Free Shipping"
      },
      {
        "type": "checkbox",
        "id": "cart_enable_fs_info",
        "label": "Enable free shipping info"
      },
      {
        "type": "header",
        "content": "Member Free Shipping"
      },
      {
        "type": "number",
        "id": "cart_fs_discount",
        "label": "Amount Free Shipping"
      },
      {
        "type": "select",
        "id": "cart_fs_discount_type",
        "label": "Free Shipping Type",
        "options": [
          {
            "value": "fixed",
            "label": "Fixed Amount"
          },
          {
            "value": "percent",
            "label": "Percent"
          }
        ],
        "default": "percent"
      },
      {
        "type": "text",
        "id": "cart_fs_free",
        "label": "Total spending for free shipping"
      },
      {
        "type": "textarea",
        "id": "cart_fs_before",
        "label": "Message before free shipping is reached",
        "default": "Spend [amount] more to enjoy free delivery on your order!"
      },
      {
        "type": "textarea",
        "id": "cart_fs_after",
        "label": "Message after free shipping is reached",
        "default": "You have free shipping for this order!"
      },
      {
        "type": "header",
        "content": "Guest Free Shipping"
      },
      {
        "type": "number",
        "id": "cart_fs_discount2",
        "label": "Amount Free Shipping"
      },
      {
        "type": "select",
        "id": "cart_fs_discount_type2",
        "label": "Free Shipping Type",
        "options": [
          {
            "value": "fixed",
            "label": "Fixed Amount"
          },
          {
            "value": "percent",
            "label": "Percent"
          }
        ],
        "default": "percent"
      },
      {
        "type": "text",
        "id": "cart_fs_free2",
        "label": "Total spending for free shipping"
      },
      {
        "type": "textarea",
        "id": "cart_fs_before2",
        "label": "Message before free shipping is reached",
        "default": "Spend [amount] more to enjoy free delivery on your order!"
      },
      {
        "type": "textarea",
        "id": "cart_fs_after2",
        "label": "Message after free shipping is reached",
        "default": "You have free shipping for this order!"
      },
      {
        "type": "header",
        "content": "Cross Sell"
      },
      {
        "type": "text",
        "id": "cart_cross_sell_title",
        "label": "Cross Sell Title",
        "default": "You might also like"
      },
      {
        "type": "collection",
        "id": "cart_cross_sell_collection",
        "label": "Cross sell collection"
      },
      {
        "type": "number",
        "id": "cart_cross_sell_limit",
        "label": "Product limit",
        "default": 15
      },
      {
        "type": "header",
        "content": "Loyalty Program"
      },
      {
        "type": "checkbox",
        "id": "cart_loyalty_enabled",
        "label": "Enable Loyalty Program",
        "default": false
      },
      {
        "type": "image_picker",
        "id": "cart_loyalty_icon",
        "label": "Icon Image"
      },
      {
        "type": "textarea",
        "id": "cart_loyalty_member_msg",
        "label": "Member Message",
        "default": "Points Awarded"
      },
      {
        "type": "textarea",
        "id": "cart_loyalty_nonmember_msg",
        "label": "Non Member Message",
        "default": "<a href='/account/login'>Log in</a> or <a href='/account'>Sign up</a> to start earning points!"
      },
      {
        "type": "header",
        "content": "Terms and Conditions"
      },
      {
        "type": "textarea",
        "id": "cart_tnc_msg",
        "label": "TnC Message",
        "default": "By checking out, you agree to our <a href='/'>Terms & Conditions</a> and <a href='/'>Privacy Policy</a>."
      },
      {
        "type": "header",
        "content": "Image Banner"
      },
      {
        "type": "image_picker",
        "id": "cart_banner_image1",
        "label": "Image 1"
      },
      {
        "type": "text",
        "id": "cart_banner_title1",
        "label": "Title on Image 1"
      },
      {
        "type": "url",
        "id": "cart_banner_link1",
        "label": "Link on Image 1"
      },
      {
        "type": "checkbox",
        "id": "cart_banner_overlay1",
        "label": "Overlay on Image 1"
      },
      {
        "type": "select",
        "id": "cart_banner_color1",
        "label": "Text Color on Image 1",
        "options": [
          {
            "value": "light",
            "label": "Light"
          },
          {
            "value": "dark",
            "label": "Dark"
          }
        ],
        "default": "light"
      },
      {
        "type": "image_picker",
        "id": "cart_banner_image2",
        "label": "Image 2"
      },
      {
        "type": "text",
        "id": "cart_banner_title2",
        "label": "Title on Image 2"
      },
      {
        "type": "url",
        "id": "cart_banner_link2",
        "label": "Link on Image 2"
      },
      {
        "type": "checkbox",
        "id": "cart_banner_overlay2",
        "label": "Overlay on Image 2"
      },
      {
        "type": "select",
        "id": "cart_banner_color2",
        "label": "Text Color on Image 2",
        "options": [
          {
            "value": "light",
            "label": "Light"
          },
          {
            "value": "dark",
            "label": "Dark"
          }
        ],
        "default": "light"
      },
      {
        "type": "image_picker",
        "id": "cart_banner_image3",
        "label": "Image 3"
      },
      {
        "type": "text",
        "id": "cart_banner_title3",
        "label": "Title on Image 3"
      },
      {
        "type": "url",
        "id": "cart_banner_link3",
        "label": "Link on Image 3"
      },
      {
        "type": "checkbox",
        "id": "cart_banner_overlay3",
        "label": "Overlay on Image 3"
      },
      {
        "type": "select",
        "id": "cart_banner_color3",
        "label": "Text Color on Image 3",
        "options": [
          {
            "value": "light",
            "label": "Light"
          },
          {
            "value": "dark",
            "label": "Dark"
          }
        ],
        "default": "light"
      },
      {
        "type": "image_picker",
        "id": "cart_banner_image4",
        "label": "Image 4"
      },
      {
        "type": "text",
        "id": "cart_banner_title4",
        "label": "Title on Image 4"
      },
      {
        "type": "url",
        "id": "cart_banner_link4",
        "label": "Link on Image 4"
      },
      {
        "type": "checkbox",
        "id": "cart_banner_overlay4",
        "label": "Overlay on Image 4"
      },
      {
        "type": "select",
        "id": "cart_banner_color4",
        "label": "Text Color on Image 4",
        "options": [
          {
            "value": "light",
            "label": "Light"
          },
          {
            "value": "dark",
            "label": "Dark"
          }
        ],
        "default": "light"
      },
      {
        "type": "image_picker",
        "id": "cart_banner_image5",
        "label": "Image 5"
      },
      {
        "type": "text",
        "id": "cart_banner_title5",
        "label": "Title on Image 5"
      },
      {
        "type": "url",
        "id": "cart_banner_link5",
        "label": "Link on Image 5"
      },
      {
        "type": "checkbox",
        "id": "cart_banner_overlay5",
        "label": "Overlay on Image 5"
      },
      {
        "type": "select",
        "id": "cart_banner_color5",
        "label": "Text Color on Image 5",
        "options": [
          {
            "value": "light",
            "label": "Light"
          },
          {
            "value": "dark",
            "label": "Dark"
          }
        ],
        "default": "light"
      },
      {
        "type": "image_picker",
        "id": "cart_banner_image6",
        "label": "Image 6"
      },
      {
        "type": "text",
        "id": "cart_banner_title6",
        "label": "Title on Image 6"
      },
      {
        "type": "url",
        "id": "cart_banner_link6",
        "label": "Link on Image 6"
      },
      {
        "type": "checkbox",
        "id": "cart_banner_overlay6",
        "label": "Overlay on Image 6"
      },
      {
        "type": "select",
        "id": "cart_banner_color6",
        "label": "Text Color on Image 6",
        "options": [
          {
            "value": "light",
            "label": "Light"
          },
          {
            "value": "dark",
            "label": "Dark"
          }
        ],
        "default": "light"
      },
      {
        "type": "image_picker",
        "id": "cart_banner_image7",
        "label": "Image 7"
      },
      {
        "type": "text",
        "id": "cart_banner_title7",
        "label": "Title on Image 7"
      },
      {
        "type": "url",
        "id": "cart_banner_link7",
        "label": "Link on Image 7"
      },
      {
        "type": "checkbox",
        "id": "cart_banner_overlay7",
        "label": "Overlay on Image 7"
      },
      {
        "type": "select",
        "id": "cart_banner_color7",
        "label": "Text Color on Image 7",
        "options": [
          {
            "value": "light",
            "label": "Light"
          },
          {
            "value": "dark",
            "label": "Dark"
          }
        ],
        "default": "light"
      }
    ]
  },
  {
    "name": "Product",
    "settings": [
      {
        "type": "header",
        "content": "General Settings"
      },
      {
        "type": "text",
        "id": "product_variants_for_color_swatch",
        "label": "Variant names for color swatch",
        "default": "Color",
        "info": "Enter variant names separated by comma. For example: Color,Size,Material"
      },
      {
        "type": "header",
        "content": "Product Card"
      },
      {
        "type": "checkbox",
        "id": "product_card_image_hover",
        "label": "Change image on hover"
      },
      {
        "type": "header",
        "content": "Loyalty"
      },
      {
        "type": "number",
        "id": "product_loyalty_value",
        "label": "Loyalty Point Value",
        "default": 1,
        "info": "Loyalty point value in total order spent for 1 point. For example, if you want to give 1 point for every $10 spent, enter 10 here."
      },
      {
        "type": "image_picker",
        "id": "product_loyalty_icon",
        "label": "Loyalty Message Icon",
      },
      {
        "type": "richtext",
        "id": "product_loyalty_message",
        "label": "Loyalty Message",
        "info": "Put [points] to show points"
      }
    ]
  },
  {
    "name": "Collection & Search",
    "settings": [
      {
        "type": "header",
        "content": "General Settings"
      },
      {
        "type": "radio",
        "id": "pagination_type",
        "label": "Pagination type",
        "options": [
          {
            "value": "pagination",
            "label": "Normal pagination"
          },
          {
            "value": "show_more",
            "label": "Show more button"
          }
        ],
        "default": "pagination"
      },
      {
        "type": "checkbox",
        "id": "hide_pagination",
        "label": "Hide pagination"
      },
      {
        "type": "header",
        "content": "Collection Settings"
      },
      {
        "type": "number",
        "id": "collection_products_number",
        "label": "Collection page products per page",
        "default": 24
      },
      {
        "type": "header",
        "content": "Search Settings"
      },
      {
        "type": "product_list",
        "id": "search_featured_products",
        "label": "Search featured products",
        "limit": 4,
        "info": "Featured products in search dropdown"
      },
      {
        "type": "number",
        "id": "search_products_number",
        "label": "Search page products per page",
        "default": 24
      },
      {
        "type": "checkbox",
        "id": "pred_search_show_articles",
        "label": "Show articles",
        "default": true,
        "info": "Show articles in predictive search"
      },
      {
        "type": "checkbox",
        "id": "pred_search_show_pages",
        "label": "Show pages",
        "default": true,
        "info": "Show pages in predictive search"
      },
      {
        "type": "checkbox",
        "id": "pred_search_show_collections",
        "label": "Show collections",
        "default": true,
        "info": "Show collections in predictive search"
      },
      {
        "type": "text",
        "id": "pred_search_no_result_btn_label",
        "label": "No result button label"
      },
      {
        "type": "url",
        "id": "pred_search_no_result_btn_url",
        "label": "No result button URL"
      }
    ]
  },
  {
    "name": "Blog Listing",
    "settings": [
      {
        "type": "header",
        "content": "Featured Article"
      },
      {
        "type": "article",
        "id": "featured_article",
        "label": "Choose an article for Featured"
      },
      {
        "type": "header",
        "content": "List Settings"
      },
      {
        "type": "range",
        "id": "posts_per_page",
        "label": "Posts per page",
        "min": 3,
        "max": 50,
        "step": 1,
        "default": 12
      }
    ]
  },
  {
    "name": "Scripts Code",
    "settings": [
      {
        "type": "textarea",
        "id": "script_head",
        "label": "Script Head",
        "info": "Script inside <head>"
      },
      {
        "type": "textarea",
        "id": "script_body",
        "label": "Script Body",
        "info": "Script after open <body>"
      },
      {
        "type": "textarea",
        "id": "script_footer",
        "label": "Script Footer",
        "info": "Script on the footer"
      }
    ]
  }
]
