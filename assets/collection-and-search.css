.cns__topbar button {
  display: flex;
  align-items: center;
  column-gap: 8px;
}

.cns__topbar button i {
  width: 13px;
  display: flex;
  align-items: center;
}

.cns__select .icon {
  position: relative;
  right: unset;
  top: unset;
  transform: unset;
  width: 12px;
}

.cns__select.active .icon {
  transform: rotate(180deg);
}

.cns__select .text {
  margin-right: 8px !important;
}

.cns__select .options {
  margin-top: 16px;
  border-radius: 0 0 8px 8px;
  outline: unset;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.10);
  width: max-content;
  left: unset;
  right: 0;
}

.cns__select .options li {
  opacity: 0.3;
  padding: 3px 16px;
}

.cns__select .options li:hover {
  opacity: 1;
}

.cns__select .options li.active,
.cns__select .options li:hover {
  background: none;
  opacity: 1;
}

.cns__select .options li:first-child {
  padding-top: 12px;
}

.cns__select .options li:last-child {
  padding-bottom: 12px;
}

.cns__drawer .cns__overlay {
  position: fixed;
  inset: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(31, 31, 31, 0.60);
  opacity: 0;
  pointer-events: none;
  transition: all .3s ease;
}

.cns__drawer-inner {
  position: fixed;
  bottom: 0;
  left: 0;
  transform: translateY(100%);
  background: var(--white);
  height: 90vh;
  width: 100%;
  transition: all .3s ease;
  border-radius: 6px 6px 0 0;
}

.cns__drawer[data-show="true"] .cns__overlay {
  opacity: 1;
  pointer-events: unset;
  transition: all .3s ease;
}

.cns__drawer[data-show="true"] .cns__drawer-inner {
  transform: translateY(0%);
  transition: all .3s ease;
}

.cns__filtered i { width: 7px }
.cns__filtered i svg path { stroke: var(--black) }

.cns__drawer-main {
  flex: auto;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.cns__drawer-main::-webkit-scrollbar {
  display: none;
}

.cns__close-btn {
  width: 16px;
  height: 16px;
}

.cns__accordion .title-icon .icon,
.cns__accordion .title-icon.active .icon {
  width: 10px;
}

.cns__accordion.always-open .icon-wrapper {
  display: none;
}

.cns__price-slider {
  padding-left: 5px;
  padding-right: 5px;
}
.cns__price-slider .noUi-touch-area {
  cursor: grab;
}
.cns__price-slider .noUi-handle:before,
.cns__price-slider .noUi-handle:after {
  display: none;
}
.cns__price-slider .noUi-handle {
  width: 10px;
  height: 10px;
  border-radius: 100%;
  border: none;
  background: var(--black);
  box-shadow: initial;
  top: -4px;
}
.cns__price-slider .noUi-handle.noUi-handle-lower {
  right: -6px;
}
.cns__price-slider .noUi-handle.noUi-handle-upper {
  right: -5px;
}
.cns__price-slider.noUi-horizontal {
  height: 3px;
}
.cns__price-slider.noUi-target {
  border-radius: initial;
  border: none;
  box-shadow: initial;
  background: var(--border);
}
.cns__price-slider .noUi-connect {
  background: var(--black);
}

.cns__products {
  width: calc(100% + 40px);
  margin-left: -20px;
}

.cns__products > div.flex:not(:first-child) {
  margin-top: 20px;
}

@media only screen and (min-width: 600px) {
    .cns__products {
      width: calc(100% + 48px);
      margin-left: -24px;
    }
}

@media only screen and (min-width: 1024px) {
  .cns__topbar button {
    padding: 6px 20px;
    border-radius: 4px;
    border: 1px solid;
    column-gap: 6px;
    border-color: var(--border);
  }
  
  .cns__topbar button:has(i) {
    background: var(--black);
    border-color: var(--black);
    color: var(--white);
  }
  
  .cns__topbar button:has(i) svg path {
    stroke: var(--white);
  }

  .cns__drawer-inner {
    top: 32px;
    left: 0;
    bottom: unset;
    transform: translateX(-100%);
    height: calc(100vh - 64px);
    width: 400px;
    border-radius: 8px;
  }

  .cns__drawer[data-show="true"] .cns__drawer-inner {
    transform: translateX(calc(0% + 32px));
  }

  .cns__products {
    width: 100%;
    margin-left: 0;
  }

  .cns__products > div.flex:not(:first-child) {
    margin-top: 24px;
  }

}




body.hide-bar-on-scroll.hide-bar .top-bar.sticky-below-header {
  top: 48px !important;
}

.search-top-section form {
  width: 100%;
}
.search-top-section .search-icon {
  top: 50%;
  transform: translateY(-50%);
}

.bipl-banner .link-icon svg path {
  stroke: #ffffff;
}

@media only screen and (min-width: 600px) {
  
  .bipl-banner .image-con .global-image-wrapper {
    height: 100%;
  }

  .bipl-banner .image-con img {
    width: auto;
    height: 100%;
    object-fit: cover;
    object-position: right;
  }

} /* end of @media only screen and (min-width: 600px) */

@media only screen and (min-width: 1024px) {
  
  /* .product-list .row {
    margin-left: -8px;
    margin-right: -8px;
  }

  .product-list .product-card-wrapper,
  .product-list .banner-wrapper {
    padding-left: 8px;
    padding-right: 8px;
  } */

} /* end of @media only screen and (min-width: 1024px) */
