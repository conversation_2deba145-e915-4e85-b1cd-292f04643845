.product-top-section .right-side-inner {
  border-radius: 16px 16px 0 0;
  background-color: var(--white);
}


.product-form .pdp-atc-wrapper {
  flex: 1;
}
.product-form .global-minus.disabled svg path,
.product-form .global-plus.disabled svg path {
  stroke: var(--black);
}

.product-form .wishlist-btn {
  width: 40px;
  padding: 0;
}

.product-form .wishlist-btn::before {
  content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none"><path d="M16.377 5.39099C14.8671 3.86239 12.4289 3.86239 10.919 5.39099L10.0094 6.31003L9.09972 5.39099C7.58049 3.85301 5.13285 3.85301 3.63239 5.38161C2.12254 6.91021 2.12254 9.38599 3.63239 10.9146L10 17.3666L16.3676 10.9146C17.8775 9.38599 17.8775 6.91021 16.3676 5.38161L16.377 5.39099Z" stroke="%23652527" stroke-linecap="round" stroke-linejoin="round"/></svg>');
  display: inline-block;
  width: 20px;
  height: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.product-form .wishlist-btn:hover::before {
  content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none"><path d="M16.377 5.39099C14.8671 3.86239 12.4289 3.86239 10.919 5.39099L10.0094 6.31003L9.09972 5.39099C7.58049 3.85301 5.13285 3.85301 3.63239 5.38161C2.12254 6.91021 2.12254 9.38599 3.63239 10.9146L10 17.3666L16.3676 10.9146C17.8775 9.38599 17.8775 6.91021 16.3676 5.38161L16.377 5.39099Z" stroke="%23FFFFFF" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

.product-form .wishlist-btn .text {
  display: none;
}

.product-form .grid-cols {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(61px, 1fr));
  gap: 6px;
}


.product-form .pdpr-options .radio-container.general-option {
  padding: 0;
}
.product-form .pdpr-options .radio-container.general-option.oos:after {
  content: "";
  display: block;
  width: 99%;
  height: 97%;
  top: 0px;
  left: 0;
  z-index: 1;
  border-radius: 4px;
  transform: rotate(1.2deg);
  position: absolute;
  background: linear-gradient(to bottom right, transparent calc(50% - .5px), var(--border) calc(50% - .5px), var(--border) calc(50% + .5px), transparent calc(50% + .5px));
}
.product-form .pdpr-options .radio-container.oos .text {
  color: var(--grey);
}

.product-form .pdpr-options .radio-container.general-option .text {
  height: 38px;
  border: 1px solid var(--border);
  display: flex;
  justify-content: center;
  align-items: center;
}

.product-form
  .pdpr-options
  .radio-container.general-option
  input:checked
  ~ .text {
  border: 1px solid var(--black);
}
.sticky-atc {
  position: fixed;
  bottom: -90px;
  right: 0;
  width: 100%;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.10);
  transition: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.sticky-atc.active {
  bottom: -1px;
}

.sticky-atc .image-con {
  width: 56px;
  height: auto;
}


.sticky-atc .wishlist-btn::before {
  content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none"><path d="M16.377 5.39099C14.8671 3.86239 12.4289 3.86239 10.919 5.39099L10.0094 6.31003L9.09972 5.39099C7.58049 3.85301 5.13285 3.85301 3.63239 5.38161C2.12254 6.91021 2.12254 9.38599 3.63239 10.9146L10 17.3666L16.3676 10.9146C17.8775 9.38599 17.8775 6.91021 16.3676 5.38161L16.377 5.39099Z" stroke="%23652527" stroke-linecap="round" stroke-linejoin="round"/></svg>');
  display: inline-block;
  width: 20px;
  height: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.sticky-atc .wishlist-btn:hover::before {
  content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none"><path d="M16.377 5.39099C14.8671 3.86239 12.4289 3.86239 10.919 5.39099L10.0094 6.31003L9.09972 5.39099C7.58049 3.85301 5.13285 3.85301 3.63239 5.38161C2.12254 6.91021 2.12254 9.38599 3.63239 10.9146L10 17.3666L16.3676 10.9146C17.8775 9.38599 17.8775 6.91021 16.3676 5.38161L16.377 5.39099Z" stroke="%23FFFFFF" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

.sticky-atc .wishlist-btn .text {
  display: none;
}

.info-blocks .icon {
  width: 20px;
  height: 20px;
}

.loyalty-icon {
  flex-shrink: 0;
  width: 30px;
  height: 30px;
}
.pt-5 {
  padding-top: 0.3125rem;
}
.pb-5 {
  padding-bottom: 0.3125rem;
}
.mt-6 {
  margin-top: 0.375rem;
}
.mb-2 {
  margin-bottom: 2px;
}
.ml-6 {
  margin-left: 0.375rem;
}
.pl-6 {
  padding-left: 0.375rem;
}

.pr-6 {
  padding-right: 0.375rem;
}
.pt-14 {
  padding-top: 0.875rem;
}
.pb-14 {
  padding-bottom: 0.875rem;
}

.product-variety-opt .color-option {
	transition: opacity 0.3s ease;
}
.global-select-div.pdp-variety-select .inner {
  justify-content: flex-start;
}
.global-select-div.pdp-variety-select .option-text-cont {
  pointer-events: none;
}
.global-select-div.pdp-variety-select .color-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}
.global-form-qty {
  height: unset;
}
.global-form-qty button svg {
  width: 10px;
  height: 10px;
}
.global-select-div.pdp-variety-select .options li {
  padding: 12px 16px;
}
.global-form-qty button.disabled {
  background-color: var(--white);
}
.global-form-qty button.global-minus {
  padding-right: 16px;
}
.global-form-qty button.global-plus {
  padding-left: 16px;
}
.global-form-qty .global-qty-text {
  height: unset;
}
.global-form-qty button.global-minus, .global-form-qty button.global-plus {
  padding: 6px 6px;
}


.sticky-atc .btn2 {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

@media only screen and (min-width: 600px) {
  

}

@media only screen and (min-width: 1024px) {
  .product-top-section .right-side-inner {
    background-color: transparent;
    width: 100%;
    margin-left: 0;
  }
  .product-top-section .left-side-content {
    position: sticky;
    top: 83px;
  }
  .product-top-section .right-side {
    position: sticky;
    top: 83px;
  }
  .product-form .grid-cols {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(50.5px, 1fr));
    gap: 8px;
  }

  /* .glance-info {
    grid-template-columns: repeat(3, 1fr);
  }
  .glance-info .each-glance {
    height: 96px;
  } */
  .d-bg-off-white {
    background-color: var(--off-white);
  }
  .product-form .pdp-atc-wrapper,
  .product-form .wishlist-btn {
    width: 50%;
  }

  .product-form .wishlist-btn {
    padding-left: 20px;
  }

  .product-form .wishlist-btn::before {
    content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 20 21" fill="none"><path d="M16.377 5.39099C14.8671 3.86239 12.4289 3.86239 10.919 5.39099L10.0094 6.31003L9.09972 5.39099C7.58049 3.85301 5.13285 3.85301 3.63239 5.38161C2.12254 6.91021 2.12254 9.38599 3.63239 10.9146L10 17.3666L16.3676 10.9146C17.8775 9.38599 17.8775 6.91021 16.3676 5.38161L16.377 5.39099Z" stroke="%23652527" stroke-linecap="round" stroke-linejoin="round"/></svg>');
    display: inline-block;
    width: 13px;
    height: 13px;
    left: auto;
    transform: translate(-20px, -50%);
  }

  .product-form .wishlist-btn:hover::before {
    content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 20 21" fill="none"><path d="M16.377 5.39099C14.8671 3.86239 12.4289 3.86239 10.919 5.39099L10.0094 6.31003L9.09972 5.39099C7.58049 3.85301 5.13285 3.85301 3.63239 5.38161C2.12254 6.91021 2.12254 9.38599 3.63239 10.9146L10 17.3666L16.3676 10.9146C17.8775 9.38599 17.8775 6.91021 16.3676 5.38161L16.377 5.39099Z" stroke="%23FFFFFF" stroke-linecap="round" stroke-linejoin="round"/></svg>');
  }

  .product-form .wishlist-btn .text {
    display: inline-block;
  }
  .sticky-atc {
    bottom: 40px;
    right: -420px;
    border-radius: 8px;
    width: 300px;
    background-color: var(--white);
}

.sticky-atc.active {
    bottom: 40px;
    right: 40px;
}

.sticky-atc .btn2 {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}


/* .sticky-atc .wishlist-btn {
    display: none;
} */

}

/* @media only screen and (min-width: 1200px) {
  .product-top-section .left-side-content, .product-top-section .left-side-content {
    top: 83px;
  }
} */

@media only screen and (min-width: 1440px) {
  /* .product-top-section .cont-wrapper {
    width: 1105px;
    margin: 0 auto;
  } */
}

@media only screen and (min-width: 1920px) {
}