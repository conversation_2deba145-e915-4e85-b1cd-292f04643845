/* 
.product-images-popup .popup-content .inner {
    width: calc(100vw - 64px);
}

.product-images-popup .popup-content .inner-wrapper {
    display: grid;
    place-content: center;
}

.product-images-popup .popup-content {
    background-color: var(--white);
}

.product-images-popup .popup-content .close svg path {
    stroke: var(--brown);
}



.product-images-main .thumbs-wrapper .swiper-thumbs {
    position: relative;
    flex-shrink: 0;
    margin-left: 1.6px;
}

.product-images-main .swiper-thumbs .swiper-slide {
    cursor: pointer;
    width: 54px;
    height: 54px;
    border-radius: 6px;
}

.product-images-main .swiper-thumbs .swiper-slide .image-box-square {
    width: 54px;
    height: 54px;
    position: relative;
    border-radius: 6px;
}

.product-images-main .swiper-thumbs .swiper-slide .global-image-wrapper {
    height: 100%;
    border-radius: 5.6px;
}

.product-images-main .swiper-thumbs .swiper-slide .global-image-wrapper img {
    object-fit: cover;
    height: 100%;
    border-radius: 5.6px;
}

.product-images-main .thumbs-wrapper .thumbs-swiper-button-next,
.product-images-main .thumbs-wrapper .thumbs-swiper-button-prev {
    cursor: pointer;
}

.product-images-main .thumbs-wrapper .swiper-slide.swiper-slide-thumb-active {
    outline: 1.6px solid var(--brown-80);
}

.product-images-popup .product-images-popup-swiper-button-next,
.product-images-popup .product-images-popup-swiper-button-prev,
.product-images-main .product-images-main-swiper-button-next,
.product-images-main .product-images-main-swiper-button-prev {
    width: 36px;
    height: 36px;
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
}
.product-images-main .product-images-main-swiper-button-next {
    right: 0;
}
.product-images-main .product-images-main-swiper-button-prev {
    left: 0;
}

.product-images-popup .product-images-popup-swiper-button-next svg path,
.product-images-popup .product-images-popup-swiper-button-prev svg path {
    stroke: var(--brown);
}

.product-images-popup .product-images-popup-swiper-button-next.swiper-button-next::after,
.product-images-popup .product-images-popup-swiper-button-prev.swiper-button-prev::after,
.product-images-main .product-images-main-swiper-button-next.swiper-button-next::after,
.product-images-main .product-images-main-swiper-button-prev.swiper-button-prev::after {
    display: none;
} */

/* Base styles for thumbnail swipers */
/* .product-images-main .swiper-thumbs,
.product-images-popup .swiper-thumbs {
    position: relative;
    flex-shrink: 0;
    margin-left: 1.6px;
}

.product-images-main .swiper-thumbs .swiper-slide,
.product-images-popup .swiper-thumbs .swiper-slide {
    cursor: pointer;
    width: 54px;
    height: 54px;
    border-radius: 6px;
}

.product-images-main .swiper-thumbs .swiper-slide .image-box-square,
.product-images-popup .swiper-thumbs .swiper-slide .image-box-square {
    width: 54px;
    height: 54px;
    position: relative;
    border-radius: 6px;
}

.product-images-main .swiper-thumbs .swiper-slide .global-image-wrapper,
.product-images-popup .swiper-thumbs .swiper-slide .global-image-wrapper {
    height: 100%;
    border-radius: 5.6px;
}

.product-images-main .swiper-thumbs .swiper-slide .global-image-wrapper img,
.product-images-popup .swiper-thumbs .swiper-slide .global-image-wrapper img {
    object-fit: cover;
    height: 100%;
    border-radius: 5.6px;
}

.product-images-main .thumbs-wrapper .swiper-slide.swiper-slide-thumb-active,
.product-images-popup .swiper-thumbs .swiper-slide.swiper-slide-thumb-active {
    outline: 1.6px solid var(--brown);
} */


.product-images-main .swiper-pagination-bullet-active,
.product-images-main .swiper-pagination-bullet {
    background-color: var(--black);
}

/* Media Queries in ascending order */
@media only screen and (min-width: 600px) {
    .product-images-popup .popup-content .inner {
        width: 100vw;
    }
}

@media only screen and (min-width: 1024px) {
    .product-images-main .swiper-wrapper {
        display: grid;
        gap: 8px;
        grid-template-columns: repeat(2, 1fr);
    }

    .product-images-main .thumbs-wrapper {
        position: absolute;
        top: calc(50% - 154px);
        left: 23px;
        height: 308px;
        width: 58px;
        overflow: hidden;
        padding-top: 30px;
        padding-bottom: 30px;
    }
    .product-images-main .swiper-thumbs,
    .product-images-popup .swiper-thumbs {
        height: 100%;
        margin-left: 0;
    }
    .product-images-main .swiper-thumbs .swiper-wrapper,
    .product-images-popup .swiper-thumbs .swiper-wrapper {
        width: 54px;
    }
    .product-images-popup .swiper-thumbs .swiper-wrapper.d-jc-normal {
        justify-content: normal;
        width: 100%;
    }
    .product-images-main .swiper-thumbs .swiper-wrapper .swiper-slide,
    .product-images-popup .swiper-thumbs .swiper-slide {
        width: 54px;
        height: 54px;
    }

    .product-images-main .swiper-thumbs .swiper-wrapper .swiper-slide .image-box-square,
    .product-images-popup .swiper-thumbs .swiper-wrapper .swiper-slide .image-box-square {
        width: 54px;
        height: 54px;
    }

    .product-images-main .swiper-slide.cursor-zoom-in {
        cursor: zoom-in;
    }

    .product-images-popup .popup-content .inner {
        width: 600px;
    }

    .product-images-popup .swiper-button-disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: all;
    }
    
    .product-images-popup .product-images-popup-swiper-button-next svg path,
    .product-images-popup .product-images-popup-swiper-button-prev svg path {
        stroke: var(--brown);
    }
    
    .product-images-popup .product-images-popup-swiper-button-next {
        right: 100px; 
    }
    
    .product-images-popup .product-images-popup-swiper-button-prev {
        left: 100px;
    }
    .product-images-popup .popup-content .inner-wrapper {
        place-items: center;
        grid-template-rows: 85vh 15vh;
    }
}
@media only screen and (min-width: 1440px) {
    .product-images-main .thumbs-wrapper {
        position: absolute;
        top: calc(50% - 220px);
        left: 23px;
        height: 440px;
        width: 64px;
        overflow: hidden;
        padding-top: 40px;
        padding-bottom: 40px;
    }
    .product-images-main .swiper-thumbs,
    .product-images-popup .swiper-thumbs {
        height: 100%;
        margin-left: 0;
    }
    .product-images-main .swiper-thumbs .swiper-wrapper,
    .product-images-popup .swiper-thumbs .swiper-wrapper {
        width: 60px;
    }
    .product-images-main .swiper-thumbs .swiper-wrapper .swiper-slide,
    .product-images-popup .swiper-thumbs .swiper-slide {
        width: 60px;
        height: 60px;
    }
    .product-images-main .swiper-thumbs .swiper-wrapper .swiper-slide .image-box-square,
    .product-images-popup .swiper-thumbs .swiper-wrapper .swiper-slide .image-box-square {
        width: 60px;
        height: 60px;
    }
}
@media only screen and (min-width: 1920px) {
    .product-images-main .thumbs-wrapper {
        position: absolute;
        top: calc(50% - 246px);
        left: 23px;
        height: 492px;
        width: 64px;
        overflow: hidden;
        padding-top: 30px;
        padding-bottom: 30px;
    }
}