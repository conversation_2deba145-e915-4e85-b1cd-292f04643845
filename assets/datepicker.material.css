.datepicker {
  display: inline-block;
  width: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.datepicker table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  border: 0;
}

.datepicker table th,
.datepicker table td {
  width: calc(100% / 7);
  padding: 0;
}

.datepicker table th {
  color: var(--dark-blue);
  /* text-transform: uppercase; */
  font-size: 12px;
  line-height: 4;
  font-weight: 500;
  text-align: center;
}

.datepicker__wrapper {
  color: #333333;
  border-radius: 0.125rem;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  padding: 15px;
  position: relative;
  z-index: 1;
  background: white;
  /* border: 1px solid #2196F3; */
  /* width: 16rem; */
  min-width: 300px;
}

.datepicker__wrapper::after {
  content: '';
  display: table;
  clear: both;
}

.datepicker:not(.is-inline) .datepicker__wrapper {
  /* box-shadow: 0 1px 3px rgba(0,0,0,0.1), 0 1px 2px rgba(0,0,0,0.2); */
  box-shadow: 0px 3px 16px #0000001A;
  margin: 0.25rem 0;
  width: 100%;
  top: -5px !important;
  bottom: unset !important;
}

.datepicker__header {
  position: relative;
  text-align: center;
  padding: 0.25rem;
  margin: -0.5rem -0.5rem 0;
}

.datepicker__title {
  display: inline-block;
  padding: 0.25rem;
  font-size: 14px;
  line-height: 1.5rem;
  font-weight: 500;
  color: var(--dark-blue);
}

.datepicker__prev,
.datepicker__next {
  display: block;
  cursor: pointer;
  position: relative;
  outline: none;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: no-repeat center / 60%;
  font-size: 0;
}

/* .datepicker__prev:hover,
  .datepicker__next:hover {
    background-color: #1E88E5;
  } */

.datepicker__prev {
  float: left;
  position: relative;
  /* background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18"><path d="M15 8.25H5.87l4.19-4.19L9 3 3 9l6 6 1.06-1.06-4.19-4.19H15v-1.5z" fill="white"/></svg>'); */
}

.datepicker__prev::before {
  content: "";
  display: block;
  width: 8px;
  height: 8px;
  border-left: 1px solid;
  border-top: 1px solid;
  transform: rotate(-45deg) translate(0, -50%);
  position: absolute;
  top: 50%;
  left: 17px;
}

.datepicker__next {
  float: right;
  position: relative;
  /* background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18"><path d="M9 3L7.94 4.06l4.19 4.19H3v1.5h9.13l-4.19 4.19L9 15l6-6z" fill="white"/></svg>'); */
}

.datepicker__next::before {
  content: "";
  display: block;
  width: 8px;
  height: 8px;
  border-top: 1px solid;
  border-right: 1px solid;
  transform: rotate(45deg) translate(0, -50%);
  position: absolute;
  top: 50%;
  right: 17px;
}

.datepicker__prev.is-disabled,
.datepicker__next.is-disabled {
  pointer-events: none;
  cursor: default;
  opacity: 0.4;
}

.datepicker__time {
  padding: 0.5rem 1rem;
  margin: 0 -0.5rem;
  font-size: 0.75rem;
  text-align: right;
  background: #E3F2FD;
  border-bottom: 1px solid #BBDEFB;
}

.datepicker__label {
  margin-right: 1rem;
  color: rgba(0, 0, 0, 0.4);
  float: left;
}

.datepicker__field {
  display: inline-block;
  margin: 0 0.125rem;
  color: #333;
  width: 2ch;
}

.datepicker__field span {
  display: block;
  width: 100%;
}

.datepicker__day {
  color: var(--dark-blue);
  border: 1px solid var(--light-grey);
}

.datepicker__day div {
  cursor: pointer;
  display: block;
  box-sizing: border-box;
  border: 0;
  margin: 0;
  background: transparent;
  position: relative;
  /* border-radius: 50%; */
}

.datepicker__day div::after {
  content: '';
  display: block;
  border-radius: 50%;
  padding-top: 100%;
  position: relative;
  background: inherit;
  z-index: 1;
}

.datepicker__day div:hover,
.datepicker__day.is-highlighted div {
  background: var(--brown-20);
}

.datepicker__day.is-today {
  /* color: #2196F3; */
  position: relative;
}

.datepicker__day.is-today::before {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-top: 10px solid var(--stroke);
  border-left: 10px solid transparent;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 9;
}

/* .datepicker__day.is-today div::after {
    box-shadow: inset 0 0 0 1px currentColor;
  } */

.datepicker__day.is-today.is-disabled.is-selected div::after,
.datepicker__day.is-today.is-otherMonth.is-selected div::after {
  box-shadow: none;
}

.datepicker__day.is-selected div {
  background: var(--brown-20);
}

/* .datepicker__day.is-selected:hover div::after {
    background: #1E88E5;
  } */

.datepicker__day.is-selected .datepicker__daynum {
  font-weight: 500;
  color: var(--dark-blue);
}

.datepicker__day.is-selected+.is-selected div::before,
.datepicker__day.is-highlighted+.is-highlighted div::before {
  content: '';
  position: absolute;
  top: 0;
  left: -50%;
  width: 100%;
  height: 100%;
  background: inherit;
  z-index: 0;
}

.datepicker__day.is-disabled,
.datepicker__day.is-otherMonth {
  cursor: default;
  pointer-events: none;
  color: var(--dark-blue);
  background: var(--light-grey);
}

.datepicker__day.is-disabled.is-selected .datepicker__daynum,
.datepicker__day.is-otherMonth.is-selected .datepicker__daynum {
  color: rgba(0, 0, 0, 0.2);
}

.datepicker__day.is-disabled.is-selected div,
.datepicker__day.is-otherMonth.is-selected div,
.datepicker__day.is-disabled.is-selected+.is-selected div::before,
.datepicker__day.is-otherMonth.is-selected+.is-selected div::before {
  background: #E3F2FD;
}

.datepicker__daynum {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  font-size: 0.75rem;
  line-height: 1rem;
  margin-top: -0.5rem;
  text-align: center;
  z-index: 2;
}