{%- layout none -%}
{%- paginate blog.articles by settings.posts_per_page -%}
    {%- for post in blog.articles -%}
        {% comment %} {%- if post.id != settings.featured_article.id -%} {% endcomment %}
            {% render 'article-card',
                article: post
            %}
        {% comment %} {%- endif -%} {% endcomment %}
    {%- endfor -%}

    {%- if paginate.pages > 1 -%}
        <div class="blog-page">
            {% render 'global-pagination', paginate: paginate %}
        </div>
    {%- endif -%}
{%- endpaginate -%}