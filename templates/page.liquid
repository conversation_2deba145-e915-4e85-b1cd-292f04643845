<style>
  .page-default .shopify-page-title {
    padding-top: 48px;
    padding-bottom: 48px;
  }
  .page-default .shopify-page-body {
    padding-top: 48px;
  }
  @media only screen and (min-width:1024px) {
    .page-default .shopify-page-title {
      padding-top: 70px;
      padding-bottom: 70px;
    }
    .page-default .shopify-page-body {
      padding-top: 70px;
    }
  }
</style>
<section class="page-default global-content-top-margin">
  <div class="pb-60 d-pb-100 b-1 b-stroke">
    <div class="shopify-page-title">
      <h1 class="h1 tc">{{ page.title }}</h1>
    </div>
    <div class="shopify-page-body bt-1 b-stroke">
      <div class="container">
        <div class="row">
          <div class="rte p2 col-12 col-d-10 col-hd-8 push-d-1 push-hd-2">
            {{ page.content }}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
