<style>
  	#activate-account-page {
		height: 100vh;
		display: flex;
		align-items: center;
	}
</style>

<section class="global-content-top-margin">
	<div id="activate-account-page" class="sp-md">
		<div class="container w-full">
			<div class="row">
				<div class="col-12 col-t-8 col-d-4 push-t-2 push-d-4">
					<div class="activate-container">
						{%- form 'activate_customer_password', id: 'activate-account-form' -%}
							<div class="tc activate-account-title">
								<h1 class="h4 mb-8">{{ 'customer.activate_account.title' | t }}</h1>
								<p class="p2">{{ 'customer.activate_account.subtext' | t }}</p>
							</div>

							<div class="mt-16 d-mt-20">
								{%- if form.errors -%}
								<div class="form-errors global-error-msg mb-12 d-mb-16 rte p2">
									{{ form.errors | default_errors }}
								</div>
								{%- endif -%}

								<div class="mb-8 d-mb-12">
									<div class="password-field global-input relative">
										<input type="password" v-model="password" name="customer[password]" id="password" class="p2"
										placeholder="{{ 'customer.activate_account.password' | t }}" :class="{'global-input-error' : password_error}" />
										<a class="absolute icon-password">
										<span class="icon-show">
											{{ settings.icon_show_password }}
										</span>
										<span class="icon-hide hidden">
											{{ settings.icon_hide_password }}
										</span>
										</a>
									</div>
									<div class="global-error-msg p2 mt-8" :class="{'hide-m': !password_error}">{{ 'customer.activate_account.error_password' | t }}</div>
								</div>
								<div class="mb-12 d-mb-20">
									<div class="password-field global-input relative">
										<input type="password" v-model="password_confirm" name="customer[password_confirmation]" id="password_confirmation" class="p2"
										placeholder="{{ 'customer.activate_account.password_confirm' | t }}" :class="{'global-input-error' : password_confirm_error}" />
										<a class="absolute icon-password">
										<span class="icon-show">
											{{ settings.icon_show_password }}
										</span>
										<span class="icon-hide hidden">
											{{ settings.icon_hide_password }}
										</span>
										</a>
									</div>
									<div class="global-error-msg p2 mt-8" :class="{'hide-m': !password_confirm_error}">{{ 'customer.activate_account.error_password_confirm' | t }}</div>
								</div>
								<button type="submit" class="btn2 w-full">{{ 'customer.activate_account.submit' | t }}</button>
							</div>
						{%- endform -%}
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<script>
	Vue.createApp({
		delimiters: ['${', '}'],
		data() {
			return {
				password: null,
				password_confirm: null,
				has_error: false,
				password_error: false,
				password_confirm_error: false,
			}
		},
		mounted() {
			const thisObj = this;
			document.getElementById('activate-account-form').addEventListener('submit', function(e) {
				e.preventDefault();

				thisObj.has_error = false;
				thisObj.password_error = false;
				thisObj.password_confirm_error = false;

				if(!thisObj.password) {
					thisObj.password_error = true;
					thisObj.has_error = true;
				}

				if(!thisObj.password_confirm) {
					thisObj.password_confirm_error = true;
					thisObj.has_error = true;
				}

				if(!thisObj.has_error) {
				this.submit();
				}
			});
		}
	}).mount('#activate-account-page');
</script>