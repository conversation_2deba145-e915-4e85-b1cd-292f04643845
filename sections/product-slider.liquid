{% liquid
    assign section_name = 'Product Slider'
    assign el_id = section_name | handle | append: '-' | append: section.id
    
    assign section_padding = section.settings.padding
    assign padding_class = 'sp-' | append: section_padding

    if section.settings.no_padding_top
        assign padding_class = padding_class | append: ' spt-no'
    else
        if section.settings.top_half_padding
            assign padding_class = padding_class | append: ' spt-' | append: section_padding | append: '-half'
        endif
    endif

    if section.settings.no_padding_bottom
        assign padding_class = padding_class | append: ' spb-no'
    else
        if section.settings.bottom_half_padding
            assign padding_class = padding_class | append: ' spb-' | append: section_padding | append: '-half'
        endif
    endif
%}
{% if request.visual_preview_mode %}
    <img src="{{ "section-preview-product-slider.png" | asset_url }}" width="100%" height="100%" />
{% else %}
    {%- assign has_content = false -%}
    {%- if section.settings.product_source == 'collection' -%}
        {%- if section.settings.collection.products.size > 0 -%}
            {%- assign has_content = true -%}
        {%- endif -%}
    {%- else -%}
        {%- if section.settings.product_list != blank -%}
            {%- assign has_content = true -%}
        {%- endif -%}
    {%- endif -%}
    {%- if has_content == true -%}
        {% if section.settings.padding_system == "manual" %}
        {% assign padding_class = "sp" %}
        <style>
        #{{ el_id }} .sp {
            padding-top: {{ section.settings.padding_top }}px;
            padding-bottom: {{ section.settings.padding_bottom }}px;
        }
        @media only screen and (min-width: 1024px) {
            #{{ el_id }} .sp {
                padding-top: {{ section.settings.padding_top_d }}px;
                padding-bottom: {{ section.settings.padding_bottom_d }}px;
            }
        }
        </style>
        {% endif %}
    <style>
    #{{ el_id }} .product-slider-wrp {
        margin-left: var(--container_padding_min);
        width: calc(100% + var(--container_padding) + var(--container_padding));
    }
    #{{ el_id }} .swiper-slide {
        height: unset;
    }
    #{{ el_id }} .prev-btn,
    #{{ el_id }} .next-btn {
        display: none;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 48px;
        height: 48px;
        background: var(--white);
        border: 1px solid var(--border);
        opacity: .7;
        backdrop-filter: blur(5px);
    }
    #{{ el_id }} .prev-btn:hover,
    #{{ el_id }} .next-btn:hover {
        background: var(--white);
    }
    #{{ el_id }} .prev-btn.swiper-button-disabled,
    #{{ el_id }} .next-btn.swiper-button-disabled {
        cursor: not-allowed;
        opacity: 0;
    }
    #{{ el_id }} .prev-btn svg path,
    #{{ el_id }} .next-btn svg path {
        stroke: var(--black);
    }
    #{{ el_id }} .prev-btn:hover svg path,
    #{{ el_id }} .next-btn:hover svg path {
        stroke: var(--black);
    }
    #{{ el_id }} .prev-btn {
        left: -24px;
    }
    #{{ el_id }} .next-btn {
        right: -24px;
    }
    @media screen and (min-width: 600px){
        #{{ el_id }} .product-slider-wrp {
            margin-left: var(--container_padding_min_t);
            width: calc(100% + var(--container_padding_t) + var(--container_padding_t));
        }
        #{{ el_id }} .swiper-slide {
            height: unset;
        }
    }
    @media screen and (min-width: 1024px){
        #{{ el_id }} .prev-btn,
        #{{ el_id }} .next-btn {
            display: flex;
        }
        /* #{{ el_id }} .product-slider-wrp {
            margin-left: var(--container_padding_min_d);
            width: calc(100% + var(--container_padding_d) + var(--container_padding_d));
        } */
        #{{ el_id }} .product-slider-wrp {
            margin-left: 0;
            width: 100%;
        }        
        #{{ el_id }} .swiper-slide {
            height: unset;
        }
    }
    </style>
    {% render "separator",
        show: section.settings.border_top,
        full_width: section.settings.border_top_full,
        wrapper_classes: padding_bt
    %}
    <div id="{{ el_id }}" class="relative" {% if section.settings.bg_color != blank %}style="background-color: {{ section.settings.bg_color }};"{% endif %}>
        <div class="container">
            <div class="relative {{ padding_class }}">
                <div class="relative header-wrp flex flex-column d-flex-row jc-start d-jc-between ai-start d-ai-end rowgap-8 d-rowgap-0">
                    {%- if section.settings.title != blank -%}
                        <{{ section.settings.title_text_element }} class="{{ section.settings.title_text_class }} tc">{{ section.settings.title }}</{{ section.settings.title_text_element }}>
                    {%- endif -%}
                    {%- if section.settings.btn_label != blank and section.settings.btn_url != blank -%}
                        <div class="relative">
                            <a href="{{ section.settings.btn_url }}" alt="{{ section.settings.btn_label }}" title="{{ section.settings.btn_label | escape }}" class="link3">{{ section.settings.btn_label }}</a>
                        </div>
                    {%- endif -%}
                </div>
                <div class="product-slider-wrp relative pt-24 d-pt-32">
                    {% liquid
                    if section.settings.product_source == 'collection'
                        assign products = collections[section.settings.collection].products
                    elsif section.settings.product_source == 'product_list'
                        assign products = section.settings.product_list
                    endif
                    %}
                    {%- render 'product-slider',
                        products: products,
                        limit: section.settings.limit,
                        load_inside_vue: true
                    -%}
                </div>
                <button class="absolute jc-center ai-center prev-btn rounded-4 zi-1">
                    {{ settings.icon_slide_left }}
                </button>
                <button class="absolute jc-center ai-center next-btn rounded-4 zi-1">
                    {{ settings.icon_slide_right }}
                </button>
            </div>
        </div>
    </div>
    {% render "separator",
        show: section.settings.border_bottom,
        full_width: section.settings.border_bottom_full,
        wrapper_classes: padding_bt
    %}
    <script>
    {%- assign vue_el = el_id | replace: '-', '_' | prepend: 'V'  -%}
    var {{ vue_el }} = Vue.createApp({
        delimiters: ['${', '}'],
        data() {
            return {
                loading: false,
                screen: {
                    hhd: window.innerWidth > 1919,
                    hd: window.innerWidth > 1439 && window.innerWidth < 1920,
                    d: window.innerWidth > 1023 && window.innerWidth < 1440,
                    t: window.innerWidth > 599 && window.innerWidth < 1024,
                    m: window.innerWidth < 600
                },
                swiperObj: null,
                swiperProductCards: {},
            }
        },
        mounted() {
            addEventListener('resize', () => {
                this.screen.hhd = window.innerWidth > 1919;
                this.screen.hd = window.innerWidth > 1439 && window.innerWidth < 1920;
                this.screen.d = window.innerWidth > 1023 && window.innerWidth < 1440;
                this.screen.t = window.innerWidth > 599 && window.innerWidth < 1024;
                this.screen.m = window.innerWidth < 600;
            });
            this.swiperInit();
        },
        methods: {
            swiperInit() {
                thisObj = this;
                if(!thisObj.swiperObj) {
                    thisObj.swiperObj = new Swiper(`#{{ el_id }} .product-slider-wrp .products`, {
                        slidesPerView: 1.5,
                        spaceBetween: 8,
                        allowTouchMove: true,
                        loop: false,
                        slidesOffsetBefore: 20,
                        slidesOffsetAfter: 20,
                        calculateHeight: true,
                        // speed: 300,
                        navigation: {
                            prevEl: `#{{ el_id }} .prev-btn`,
                            nextEl: `#{{ el_id }} .next-btn`,
                        },
                        breakpoints: {
                            600: {
                                slidesPerView: 2.5,
                                spaceBetween: 12,
                                slidesOffsetBefore: 20,
                                slidesOffsetAfter: 20,
                            },
                            1024: {
                                slidesPerView: 4,
                                spaceBetween: 16,
                                slidesOffsetBefore: 0,
                                slidesOffsetAfter: 0,
                            }
                        },
                        on: {
                            init: function(swiper) {
                                $360.lazyLoadInstance.update();
                                $360.swiperInCard('#{{ el_id }} .product-slider-wrp');
                                thisObj.updateHeight();
                            },
                            slideChange: function(swiper) {
                                $360.lazyLoadInstance.update();
                            },
                            resize: function(swiper) {
                                $360.lazyLoadInstance.update();
                                thisObj.updateHeight();
                            }
                        }
                    });
                }
            },
            updateHeight() {
                const itemList = document.querySelectorAll('#{{ el_id }} .swiper .swiper-wrapper .swiper-slide .product-card');
                itemList.forEach(item => {
                    item.style.height = ``;
                })
                const maxHeight = document.querySelector('#{{ el_id }} .swiper .swiper-wrapper').offsetHeight;            
                itemList.forEach(item => {
                    item.style.height = `${maxHeight}px`;
                })
            }
        }
    }).mount('#{{ el_id }}');
    window.addEventListener("load", {{ vue_el }}, false);
    document.addEventListener('shopify:section:load', function() {
        {{ vue_el }}.$forceUpdate();
    });
    </script>
    {%- endif -%}
{%- endif -%}

{% schema %}
{
    "name": "Product Slider",
    "settings": [
		{
			"type": "header",
			"content": "General Settings"
		},
		{
			"type": "color",
			"id": "bg_color",
			"label": "Background Color",
			"default": "#FFFFFF"
		},
		{
            "type": "text",
            "id": "title",
            "label": "Title"
        },
        {
            "type": "select",
            "id": "title_text_element",
            "label": "Title text element",
            "options": [
                {
                    "value": "h1",
                    "label": "h1"
                },
                {
                    "value": "h2",
                    "label": "h2"
                },
                {
                    "value": "h3",
                    "label": "h3"
                },
                {
                    "value": "h4",
                    "label": "h4"
                },
                {
                    "value": "h5",
                    "label": "h5"
                },
                {
                    "value": "h6",
                    "label": "h6"
                }
            ],
            "default": "h2"
        },
        {
            "type": "select",
            "id": "title_text_class",
            "label": "Title text size",
            "options": [
                {
                    "value": "h1",
                    "label": "h1"
                },
                {
                    "value": "h2",
                    "label": "h2"
                },
                {
                    "value": "h3",
                    "label": "h3"
                },
                {
                    "value": "h4",
                    "label": "h4"
                },
                {
                    "value": "h5",
                    "label": "h5"
                },
                {
                    "value": "h6",
                    "label": "h6"
                }
            ],
            "default": "h3"
        },
        {
            "type": "text",
            "id": "btn_label",
            "label": "Button label"
        },
        {
            "type": "url",
            "id": "btn_url",
            "label": "Button URL"
        },
        {
            "type": "select",
            "id": "product_source",
            "label": "Product source",
            "options": [
                {
                    "value": "collection",
                    "label": "Collection"
                },
                {
                    "value": "product_list",
                    "label": "Product List"
                }
            ],
            "default": "collection"
        },
        {
            "type": "collection",
            "id": "collection",
            "label": "Collection",
            "info": "If product source is collection"
        },
        {
            "type": "product_list",
            "id": "product_list",
            "label": "Product List",
            "info": "If product source is Product List",
            "limit": 12
        },
        {
            "type": "range",
            "id": "limit",
            "min": 4,
            "max": 20,
            "step": 1,
            "label": "Number of products",
            "default": 12
        },
        {
			"type": "header",
			"content": "Border Settings"
		},
        {
            "type": "checkbox",
            "id": "border_top",
            "label": "Border Top",
            "default": false
        },
        {
            "type": "checkbox",
            "id": "border_top_full",
            "label": "Full Width Border Top",
            "default": false
        },
        {
            "type": "checkbox",
            "id": "border_bottom",
            "label": "Border Bottom",
            "default": false
        },
        {
            "type": "checkbox",
            "id": "border_bottom_full",
            "label": "Full Width Border Bottom",
            "default": false
        },
        {
            "type": "header",
            "content": "Section Padding Settings"
        },
        {
            "type": "select",
            "id": "padding_system",
            "label": "Padding System",
            "options": [
                { "value": "default", "label": "Default" },
                { "value": "manual", "label": "Manual" },
            ],
            "default": "default"
        },
        {
            "type": "select",
            "id": "padding",
            "label": "Padding",
            "options": [
                { "value": "no", "label": "No" },
                { "value": "xs", "label": "Extra Small" },
                { "value": "sm", "label": "Small" },
                { "value": "md", "label": "Med" },
                { "value": "lg", "label": "Large" }
            ],
            "default": "no",
            "visible_if": "{{ section.settings.padding_system == 'default' }}"
        },
        {
            "type": "checkbox",
            "id": "top_half_padding",
            "label": "Top half padding",
            "default": false,
            "visible_if": "{{ section.settings.padding_system == 'default' }}"
        },
        {
            "type": "checkbox",
            "id": "no_padding_top",
            "label": "No padding top",
            "default": false,
            "visible_if": "{{ section.settings.padding_system == 'default' }}"
        },
        {
            "type": "checkbox",
            "id": "bottom_half_padding",
            "label": "Bottom half padding",
            "default": false,
            "visible_if": "{{ section.settings.padding_system == 'default' }}"
        },
        {
            "type": "checkbox",
            "id": "no_padding_bottom",
            "label": "No padding bottom",
            "default": false,
            "visible_if": "{{ section.settings.padding_system == 'default' }}"
        },
        {
            "type": "range",
            "id": "padding_top",
            "label": "Padding Top (Mobile & Tablet)",
            "min": 0,
            "max": 120,
            "step": 2,
            "default": 0,
            "visible_if": "{{ section.settings.padding_system == 'manual' }}"
        },
        {
            "type": "range",
            "id": "padding_bottom",
            "label": "Padding Bottom (Mobile & Tablet)",
            "min": 0,
            "max": 120,
            "step": 2,
            "default": 0,
            "visible_if": "{{ section.settings.padding_system == 'manual' }}"
        },
        {
            "type": "range",
            "id": "padding_top_d",
            "label": "Padding Top (Desktop)",
            "min": 0,
            "max": 120,
            "step": 2,
            "default": 0,
            "visible_if": "{{ section.settings.padding_system == 'manual' }}"
        },
        {
            "type": "range",
            "id": "padding_bottom_d",
            "label": "Padding Bottom (Desktop)",
            "min": 0,
            "max": 120,
            "step": 2,
            "default": 0,
            "visible_if": "{{ section.settings.padding_system == 'manual' }}"
        }
    ],
    "presets": [
        {
            "name": "Product Slider"
        }
    ]
}
{% endschema %}
