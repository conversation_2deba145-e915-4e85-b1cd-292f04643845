{%- liquid
    assign section_name = 'Order Detail'
    assign el_id = section_name | handle | append: '-' | append: section.id
-%}
{{ 'shopify_common.js' | shopify_asset_url | script_tag }}
{{ 'customer_area.js' | shopify_asset_url | script_tag }}
<style>
    .word-break {
        word-break: break-all;
    }

    #{{ el_id }} .label {
        width: 140px;
        min-width: 140px;
    }
    #{{ el_id }} .region-grid {
        display: grid;
        grid-template-columns: 1fr;
    }

    .form-edit-title svg,
    .form-add-title svg {
        width: 14px;
        height: 14px;
    }

    #{{ el_id }}_address-wrapper {
        width: calc(100% + 40px);
        margin-left: -20px;
        padding-left: 20px;
        padding-right: 20px;
    }
    @media only screen and (min-width:600px) {
        #{{ el_id }} .region-grid {
            grid-template-columns: 1fr 1fr;
        }
        .form-edit-title svg,
        .form-add-title svg {
            width: 16px;
            height: 16px;
        }
        #{{ el_id }}_address-wrapper {
            width: calc(100% + 76px);
            margin-left: -38px;
            padding-left: 38px;
            padding-right: 38px;
        }
        
        #{{ el_id }} .region-grid .fill-empty {
            grid-column: 1 / 3;
        }
    }
    @media only screen and (min-width:1024px) {
        #{{ el_id }} .address-detail-content {
            width: 185px;
        }
        
        #{{ el_id }}_address-wrapper {
            width: 100%;
            margin-left: 0;
            padding-left: 32px;
            padding-right: 32px;
        }
    }
</style>
<section id="{{ el_id }}" class="account-addresses pt-32 d-pt-80 d-pb-80">
    <div class="container">
        <div class="row flex flex-column d-flex-row">
            <div class="col-d-1"></div>
            <div class="col-12 col-d-2">
                {%- render 'account-links' -%}
            </div>
            {%- render 'account-menu-dropdown' -%}
            <div class="col-d-1"></div>
            <div class="col-12 col-d-7">
                <div id="{{ el_id }}_address-wrapper" class="address-wrapper bg-white rounded-tl-16 rounded-tr-16 d-rounded-16 pt-32 pb-32">
                    <div class="address-container">
                        <div class="address-wrapper" v-if="showList">
                            <h2 class="h3 mb-32 d-mb-60">{{ 'customer.account.address_book' | t }}</h2>
                            {%- if customer.addresses_count > 0 -%}
                                <div class="address-name pb-24 bb-1 b-brown-20">
                                    <div class="flex colgap-12 d-colgap-16 mb-12">
                                        <p class="label p2 bold-600">Name</p>
                                        <p class="p2">{{ customer.name }}</p>
                                    </div>
                                    <div class="flex colgap-12 d-colgap-16">
                                        <p class="label p2 bold-600">Email</p>
                                        <p class="p2 word-break">{{ customer.email }}</p>
                                    </div>
                                </div>
                                <div class="address-list pt-24 pb-24">
                                    {%- for address in customer.addresses -%}
                                        <div class="address-detail flex flex-column rowgap-12{% unless forloop.last %} mb-12{% endunless %}">
                                            <div class="flex colgap-12 d-colgap-16">
                                                <p class="label p2 bold-600">
                                                    Address {{ forloop.index }}
                                                    {% if address == customer.default_address %}({{ 'customer.addresses.default_address' | t }}){% endif %}
                                                </p>
                                                <div class="flex flex-column d-flex-row ai-start rowgap-12 colgap-16">
                                                    <p class="p2 address-detail-content">
                                                        {{ address.name }}
                                                        <br>
                                                        {%- if address.company != blank -%}
                                                            {{ address.company }}
                                                            <br>
                                                        {%- endif -%}
                                                        {%- if address.address1 != blank -%}
                                                            {{ address.address1 }}
                                                            <br>
                                                        {%- endif -%}
                                                        {%- if address.address2 != blank -%}
                                                            {{ address.address2 }}
                                                            <br>
                                                        {%- endif -%}
                                                        {{ address.city }}{% if address.province != blank %}, {{ address.province }}{% endif %}<br>
                                                        {{ address.country }} {{ address.zip }}
                                                    </p>
                                                    <div class="flex jc-start ai-center colgap-8">
                                                        {%- assign delete_confirm = 'customer.addresses.delete_confirm' | t -%}
                                                        <a
                                                            href="#"
                                                            rel="nofollow"
                                                            @click.prevent="showEditForm('edit_{{ forloop.index }}')"
                                                        >
                                                            <span class="icon-link flex">{{ settings.icon_edit }}</span>
                                                        </a>
                                                        <a
                                                            href="#"
                                                            rel="nofollow"
                                                            onclick="Shopify.CustomerAddress.destroy({{ address.id }}, '{{ delete_confirm }}');return false;"
                                                        >
                                                            <span class="icon-link flex">{{ settings.icon_trash }}</span>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {%- endfor -%}
                                </div>
                                <button @click="showNewForm" type="button" class="address-new btn10">{{ 'customer.addresses.add_new' | t }}</button>
                            {%- else -%}
                                <div class="order-empty tc pt-16 pb-32 d-pt-20 d-pb-88">
                                    <p class="p2 mb-24">You haven't added any addresses yet.</p>
                                    <button @click="showNewForm" type="button" class="address-new btn10">{{ 'customer.addresses.add_new' | t }}</button>
                                </div>
                            {%- endif -%}
                        </div>

                        {% comment %} Existing addresses {% endcomment %}                        
                        {%- for address in customer.addresses -%}
                            <div class="edit-address-form" :class="{'hide-m': !editForm.edit_{{ forloop.index }}.show}">
                                <div class="form-edit-relative">
                                    <div class="form-edit-title mb-32 d-mb-60">
                                        <a rel="nofollow" @click="hideEditForm('edit_{{ forloop.index }}')" class="flex ai-center colgap-4">
                                            {{ settings.icon_arrow_left }}
                                            <span class="p2" style="margin-top:1px;">{{ 'customer.addresses.back' | t }}</span>
                                        </a>
                                        <h3 class="h3 mt-24">{{ 'customer.addresses.edit_address' | t }} </h3>
                                    </div>
                                    <div class="form-address-box d-pr-48">
                                        {%- assign form_id = 'address_edit_' | append: forloop.index -%}
                                        {% form 'customer_address', address, id: form_id %}
                                        <div class="account-form">
                                            <div class="container-max flex flex-column t-flex-row t-jc-between t-ai-center rowgap-12 t-rowgap-16 t-colgap-16 mb-12 d-mb-16">
                                                <div class="col-12 col-t-6 account-form-each">
                                                    <div class="global-input">
                                                        <input
                                                            type="text"
                                                            id="address_first_name_{{form.id}}"
                                                            class="p2"
                                                            name="address[first_name]"
                                                            value="{{form.first_name}}"
                                                            placeholder="{{ 'customer.addresses.first_name' | t }}"
                                                            v-model="editForm.edit_{{ forloop.index }}.first_name"
                                                            :class="{'global-input-account-error': editForm.edit_{{ forloop.index }}.first_name_error}"
                                                        >
                                                    </div>
                                                    <div class="global-error-msg p2 mt-4" :class="{'hide-m': !editForm.edit_{{ forloop.index }}.first_name_error}">{{ 'customer.addresses.first_name_error' | t }}</div>
                                                </div>
                                                <div class="col-12 col-t-6 account-form-each">
                                                    <div class="global-input">
                                                        <input
                                                            type="text"
                                                            id="address_last_name_{{form.id}}"
                                                            class="p2"
                                                            name="address[last_name]"
                                                            value="{{form.last_name}}"
                                                            placeholder="{{ 'customer.addresses.last_name' | t }}"
                                                            v-model="editForm.edit_{{ forloop.index }}.last_name"
                                                            :class="{'global-input-account-error': editForm.edit_{{ forloop.index }}.last_name_error}"
                                                        >
                                                    </div>
                                                    <div class="global-error-msg p2 mt-4" :class="{'hide-m': !editForm.edit_{{ forloop.index }}.last_name_error}">{{ 'customer.addresses.last_name_error' | t }}</div>
                                                </div>
                                            </div>
                                            <div class="account-form-each mb-12 d-mb-16" style="display:none;">
                                                <div class="global-input">
                                                    <input
                                                        type="text"
                                                        id="address_company_{{form.id}}"
                                                        class="p2"
                                                        name="address[company]"
                                                        value="{{form.company}}"
                                                        placeholder="{{ 'customer.addresses.company' | t }}"
                                                    >
                                                </div>
                                            </div>
                                            <div class="account-form-each mb-12 d-mb-16">
                                                <div class="global-input">
                                                    <input
                                                        type="text"
                                                        id="address_address1_{{form.id}}"
                                                        class="p2"
                                                        name="address[address1]"
                                                        value="{{form.address1}}"
                                                        placeholder="{{ 'customer.addresses.address1' | t }}"
                                                        v-model="editForm.edit_{{ forloop.index }}.address1"
                                                        :class="{'global-input-account-error': editForm.edit_{{ forloop.index }}.address1_error}"
                                                    >
                                                </div>
                                                    <div class="global-error-msg p2 mt-4" :class="{'hide-m': !editForm.edit_{{ forloop.index }}.address1_error}">{{ 'customer.addresses.address1_error' | t }}</div>
                                            </div>
                                            <div class="account-form-each mb-12 d-mb-16">
                                                <div class="global-input">
                                                    <input
                                                        type="text"
                                                        id="address_address2_{{form.id}}"
                                                        class="p2"
                                                        name="address[address2]"
                                                        value="{{form.address2}}"
                                                        placeholder="{{ 'customer.addresses.address2' | t }}"
                                                    >
                                                </div>
                                            </div>
                                            <div class="region-grid rowgap-12 t-rowgap-16 t-colgap-16 mb-12 d-mb-16">
                                                <div class="account-form-each fill-empty">
                                                    <div class="global-input">
                                                        <select
                                                            id="address_country_{{form.id}}"
                                                            name="address[country]"
                                                            data-default="{{form.country}}"
                                                            data-form-id="{{form.id}}"
                                                            class="country-dropdown global-input p2"
                                                        >
                                                            {{ all_country_option_tags }}
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="account-form-each" id="address_province_container_{{form.id}}" style="display:none;">
                                                    <div class="global-input">
                                                        <select
                                                            id="address_province_{{form.id}}"
                                                            class="product-buy-options province-dropdown global-input p2"
                                                            name="address[province]"
                                                            data-default="{{form.province}}"
                                                        ></select>
                                                    </div>
                                                </div>
                                                <div class="account-form-each">
                                                    <div class="global-input">
                                                        <input
                                                            type="text"
                                                            id="address_city_{{form.id}}"
                                                            class="p2"
                                                            name="address[city]"
                                                            value="{{form.city}}"
                                                            placeholder="{{ 'customer.addresses.city' | t }}"
                                                            v-model="editForm.edit_{{ forloop.index }}.city"
                                                            :class="{'global-input-account-error': editForm.edit_{{ forloop.index }}.city_error}"
                                                        >
                                                    </div>
                                                    <div class="global-error-msg p2 mt-4" :class="{'hide-m': !editForm.edit_{{ forloop.index }}.city_error}">{{ 'customer.addresses.city_error' | t }}</div>
                                                </div>
                                                <div class="account-form-each">
                                                    <div class="global-input">
                                                        <input
                                                            type="text"
                                                            id="address_zip_{{form.id}}"
                                                            class="p2"
                                                            name="address[zip]"
                                                            value="{{form.zip}}"
                                                            placeholder="{{ 'customer.addresses.zip' | t }}"
                                                            v-model="editForm.edit_{{ forloop.index }}.zip"
                                                            :class="{'global-input-account-error': editForm.edit_{{ forloop.index }}.zip_error}"
                                                        >
                                                    </div>
                                                    <div class="global-error-msg p2 mt-4" :class="{'hide-m': !editForm.edit_{{ forloop.index }}.zip_error}">{{ 'customer.addresses.zip_error' | t }}</div>
                                                </div>
                                            </div>
                                            <div class="container-max flex flex-column rowgap-12">
                                                <div class="col-12 account-form-each" style="display: none;">
                                                    <div class="global-input">
                                                        <input
                                                            type="text"
                                                            id="address_phone_{{form.id}}"
                                                            class="p2"
                                                            name="address[phone]"
                                                            value="{{form.phone}}"
                                                            placeholder="{{ 'customer.addresses.phone' | t }}"
                                                        >
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="account-form-each global-checkbox">
                                                <label class="checkbox-label">
                                                    <input
                                                        type="checkbox"
                                                        id="address_default_address_{{ form.id }}"
                                                        name="address[default]"
                                                        value="1"
                                                        v-model="editForm['default_{{ forloop.index }}']"
                                                    >
                                                    <div class="text">
                                                        <p class="p3">{{ 'customer.addresses.set_default' | t }}</p>
                                                    </div>
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                            <button class="btn2 mt-24 d-mt-32 w-full d-w-auto" type="submit">{{ 'customer.addresses.submit' | t }}</button>
                                        </div>
                                        {% endform %}
                                    </div>
                                </div>
                            </div>
                        {%- endfor -%}
                        {% comment %} End Existing addresses {% endcomment %}

                        {% comment %} New address {% endcomment %}
                        <div class="new-address-form" :class="{'hide-m': !newForm.show}">
                            <div class="form-add">
                                <div class="form-add-title mb-32 d-mb-60">
                                    <a rel="nofollow" @click="hideNewForm" class="flex ai-center colgap-4">
                                        {{ settings.icon_arrow_left }}
                                        <span class="p2" style="margin-top:1px;">{{ 'customer.addresses.back' | t }}</span>
                                    </a>
                                    <h3 class="h3 mt-24">{{ 'customer.addresses.add' | t }} </h3>
                                </div>
                                <div class="form-address-box d-pr-48">
                                    {% form 'customer_address', customer.new_address %}
                                        <div class="account-form">
                                            <div class="container-max flex flex-column t-flex-row t-jc-between t-ai-center rowgap-12 t-colgap-16 mb-12 d-mb-16">
                                                <div class="col-12 col-t-6 account-form-each">
                                                    <div class="global-input">
                                                        <input
                                                            type="text"
                                                            id="address_first_name_{{form.id}}"
                                                            class="p2"
                                                            name="address[first_name]"
                                                            value="{{form.first_name}}"
                                                            placeholder="{{ 'customer.addresses.first_name' | t }}"
                                                            v-model="newForm.first_name"
                                                            :class="{'global-input-account-error': newForm.first_name_error}"
                                                        >
                                                    </div>
                                                    <div class="global-error-msg p2 mt-4" :class="{'hide-m': !newForm.first_name_error}">{{ 'customer.addresses.first_name_error' | t }}</div>
                                                </div>
                                                <div class="col-12 col-t-6 account-form-each">
                                                    <div class="global-input">
                                                        <input
                                                            type="text"
                                                            id="address_last_name_{{form.id}}"
                                                            class="p2"
                                                            name="address[last_name]"
                                                            value="{{form.last_name}}"
                                                            placeholder="{{ 'customer.addresses.last_name' | t }}"
                                                            v-model="newForm.last_name"
                                                            :class="{'global-input-account-error': newForm.last_name_error}"
                                                        >
                                                    </div>
                                                    <div class="global-error-msg p2 mt-4" :class="{'hide-m': !newForm.last_name_error}">{{ 'customer.addresses.last_name_error' | t }}</div>
                                                </div>
                                            </div>
                                            <div class="account-form-each mb-12 d-mb-16">
                                                <div class="global-input">
                                                    <input
                                                        type="text"
                                                        id="address_company_{{form.id}}"
                                                        class="p2"
                                                        name="address[company]"
                                                        value="{{form.company}}"
                                                        placeholder="{{ 'customer.addresses.company' | t }}"
                                                    >
                                                </div>
                                            </div>
                                            <div class="account-form-each mb-12 d-mb-16">
                                                <div class="global-input">
                                                    <input
                                                        type="text"
                                                        id="address_address1_{{form.id}}"
                                                        class="p2"
                                                        name="address[address1]"
                                                        value="{{form.address1}}"
                                                        placeholder="{{ 'customer.addresses.address1' | t }}"
                                                        v-model="newForm.address1"
                                                        :class="{'global-input-account-error': newForm.address1_error}"
                                                    >
                                                </div>
                                                <div class="global-error-msg p2 mt-4" :class="{'hide-m': !newForm.first_name_error}">{{ 'customer.addresses.address1_error' | t }}</div>
                                            </div>
                                            <div class="account-form-each mb-12 d-mb-16">
                                                <div class="global-input">
                                                    <input
                                                        type="text"
                                                        id="address_address2_{{form.id}}"
                                                        class="p2"
                                                        name="address[address2]"
                                                        value="{{form.address2}}"
                                                        placeholder="{{ 'customer.addresses.address2' | t }}"
                                                    >
                                                </div>
                                            </div>
                                            <div class="region-grid rowgap-12 t-rowgap-16 t-colgap-16 mb-12 d-mb-16">
                                                <div class="account-form-each fill-empty">
                                                    <div class="global-input">
                                                        <select
                                                            id="address_country_{{form.id}}"
                                                            name="address[country]"
                                                            data-default="Singapore"
                                                            data-form-id="{{ form.id }}"
                                                            class="country-dropdown global-input p2"
                                                        >
                                                            {{ all_country_option_tags }}
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="account-form-each" id="address_province_container_{{form.id}}" style="display:none">
                                                    <div class="global-input">
                                                        <select
                                                            id="address_province_{{form.id}}"
                                                            class="product-buy-options province-dropdown global-input p2"
                                                            name="address[province]"
                                                            data-default="{{form.province}}"
                                                        ></select>
                                                    </div>
                                                </div>
                                                <div class="account-form-each">
                                                    <div class="global-input">
                                                        <input
                                                            type="text"
                                                            id="address_city_{{form.id}}"
                                                            class="p2"
                                                            name="address[city]"
                                                            value="{{form.city}}"
                                                            placeholder="{{ 'customer.addresses.city' | t }}"
                                                            v-model="newForm.city"
                                                            :class="{'global-input-account-error': newForm.city_error}"
                                                        >
                                                    </div>
                                                    <div class="global-error-msg p2 mt-4" :class="{'hide-m': !newForm.city_error}">{{ 'customer.addresses.city_error' | t }}</div>
                                                </div>                                      
                                                <div class="account-form-each">
                                                    <div class="global-input">
                                                        <input
                                                            type="text"
                                                            id="address_zip_{{form.id}}"
                                                            class="p2"
                                                            name="address[zip]"
                                                            value="{{form.zip}}"
                                                            placeholder="{{ 'customer.addresses.zip' | t }}"
                                                            v-model="newForm.zip"
                                                            :class="{'global-input-account-error': newForm.zip_error}"
                                                        >
                                                    </div>
                                                    <div class="global-error-msg p2 mt-4" :class="{'hide-m': !newForm.zip_error}">{{ 'customer.addresses.zip_error' | t }}</div>
                                                </div>
                                            </div>
                                            <div class="container-max flex flex-column rowgap-12">
                                                <div class="col-12 account-form-each" style="display: none;">
                                                    <div class="global-input">
                                                        <input
                                                            type="text"
                                                            id="address_phone_{{form.id}}"
                                                            class="p2"
                                                            name="address[phone]"
                                                            value="{{form.phone}}"
                                                            placeholder="{{ 'customer.addresses.phone' | t }}"
                                                        >
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="account-form-each global-checkbox">
                                                <label class="checkbox-label">
                                                    <input
                                                        type="checkbox"
                                                        id="address_default_address_{{ form.id }}"
                                                        name="address[default]"
                                                        value="0"
                                                    >
                                                    <div class="text">
                                                        <p class="p3">{{ 'customer.addresses.set_default' | t }}</p>
                                                    </div>
                                                    <span class="checkmark"></span>
                                                </label>
                                            </div>
                                            <button class="btn2 mt-24 d-mt-32 w-full d-w-auto" type="submit">{{ 'customer.addresses.submit' | t }}</button>
                                        </div>
                                    {% endform %}
                                </div>
                            </div>
                        </div>
                        {% comment %} End New address {% endcomment %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<script>
    Vue.createApp({
        delimiters: ['${', '}'],
        data() {
            return {
                showList: true,
                newForm: {
                    show: false,
                    first_name: null,
                    last_name: null,
                    address1: null,
                    city: null,
                    zip: null,
                    first_name_error: false,
                    last_name_error: false,
                    address1_error: false,
                    city_error: false,
                    zip_error: false,
                    has_error: false
                },
                editForm: {
                    {% for address in customer.addresses %}
                    {%- assign default = false -%}
                    {%- if address == customer.default_address -%}
                        {%- assign default = true -%}
                    {%- endif -%}
                    edit_{{ forloop.index }}: {
                        show: false,
                        first_name: '{{ address.first_name }}',
                        last_name: '{{ address.last_name }}',
                        address1: '{{ address.address1 }}',
                        city: '{{ address.city }}',
                        zip: '{{ address.zip }}',
                        first_name_error: false,
                        last_name_error: false,
                        address1_error: false,
                        city_error: false,
                        zip_error: false,
                        has_error: false
                    },
                    default_{{ forloop.index }}: {{ default }},
                    {% endfor %}
                }
            }
        },
        mounted() {
            const thisObj = this;
            document.getElementById('address_form_new').addEventListener('submit', function (e) {
                e.preventDefault();

                thisObj.newForm.has_error = false;
                thisObj.newForm.first_name_error = false;
                thisObj.newForm.last_name_error = false;
                thisObj.newForm.address1_error = false;
                thisObj.newForm.city_error = false;
                thisObj.newForm.zip_error = false;

                if (!thisObj.newForm.first_name) {
                    thisObj.newForm.first_name_error = true;
                    thisObj.newForm.has_error = true;
                }
                if (!thisObj.newForm.last_name) {
                    thisObj.newForm.last_name_error = true;
                    thisObj.newForm.has_error = true;
                }
                if (!thisObj.newForm.address1) {
                    thisObj.newForm.address1_error = true;
                    thisObj.newForm.has_error = true;
                }
                if (!thisObj.newForm.city) {
                    thisObj.newForm.city_error = true;
                    thisObj.newForm.has_error = true;
                }
                if (!thisObj.newForm.zip) {
                    thisObj.newForm.zip_error = true;
                    thisObj.newForm.has_error = true;
                }

                if (thisObj.newForm.has_error) {
                    e.stopImmediatePropagation();
                }
            });

            {% for address in customer.addresses %}
                document.getElementById('address_edit_{{ forloop.index }}').addEventListener('submit', function (e) {
                    e.preventDefault();

                    thisObj.editForm.edit_{{ forloop.index }}.has_error = false;
                    thisObj.editForm.edit_{{ forloop.index }}.first_name_error = false;
                    thisObj.editForm.edit_{{ forloop.index }}.last_name_error = false;
                    thisObj.editForm.edit_{{ forloop.index }}.address1_error = false;
                    thisObj.editForm.edit_{{ forloop.index }}.city_error = false;
                    thisObj.editForm.edit_{{ forloop.index }}.zip_error = false;

                    if (!thisObj.editForm.edit_{{ forloop.index }}.first_name) {
                        thisObj.editForm.edit_{{ forloop.index }}.first_name_error = true;
                        thisObj.editForm.edit_{{ forloop.index }}.has_error = true;
                    }
                    if (!thisObj.editForm.edit_{{ forloop.index }}.last_name) {
                        thisObj.editForm.edit_{{ forloop.index }}.last_name_error = true;
                        thisObj.editForm.edit_{{ forloop.index }}.has_error = true;
                    }
                    if (!thisObj.editForm.edit_{{ forloop.index }}.address1) {
                        thisObj.editForm.edit_{{ forloop.index }}.address1_error = true;
                        thisObj.editForm.edit_{{ forloop.index }}.has_error = true;
                    }
                    if (!thisObj.editForm.edit_{{ forloop.index }}.city) {
                        thisObj.editForm.edit_{{ forloop.index }}.city_error = true;
                        thisObj.editForm.edit_{{ forloop.index }}.has_error = true;
                    }
                    if (!thisObj.editForm.edit_{{ forloop.index }}.zip) {
                        thisObj.editForm.edit_{{ forloop.index }}.zip_error = true;
                        thisObj.editForm.edit_{{ forloop.index }}.has_error = true;
                    }

                    if (thisObj.editForm.edit_{{ forloop.index }}.has_error) {
                        e.stopImmediatePropagation();
                    }
                });
            {% endfor %}

            document.querySelectorAll('select.global-input').forEach(el => {
                el.addEventListener('click', () => {
                    if (!el.classList.contains('active')) {
                        el.classList.add('active');
                    } else {
                        el.classList.remove('active');
                    }
                });
            });
        },
        methods: {
            showNewForm() {
                this.showList = false;
                this.newForm.show = true;

                /** Handling province options */
                if (Shopify && Shopify.CountryProvinceSelector) {
                    setTimeout(function() {
                        document.querySelectorAll('.country-dropdown').forEach(el => {
                            const formId = el.getAttribute('data-form-id');
                            let countrySelector = 'address_country_' + formId;
                            let provinceSelector = 'address_province_' + formId;
                            let containerSelector = 'address_province_container_' + formId;

                            let countryEl         = document.getElementById(countrySelector);
                            let provinceEl        = document.getElementById(provinceSelector);
                            let provinceContainer = document.getElementById(containerSelector);

                            el.addEventListener('change', () => {
                                const opt = countryEl.options[countryEl.selectedIndex];
                                const raw = opt.getAttribute('data-provinces');
                                const provinces = JSON.parse(raw);

                                while (provinceEl.firstChild) {
                                    provinceEl.removeChild(provinceEl.firstChild);
                                }

                                if (provinces && provinces.length == 0) {
                                    provinceContainer.style.display = 'none';
                                    countryEl.closest('.account-form-each').classList.add("fill-empty");
                                } else {
                                    for (let i = 0, count = provinces.length; i < provinces.length; i++) {
                                        const options = document.createElement('option');
                                        options.value = provinces[i][0];
                                        options.innerHTML = provinces[i][1];
                                        provinceEl.appendChild(options);
                                    }
                                    provinceContainer.style.display = "";
                                    countryEl.closest('.account-form-each').classList.remove("fill-empty");
                                }
                            });

                            const value = countryEl.getAttribute('data-default');
                            Shopify.setSelectorByValue(countryEl, value);
                            
                            // const formId = el.getAttribute('data-form-id');
                            // var countrySelector = 'address_country_' + formId;
                            // var provinceSelector = 'address_province_' + formId;
                            // var containerSelector = 'address_province_container_' + formId;

                            // new Shopify.CountryProvinceSelector(countrySelector, provinceSelector, {
                            //     hideElement: containerSelector
                            // });
                        });
                    }, 200);
                }
                /** End handling province options */
            },
            hideNewForm() {
                this.showList = true;
                this.newForm.show = false;
            },
            showEditForm(id) {
                this.showList = false;
                this.editForm[id].show = true;

                /** Handling province options */
                if (Shopify && Shopify.CountryProvinceSelector) {
                    setTimeout(function() {
                        document.querySelectorAll('.country-dropdown').forEach(el => {
                            const formId = el.getAttribute('data-form-id');
                            var countrySelector = 'address_country_' + formId;
                            var provinceSelector = 'address_province_' + formId;
                            var containerSelector = 'address_province_container_' + formId;

                            new Shopify.CountryProvinceSelector(countrySelector, provinceSelector, {
                                hideElement: containerSelector
                            });

                            let countryEl         = document.getElementById(countrySelector);
                            const opt = countryEl.options[countryEl.selectedIndex];
                            const raw = opt.getAttribute('data-provinces');
                            const provinces = JSON.parse(raw);

                            if (provinces && provinces.length == 0) {
                                countryEl.closest('.account-form-each').classList.add("fill-empty");
                            } else {
                                countryEl.closest('.account-form-each').classList.remove("fill-empty");
                            }
                        });
                    }, 200);
                }
                /** End handling province options */
            },
            hideEditForm(id) {
                this.showList = true;
                this.editForm[id].show = false;
            },
            hideAllForm() {
                this.newForm.show = false;
                {% for address in customer.addresses %}
                this.editForm.edit_{{ forloop.index }}.show = false;
                {% endfor %}
            }
        }
    }).mount('#{{ el_id }}_address-wrapper');
</script>
{% schema %}
    {
        "name": "Account Addresses",
        "settings": [],
        "presets": [{ "name": "Account Addresses" }]
    }
{% endschema %}