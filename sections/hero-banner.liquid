{% liquid
  
  assign section_name = "Hero Banner"
  assign el_id = section_name | handle | append: "-" | append: section.id
  
  assign section_padding = section.settings.padding
  assign padding_class = "sp-" | append: section_padding
  
  if section.settings.no_padding_top
    assign padding_class = padding_class | append: " spt-no"
  else
    if section.settings.top_half_padding
      assign padding_class = padding_class | append: " spt-" | append: section_padding | append: "-half"
    endif
  endif
  
  if section.settings.no_padding_bottom
    assign padding_class = padding_class | append: " spb-no"
  else
    if section.settings.bottom_half_padding
      assign padding_class = padding_class | append: " spb-" | append: section_padding | append: "-half"
    endif
  endif

  assign has_content = false
  if section.settings.title != blank or section.settings.text != blank or section.settings.image != blank or section.settings.video != blank
    assign has_content = true
  endif

%}

{% if request.visual_preview_mode %}
  <img src="{{ "section-preview_hero-banner.png" | asset_url }}" width="100%" height="100%" />
{% else %}

{% if has_content == true %}

{% if section.settings.padding_system == "manual" %}
  {% assign padding_class = "sp" %}
  <style>
    #{{ el_id }} .sp {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
    @media only screen and (min-width: 1024px) {
      #{{ el_id }} .sp {
        padding-top: {{ section.settings.padding_top_d }}px;
        padding-bottom: {{ section.settings.padding_bottom_d }}px;
      }
    }
  </style>
{% endif %}

<style>

  #{{ el_id }} .overlay {
    opacity: unset;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 50.48%, rgba(0, 0, 0, 0.30) 100%);
  }

  .{{ el_id }}__inner {
    display: grid;
  }

  .{{ el_id }}__inner > * {
    width: 100%;
    grid-area: 1 / 1;
  }

  .{{ el_id }}__inner figure .global-image-wrapper {
    min-height: 100%;
  }

  .{{ el_id }}__inner figure .global-image-wrapper img {
    height: 100%;
    object-fit: cover;
  }

  {% if section.settings.text_color == "light" %}
    .{{ el_id }}__content article {
      color: white;
    }
  {% endif %}

  {% unless section.settings.alignment == "center" or section.settings.va == "center" %}
  
    #{{ el_id }} article,
    #{{ el_id }} article + div {
      min-width: 20%;
      max-width: 70%
    }

  {% endunless %}

  #{{ el_id }} article + div {
    flex: none;
    {% if section.settings.alignment == "center" %}
      width: 60%;
    {% endif %}
  }

  @media only screen and (min-width: 600px) {

    #{{ el_id }} article + div {
      width: 100%;
    }

    #{{ el_id }} article,
    #{{ el_id }} article + div {
      min-width: unset;
      max-width: unset;
    }

  }

</style>

{% capture kicker %}
  {% unless section.settings.kicker == blank %}
    <{{ section.settings.kicker_element }} class="{{ section.settings.kicker_class }}">
      {{ section.settings.kicker }}
    </{{ section.settings.kicker_element }}>
  {% endunless %}
{% endcapture %}

{% capture title %}
  {% unless section.settings.title == blank %}
    <{{ section.settings.title_element }} class="{{ section.settings.title_class }}">
      {{ section.settings.title }}
    </{{ section.settings.title_element }}>
  {% endunless %}
{% endcapture %}

{% capture text %}
  {% unless section.settings.text ==  blank %}
    <p class="{{ section.settings.text_class }}">{{ section.settings.text }}</p>
  {% endunless %}
{% endcapture %}

{% liquid
  assign cta_label_1 = section.settings.cta_label_1
  assign cta_class_1 = section.settings.cta_class_1
  assign cta_url_1 = section.settings.cta_url_1
  assign cta_label_2 = section.settings.cta_label_2
  assign cta_class_2 = section.settings.cta_class_2
  assign cta_url_2 = section.settings.cta_url_2
%}

{% capture cta_1 %}
  {% unless cta_label_1 == blank %}
    {% if cta_url_1 and cta_url_2 %}
      <a href="{{ cta_url_1 }}" class="{{ cta_class_1 }}">{{ cta_label_1 }}</a>
    {% else %}
      <span class="{{ cta_class_1 }}">{{ cta_label_1 }}</span>
    {% endif %}
  {% endunless %}
{% endcapture %}

{% capture cta_2 %}
  {% unless cta_label_2 == blank %}
    {% if cta_url_1 and cta_url_2 %}
      <a href="{{ cta_url_2 }}" class="{{ cta_class_2 }}">{{ cta_label_2 }}</a>
    {% else %}
      <span class="{{ cta_class_2 }}">{{ cta_label_2 }}</span>
    {% endif %}
  {% endunless %}
{% endcapture %}

{% capture image_render %}
  {% unless section.settings.image == blank and section.settings.image_m == blank %}
    {%- assign default_image_alt = section_name | append: " Image"  %}
    {%- assign image_alt = section.settings.title | default: default_image_alt | escape -%}
    <figure>
      {%- render 'global-image-wrapper-responsive',
        desktop_image: section.settings.image,
        mobile_image: section.settings.image_m,
        image_id: section.id,
        image_alt: image_alt,
        preload: section.settings.preload,
        overlay: section.settings.overlay,
        image_change_breakpoint: "desktop"
      -%}
    </figure>
  {% endunless %}
{% endcapture %}

{% capture video_render %}
  {% unless section.settings.video == blank and section.settings.video_m == blank %}
    {% assign preload = "none" %}
    {% if section.settings.preload %}
      {% assign preload = "auto" %}
    {% endif %}
    {% render "global-video-wrapper-responsive",
      video: section.settings.video,
      video_m: section.settings.video_m,
      preload: preload,
      overlay: section.settings.overlay,
    %}
  {% endunless %}
{% endcapture %}

{% capture media_render %}
  {% if section.settings.media_type == "image" %}
    {{ image_render }}
  {% elsif section.settings.media_type == "video" %}
    {{ video_render }}
  {% endif %}
{% endcapture %}

{% liquid
  
  assign alignment = section.settings.alignment
  assign alignment_t = section.settings.alignment_t

  assign va = section.settings.va
  assign va_t = section.settings.va_t

  assign ha = ""
  assign ha_t = ""
  assign t = ""
  assign t_t = ""

  if alignment == "left"
    assign ha = "start"
    assign t = "l"
  elsif alignment == "right"
    assign ha = "end"
    assign t = "r"
  elsif alignment == "center"
    assign ha = "center"
    assign t = "c"
  endif

  if alignment_t == "left"
    assign ha_t = "start"
    assign t_t = "l"
  elsif alignment_t == "right"
    assign ha_t = "end"
    assign t_t = "r"
  elsif alignment_t == "center"
    assign ha_t = "center"
    assign t_t = "c"
  endif

  assign col = section.settings.col
  assign col_t = section.settings.col_t
  assign col_d = section.settings.col_d
  assign col_hd = section.settings.col_hd
  assign col_hhd = section.settings.col_hhd

  assign wrapper_el = "div"
  assign href = ""
  if cta_url_1 == blank or cta_url_2 == blank
    assign href = cta_url_1 | default: cta_url_2
  endif
  unless href == blank
    assign wrapper_el = "a"
  endunless

%}

{% capture position_classes %}
  ai-{{ va }} t-ai-{{ va_t }} jc-{{ ha }} t-jc-{{ ha_t }}
{% endcapture %}

{%- capture alignment_classes -%}t{{ t }} t-t{{ t_t }}{%- endcapture -%}
{%- capture col_classes -%}col-{{ col }} col-t-{{ col_t }} col-d-{{ col_d }} col-hd-{{ col_hd }} col-hhd-{{ col_hhd }}{%- endcapture -%}

<div id="{{ el_id }}">
  <div class="{{ padding_class }}">
    <{{ wrapper_el }}
      {% if href != blank %}href="{{ href }}"{% endif %}
      class="{{ el_id }}__inner bg-brown-20"
    >
      {{ media_render }}
      <div class="{{ el_id }}__content container pt-48 pb-48 d-pt-32 d-pb-32 zi-1">
        <div class="row h-full flex {{ position_classes }}">
          <div class="{{ col_classes }} {{ alignment_classes }}">
            {% liquid
              assign content_classes = "flex-column"
              if alignment == "center"
                assign content_classes = content_classes | append: " ai-center"
              endif
              unless alignment == "center" or va == "center"
                assign content_classes = "t-flex-column"
                if alignment == "left"
                  assign content_classes = content_classes | append: " flex-row"
                elsif alignment == "right"
                  assign content_classes = content_classes | append: " row-reverse"
                endif
              endunless
            %}
            <div class="flex {{ content_classes }} colgap-20 rowgap-28 d-rowgap-40">
              {% unless title == blank and text == blank and kicker == blank %}
                <article class="flex flex-column rowgap-24">
                  {{ kicker }}
                  {% unless title == blank and text == blank %}
                    <div class="flex flex-column rowgap-12 d-rowgap-16">
                      {{ title }}
                      {{ text }}
                    </div>
                  {% endunless %}
                </article>
              {% endunless %}
              {% unless cta_1 == blank and cta_2 == blank %}
                {% liquid
                  assign cta_wrapper_classes = "t-jc-" | append: ha_t
                  if alignment != "center" and va != "center"
                    if va == "end"
                      assign cta_wrapper_classes = cta_wrapper_classes | append: " jc-end"
                    endif
                    if alignment == "left"
                      assign cta_wrapper_classes = cta_wrapper_classes | append: " ai-end"
                    endif
                    if alignment == "right"
                      assign cta_wrapper_classes = cta_wrapper_classes | append: " ai-start"
                    endif
                  else
                    if alignment != "center"
                      assign cta_wrapper_classes = cta_wrapper_classes | append: " ai-" | append: ha
                    endif
                  endif
                %}
                <div class="flex flex-column t-flex-row {{ cta_wrapper_classes }} flex-wrap colgap-12 d-colgap-16 rowgap-12">
                  {{ cta_1 }}
                  {{ cta_2 }}
                </div>
              {% endunless %}
            </div>
          </div>
        </div>
      </div>
    </{{ wrapper_el }}>
  </div>
</div>

{% endif %} {% comment %} has_content {% endcomment %}

{% endif %} {% comment %} request.visual_preview_mode {% endcomment %}

{% schema %}
{
  "name": "Hero Banner",
  "settings": [
    { "type": "header", "content": "General Settings" },
      {
        "type": "select",
        "id": "text_color",
        "label": "Text Color",
        "options": [
          { "value": "dark", "label": "Dark" },
          { "value": "light", "label": "Light" },
        ]
      },
    { "type": "header", "content": "Media Settings" },
      {
        "type": "select",
        "id": "media_type",
        "label": "Media Type",
        "options": [
          { "label": "Image", "value": "image" },
          { "label": "Video", "value": "video" }
        ],
        "default": "image"
      },
      {
        "type": "image_picker",
        "id": "image",
        "label": "Image (All Desktop)"
      },
      {
        "type": "image_picker",
        "id": "image_m",
        "label": "Image (Mobile & Tablet)"
      },
      {
        "type": "video",
        "id": "video",
        "label": "Video (Tablet and All Desktop)"
      },
      {
        "type": "video",
        "id": "video_m",
        "label": "Video (Mobile)"
      },
      {
        "type": "checkbox",
        "id": "preload",
        "label": "Preload Media"
      },
      {
        "type": "checkbox",
        "id": "overlay",
        "label": "Show Overlay"
      },
    { "type": "header", "content": "Small Title Settings" },
      {
        "type": "text",
        "id": "kicker",
        "label": "Title",
      },
      {
        "type": "select",
        "id": "kicker_element",
        "label": "Small Title Element",
        "options": [
          { "value": "h1", "label": "<h1>" },
          { "value": "h2", "label": "<h2>" },
          { "value": "h3", "label": "<h3>" },
          { "value": "h4", "label": "<h4>" },
          { "value": "h5", "label": "<h5>" },
          { "value": "h6", "label": "<h6>" }
        ],
        "default": "h5"
      },
      {
        "type": "select",
        "id": "kicker_class",
        "label": "Small Title Class",
        "options": [
          { "value": "h1", "label": "H1" },
          { "value": "h2", "label": "H2" },
          { "value": "h3", "label": "H3" },
          { "value": "h4", "label": "H4" },
          { "value": "h5", "label": "H5" },
          { "value": "h6", "label": "H6" },
        ],
        "default": "h5"
      },
    { "type": "header", "content": "Title Settings" },
      {
        "type": "text",
        "id": "title",
        "label": "Title",
        "default": "Hero Banner Title"
      },
      {
        "type": "select",
        "id": "title_element",
        "label": "Title Element",
        "options": [
          { "value": "h1", "label": "<h1>" },
          { "value": "h2", "label": "<h2>" },
          { "value": "h3", "label": "<h3>" },
          { "value": "h4", "label": "<h4>" },
          { "value": "h5", "label": "<h5>" },
          { "value": "h6", "label": "<h6>" }
        ],
        "default": "h2"
      },
      {
        "type": "select",
        "id": "title_class",
        "label": "Title Class",
        "options": [
          { "value": "h1", "label": "H1" },
          { "value": "h2", "label": "H2" },
          { "value": "h3", "label": "H3" },
          { "value": "h4", "label": "H4" },
          { "value": "h5", "label": "H5" },
          { "value": "h6", "label": "H6" },
        ],
        "default": "h2"
      },
    { "type": "header", "content": "Text Settings" },
      {
        "type": "textarea",
        "id": "text",
        "label": "Text",
        "default": "Hero Banner Text"
      },
      {
        "type": "select",
        "id": "text_class",
        "label": "Text Class",
        "options": [
          { "value": "p1", "label": "P1" },
          { "value": "p2", "label": "P2" },
          { "value": "p3", "label": "P3" },
          { "value": "p4", "label": "P4" },
          { "value": "p5", "label": "P5" },
          { "value": "p6", "label": "P6" },
          { "value": "p7", "label": "P7" },
        ],
        "default": "p1"
      },
    { "type": "header", "content": "CTA Settings" },
      {
        "type": "text",
        "id": "cta_label_1",
        "label": "CTA 1 Label"
      },
      {
        "type": "url",
        "id": "cta_url_1",
        "label": "CTA 1 URL"
      },
      {
        "type": "select",
        "id": "cta_class_1",
        "label": "CTA 1 Class",
        "options": [
          { "value": "btn1", "label": "Button 1" },
          { "value": "btn2", "label": "Button 2" },
          { "value": "btn3", "label": "Button 3" },
          { "value": "btn4", "label": "Button 4" },
          { "value": "btn5", "label": "Button 5" },
          { "value": "btn6", "label": "Button 6" },
          { "value": "btn7", "label": "Button 7" },
          { "value": "btn8", "label": "Button 8" },
          { "value": "btn9", "label": "Button 9" },
          { "value": "btn10", "label": "Button 10" },
          { "value": "btn11", "label": "Button 11" },
          { "value": "link1", "label": "Link 1" },
          { "value": "link3", "label": "Link 2" },
          { "value": "link2", "label": "Link 3" },
          { "value": "link4", "label": "Link 4" },
        ],
        "default": "btn1"
      },
      {
        "type": "text",
        "id": "cta_label_2",
        "label": "CTA 2 Label"
      },
      {
        "type": "url",
        "id": "cta_url_2",
        "label": "CTA 2 URL"
      },
      {
        "type": "select",
        "id": "cta_class_2",
        "label": "CTA 2 Class",
        "options": [
          { "value": "btn1", "label": "Button 1" },
          { "value": "btn2", "label": "Button 2" },
          { "value": "btn3", "label": "Button 3" },
          { "value": "btn4", "label": "Button 4" },
          { "value": "btn5", "label": "Button 5" },
          { "value": "btn6", "label": "Button 6" },
          { "value": "btn7", "label": "Button 7" },
          { "value": "btn8", "label": "Button 8" },
          { "value": "btn9", "label": "Button 9" },
          { "value": "btn10", "label": "Button 10" },
          { "value": "btn11", "label": "Button 11" },
          { "value": "link1", "label": "Link 1" },
          { "value": "link3", "label": "Link 2" },
          { "value": "link2", "label": "Link 3" },
          { "value": "link4", "label": "Link 4" },
        ],
        "default": "btn2"
      },
    { "type": "header", "content": "Position Settings" },
      {
        "type": "select",
        "id": "va",
        "label": "Position (Mobile)",
        "options": [
          { "value": "start", "label": "Top" },
          { "value": "center", "label": "Middle" },
          { "value": "end", "label": "Bottom" },
        ],
        "default": "start"
      },
      {
        "type": "select",
        "id": "va_t",
        "label": "Position (Tablet & All Desktop)",
        "options": [
          { "value": "start", "label": "Top" },
          { "value": "center", "label": "Middle" },
          { "value": "end", "label": "Bottom" },
        ],
        "default": "center"
      },
    { "type": "header", "content": "Alignment Settings" },
      {
        "type": "text_alignment",
        "id": "alignment",
        "label": "Alignment (Mobile)",
        "default": "center"
      },
      {
        "type": "text_alignment",
        "id": "alignment_t",
        "label": "Alignment (Tablet & All Desktop)",
        "default": "left"
      },
    { "type": "header", "content": "Dimension Settings" },
      {
        "type": "range",
        "id": "col",
        "label": "Content Width (Mobile)",
        "min": 1,
        "max": 12,
        "step": 1,
        "unit": "col",
        "default": 12
      },
      {
        "type": "range",
        "id": "col_t",
        "label": "Content Width (Tablet)",
        "min": 1,
        "max": 12,
        "step": 1,
        "unit": "col",
        "default": 10
      },
      {
        "type": "range",
        "id": "col_d",
        "label": "Content Width (Desktop 1024)",
        "min": 1,
        "max": 12,
        "step": 1,
        "unit": "col",
        "default": 6
      },
      {
        "type": "range",
        "id": "col_hd",
        "label": "Content Width (Desktop 1440)",
        "min": 1,
        "max": 12,
        "step": 1,
        "unit": "col",
        "default": 6
      },
      {
        "type": "range",
        "id": "col_hhd",
        "label": "Content Width (Desktop 1920)",
        "min": 1,
        "max": 12,
        "step": 1,
        "unit": "col",
        "default": 6
      },
    { "type": "header", "content": "Section Padding Settings" },
      {
        "type": "select",
        "id": "padding_system",
        "label": "Padding System",
        "options": [
          { "value": "default", "label": "Default" },
          { "value": "manual", "label": "Manual" },
        ],
        "default": "default"
      },
      {
        "type": "select",
        "id": "padding",
        "label": "Padding",
        "options": [
          { "value": "no", "label": "No" },
          { "value": "xs", "label": "Extra Small" },
          { "value": "sm", "label": "Small" },
          { "value": "md", "label": "Med" },
          { "value": "lg", "label": "Large" }
        ],
        "default": "no",
        "visible_if": "{{ section.settings.padding_system == 'default' }}"
      },
      {
        "type": "checkbox",
        "id": "top_half_padding",
        "label": "Top half padding",
        "default": false,
        "visible_if": "{{ section.settings.padding_system == 'default' }}"
      },
      {
        "type": "checkbox",
        "id": "no_padding_top",
        "label": "No padding top",
        "default": false,
        "visible_if": "{{ section.settings.padding_system == 'default' }}"
      },
      {
        "type": "checkbox",
        "id": "bottom_half_padding",
        "label": "Bottom half padding",
        "default": false,
        "visible_if": "{{ section.settings.padding_system == 'default' }}"
      },
      {
        "type": "checkbox",
        "id": "no_padding_bottom",
        "label": "No padding bottom",
        "default": false,
        "visible_if": "{{ section.settings.padding_system == 'default' }}"
      },
      {
        "type": "range",
        "id": "padding_top",
        "label": "Padding Top (Mobile & Tablet)",
        "min": 0,
        "max": 120,
        "step": 2,
        "default": 0,
        "visible_if": "{{ section.settings.padding_system == 'manual' }}"
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "label": "Padding Bottom (Mobile & Tablet)",
        "min": 0,
        "max": 120,
        "step": 2,
        "default": 0,
        "visible_if": "{{ section.settings.padding_system == 'manual' }}"
      },
      {
        "type": "range",
        "id": "padding_top_d",
        "label": "Padding Top (Desktop)",
        "min": 0,
        "max": 120,
        "step": 2,
        "default": 0,
        "visible_if": "{{ section.settings.padding_system == 'manual' }}"
      },
      {
        "type": "range",
        "id": "padding_bottom_d",
        "label": "Padding Bottom (Desktop)",
        "min": 0,
        "max": 120,
        "step": 2,
        "default": 0,
        "visible_if": "{{ section.settings.padding_system == 'manual' }}"
      }
  ],
  "presets": [
    {
      "name": "Hero Banner",
    }
  ]
}
{% endschema %}