{% liquid
    assign section_name = 'Recently Viewed Products'
    assign el_id = section_name | handle | append: '-' | append: section.id

    assign section_padding = section.settings.padding
    assign padding_class = 'sp-' | append: section_padding

    if section.settings.no_padding_top
        assign padding_class = padding_class | append: ' spt-no'
    else
        if section.settings.top_half_padding
            assign padding_class = padding_class | append: ' spt-' | append: section_padding | append: '-half'
        endif
    endif

    if section.settings.no_padding_bottom
        assign padding_class = padding_class | append: ' spb-no'
    else
        if section.settings.bottom_half_padding
            assign padding_class = padding_class | append: ' spb-' | append: section_padding | append: '-half'
        endif
    endif

%}
{% if request.visual_preview_mode %}
    <img src="{{ "section-preview-recently-viewed.png" | asset_url }}" width="100%" height="100%" />
{% else %}
{%- if section.settings.title != blank -%}
<style>
#{{ el_id }} .ctn-wrp {
    margin-left: var(--container_padding_min);
    width: calc(100% + var(--container_padding) + var(--container_padding));
}
#{{ el_id }} .prev-btn,
#{{ el_id }} .next-btn {
    display: none;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 48px;
    height: 48px;
    background: var(--white);
    border: 1px solid var(--border);
    opacity: .7;
    backdrop-filter: blur(5px);
}
#{{ el_id }} .prev-btn:hover,
#{{ el_id }} .next-btn:hover {
    background: var(--white);
}
#{{ el_id }} .prev-btn.swiper-button-disabled,
#{{ el_id }} .next-btn.swiper-button-disabled {
    cursor: not-allowed;
    opacity: 0;
}
#{{ el_id }} .prev-btn svg path,
#{{ el_id }} .next-btn svg path {
    stroke: var(--black);
}
#{{ el_id }} .prev-btn:hover svg path,
#{{ el_id }} .next-btn:hover svg path {
    stroke: var(--black);
}
#{{ el_id }} .prev-btn {
    left: -24px;
}
#{{ el_id }} .next-btn {
    right: -24px;
}
@media screen and (min-width: 600px){
    #{{ el_id }} .ctn-wrp {
        margin-left: var(--container_padding_min_t);
        width: calc(100% + var(--container_padding_t) + var(--container_padding_t));
    }
    #{{ el_id }} .swiper-slide {
        height: unset;
    }
}
@media screen and (min-width: 1024px){
    #{{ el_id }} .prev-btn,
    #{{ el_id }} .next-btn {
        display: flex;
    }
    #{{ el_id }} .ctn-wrp {
        margin-left: 0;
        width: 100%;
    }
    /* #{{ el_id }} .ctn-wrp {
        margin-left: var(--container_padding_min_d);
        width: calc(100% + var(--container_padding_d) + var(--container_padding_d));
    }*/
    #{{ el_id }} .swiper-slide {
        height: unset;
    }
}
</style>
{% render "separator",
    show: section.settings.border_top,
    full_width: section.settings.border_top_full,
    wrapper_classes: padding_bt
%}
<div id="{{ el_id }}" class="relative hide-m" {% if section.settings.bg_color != blank %}style="background-color: {{ section.settings.bg_color }};"{% endif %}>
    <div class="container">
        <div class="relative {{ padding_class }}">
            <{{ section.settings.title_text_element }} class="{{ section.settings.title_text_class }} tl">{{ section.settings.title }}</{{ section.settings.title_text_element }}>
            <div class="relative main-ctn-wrp">
                <div class="relative ctn-wrp mt-20 d-mt-40">
                    <div v-if="loading" class="loading zi-1">
                        <span class="btn-loading">{{ settings.icon_loading }}</span>
                    </div>
                    <div class="recently-viewed swiper">
                        <div class="swiper-wrapper flex jc-start ai-stretch"></div>
                    </div>
                </div>
                <button class="absolute jc-center ai-center prev-btn rounded-4 zi-1">
                    {{ settings.icon_slide_left }}
                </button>
                <button class="absolute jc-center ai-center next-btn rounded-4 zi-1">
                    {{ settings.icon_slide_right }}
                </button>
            </div>
        </div>
    </div>
</div>
{% render "separator",
    show: section.settings.border_bottom,
    full_width: section.settings.border_bottom_full,
    wrapper_classes: padding_bt
%}
<script>
{%- assign vue_el = el_id | replace: '-', '_' | prepend: 'V'  -%}
var {{ vue_el }} = Vue.createApp({
    delimiters: ['${', '}'],
    data() {
        return {
            loading: false,
            screen: {
                hhd: window.innerWidth > 1919,
                hd: window.innerWidth > 1439 && window.innerWidth < 1920,
                d: window.innerWidth > 1023 && window.innerWidth < 1440,
                t: window.innerWidth > 599 && window.innerWidth < 1024,
                m: window.innerWidth < 600
            },
            recentlyViewed: [],
            swiperObj: null,
            swiperProductCards: {},
        }
    },
    mounted() {
        addEventListener('resize', () => {
            this.screen.hhd = window.innerWidth > 1919;
            this.screen.hd = window.innerWidth > 1439 && window.innerWidth < 1920;
            this.screen.d = window.innerWidth > 1023 && window.innerWidth < 1440;
            this.screen.t = window.innerWidth > 599 && window.innerWidth < 1024;
            this.screen.m = window.innerWidth < 600;
        });
        this.logRecentlyViewed();
    },
    methods: {
        logRecentlyViewed() {
            this.loading = true;
            const recentlyViewedCookie = $360.getCookie('shopify_recently_viewed');
            let rcvVal = [];
            if(recentlyViewedCookie) {
                const rvcSplit = recentlyViewedCookie.split('=');
                const rcvParse = JSON.parse(recentlyViewedCookie.split('=').at(1));
                if(rcvParse.length > 20) {
                    // Limit up to 20 records
                    rcvVal = rcvParse.slice(-20);
                } else {
                    rcvVal = rcvParse;
                }
            }
            const productHandle = '{{ product.handle }}';
            if(rcvVal.includes(productHandle)) {
                rcvVal = rcvVal.filter(handle => handle !== '{{ product.handle }}');
                this.recentlyViewed = rcvVal;
                rcvVal.push('{{ product.handle }}');
            } else {
                rcvVal.push('{{ product.handle }}');
            }
            $360.setCookie('shopify_recently_viewed', JSON.stringify(rcvVal), 30, 'day');
            this.recentlyViewed = rcvVal.filter(handle => handle !== '{{ product.handle }}').slice(-12);
            this.showRecentlyViewed();
        },
        async showRecentlyViewed() {
            const thisObj = this;
            // if(document.querySelector('.recently-viewed .swiper-wrapper').hasChildNodes()) return;
            if(this.recentlyViewed.length > 0) {
                const slugs = this.recentlyViewed.reverse();
                const parser = new DOMParser();
                Promise
                .allSettled(slugs.map(slug => axios.get(`/products/${slug}?view=card`)))
                .then(results => {
                    setTimeout(() => {
                        thisObj.swiperObj = new Swiper('#{{ el_id }} .recently-viewed', {
                            slidesPerView: 1.5,
                            spaceBetween: 8,
                            allowTouchMove: true,
                            loop: false,
                            slidesOffsetBefore: 20,
                            slidesOffsetAfter: 20,
                            calculateHeight: true,
                            navigation: {
                                prevEl: `#{{ el_id }} .prev-btn`,
                                nextEl: `#{{ el_id }} .next-btn`,
                            },
                            breakpoints: {
                                600: {
                                    slidesPerView: 2.5,
                                    spaceBetween: 12,
                                    slidesOffsetBefore: 20,
                                    slidesOffsetAfter: 20,
                                },
                                1024: {
                                    slidesPerView: 4,
                                    spaceBetween: 16,
                                    slidesOffsetBefore: 0,
                                    slidesOffsetAfter: 0,
                                }
                            },
                            on: {
                                init: function() {
                                    $360.lazyLoadInstance.update();
                                    thisObj.updateHeight();
                                },
                                slideChange: function() {
                                    $360.lazyLoadInstance.update();
                                },
                                resize: function() {
                                    $360.lazyLoadInstance.update();
                                    thisObj.updateHeight();
                                }
                            }
                        });

                        results.forEach(result => {
                            if (result.status === 'fulfilled') {
                                const card = result.value.data;
                                const $card = parser.parseFromString(card, 'text/html');
                                const $card_image = $card.querySelector('.product-card');
                                if ($card_image) {
                                    thisObj.swiperObj.appendSlide([
                                        `<div class="swiper-slide">${$card_image.outerHTML.toString()}</div>`
                                    ]);
                                }
                            }
                        });
                    }, 100);
                    this.loading = false;
                    if(document.querySelector('#{{ el_id }}').classList.contains('hide-m')) {
                        document.querySelector('#{{ el_id }}').classList.remove('hide-m');
                    }
                    setTimeout(() => {
                        $360.swiperInCard(`#{{ el_id }} .recently-viewed`);
                    }, 500);
                })
            }
        },
        updateHeight() {
            const itemList = document.querySelectorAll('#{{ el_id }} .swiper .swiper-wrapper .swiper-slide .product-card');
            itemList.forEach(item => {
                item.style.height = ``;
            })
            const maxHeight = document.querySelector('#{{ el_id }} .swiper .swiper-wrapper').offsetHeight;            
            itemList.forEach(item => {
                item.style.height = `${maxHeight}px`;
            })
        }
    }
}).mount('#{{ el_id }}');
window.addEventListener("load", {{ vue_el }}, false);
document.addEventListener('shopify:section:load', function() {
    {{ vue_el }}.$forceUpdate();
});
</script>
{%- endif -%}
{% endif %}

{% schema %}
{
    "name": "Recently Viewed Products",
    "settings": [
		{
			"type": "header",
			"content": "General Settings"
		},
		{
			"type": "color",
			"id": "bg_color",
			"label": "Background Color",
			"default": "#FAF6F5"
		},
		{
            "type": "text",
            "id": "title",
            "label": "Title"
        },
        {
            "type": "select",
            "id": "title_text_element",
            "label": "Title text element",
            "options": [
                {
                    "value": "h1",
                    "label": "h1"
                },
                {
                    "value": "h2",
                    "label": "h2"
                },
                {
                    "value": "h3",
                    "label": "h3"
                },
                {
                    "value": "h4",
                    "label": "h4"
                },
                {
                    "value": "h5",
                    "label": "h5"
                },
                {
                    "value": "h6",
                    "label": "h6"
                }
            ],
            "default": "h2"
        },
        {
            "type": "select",
            "id": "title_text_class",
            "label": "Title text size",
            "options": [
                {
                    "value": "h1",
                    "label": "h1"
                },
                {
                    "value": "h2",
                    "label": "h2"
                },
                {
                    "value": "h3",
                    "label": "h3"
                },
                {
                    "value": "h4",
                    "label": "h4"
                },
                {
                    "value": "h5",
                    "label": "h5"
                },
                {
                    "value": "h6",
                    "label": "h6"
                }
            ],
            "default": "h3"
        },
        {
			"type": "header",
			"content": "Border Settings"
		},
        {
            "type": "checkbox",
            "id": "border_top",
            "label": "Border Top",
            "default": false
        },
        {
            "type": "checkbox",
            "id": "border_top_full",
            "label": "Full Width Border Top",
            "default": false
        },
        {
            "type": "checkbox",
            "id": "border_bottom",
            "label": "Border Bottom",
            "default": false
        },
        {
            "type": "checkbox",
            "id": "border_bottom_full",
            "label": "Full Width Border Bottom",
            "default": false
        },
        {
            "type": "header",
            "content": "Section Padding"
        },
        {
            "type": "select",
            "id": "padding",
            "label": "Padding",
            "options": [
                {
                    "value": "no",
                    "label": "No"
                },
                {
                    "value": "sm",
                    "label": "Small"
                },
                {
                    "value": "md",
                    "label": "Med"
                },
                {
                    "value": "lg",
                    "label": "Large"
                }
            ],
            "default": "md"
        },
        {
            "type": "checkbox",
            "id": "top_half_padding",
            "label": "Top half padding",
            "default": false
        },
        {
            "type": "checkbox",
            "id": "no_padding_top",
            "label": "No padding top",
            "default": false
        },
        {
            "type": "checkbox",
            "id": "bottom_half_padding",
            "label": "Bottom half padding",
            "default": false
        },
        {
            "type": "checkbox",
            "id": "no_padding_bottom",
            "label": "No padding bottom",
            "default": false
        }
    ],
    "presets": [
        {
            "name": "Recently Viewed Products"
        }
    ]
}
{% endschema %}
