{% if customer %}
	<script>window.location.href = '/account'</script>
{% else %}	
	{%- assign el_id = 'register-' | append: section.id -%}
	<style>
		#{{ el_id }} .select-country-code {
			width: 90px;
		}
	</style>
	<div id="{{ el_id }}" class="sp-md">
		<div class="container">
			<div class="row">
				<div class="col-12 col-t-8 col-d-4 push-t-2 push-d-4">
					<div id="register-div">
						{%- form 'create_customer' -%}
							<div class="register-title tc">
								<h1 class="h4 mb-8 d-mb-16">{{ 'customer.register.title' | t }}</h1>
								<p class="p2">
									{{ 'customer.register.sign_in_note' | t }}
									<a href="{{ routes.account_login_url }}" class="underline">{{ 'customer.register.sign_in_link' | t }}</a>
								</p>
							</div>
					
							<div class="mt-16 d-mt-20">
								{%- if form.errors -%}
								<div class="form-errors global-error-msg mb-12 d-mb-16 rte p3">
									{{ form.errors | default_errors }}
								</div>
								{%- endif -%}
						
								<div class="mb-8 d-mb-12">
									<div class="global-input">
										<input type="text" v-model="first_name" name="customer[first_name]" id="first_name" 
											class="global-input-account p2" :class="{'global-input-account-error': first_name_error}"
											placeholder="{{ 'customer.register.first_name' | t }}" />
									</div>
									<div class="global-error-msg p2 mt-8" :class="{'hide-m': !first_name_error}">{{ 'customer.register.error_first_name' | t }}</div>
								</div>
								<div class="mb-8 d-mb-12">
									<div class="global-input">
										<input type="text" v-model="last_name" name="customer[last_name]" id="last_name" 
											class="global-input-account p2" :class="{'global-input-account-error': last_name_error}"
											placeholder="{{ 'customer.register.last_name' | t }}" />
									</div>
									<div class="global-error-msg p2 mt-8" :class="{'hide-m': !last_name_error}">{{ 'customer.register.error_last_name' | t }}</div>
								</div>
								<div class="mb-8 d-mb-12">
									<div class="global-input">
										<input type="email" v-model="email" name="customer[email]" id="email" 
											class="global-input-account p2" :class="{'global-input-account-error': email_error}"
											placeholder="{{ 'customer.register.email' | t }}" />
									</div>
									<div class="global-error-msg p2 mt-8" :class="{'hide-m': !email_error}">{{ 'customer.register.error_email' | t }}</div>
								</div>
								<div class="mb-8 d-mb-12">
									{% liquid
										capture country_codes
											render "country-codes"
										endcapture
										assign country_code_values = country_codes | strip | strip_newlines | split: ','
										assign settings_country_codes = section.settings.country_codes | newline_to_br | strip_newlines | split: '<br />'
										if settings_country_codes.size > 0
											assign country_code_values = settings_country_codes | sort
										endif
										assign default_country_code = "+65"
										if settings_country_codes.size > 0
											assign default_country_code = settings_country_codes | first
										endif
									%}
									<div class="flex colgap-12 d-colgap-16">
										<input type="hidden" name="customer[tags]" :value="'phone:'+country_code+phone">
										{% render "global-select",
											id: 'country_code',
											field_name: 'country_code',
											default_text: default_country_code,
											default_value: default_country_code,
											values: country_code_values,
											option_values: country_code_values,
											selected_text_class: "p2",
											list_option_class: "p2",
											vue_model: 'country_code',
											global_select_class: "select-country-code"
										%}
										<div class="global-input flex-1">
											<input type="tel" v-model="phone" name="customer[phone]" id="phone" 
												class="global-input-account p2" :class="{'global-input-account-error': phone_error}"
												placeholder="{{ 'customer.register.phone' | t }}" />
										</div>
									</div>
									<div class="global-error-msg p2 mt-8" :class="{'hide-m': !phone_error}">{{ 'customer.register.error_phone' | t }}</div>
								</div>
								<div class="mb-8 d-mb-12">
									<div class="password-field global-input relative">
										<input type="password" v-model="password" name="customer[password]" id="password"
											class="global-input-account p2" :class="{'global-input-account-error': password_error}" 
											placeholder="{{ 'customer.register.password' | t }}" />
										<a class="absolute icon-password">
											<span class="icon-show">
												{{ settings.icon_show_password }}
											</span>
											<span class="icon-hide hidden">
												{{ settings.icon_hide_password }}
											</span>
										</a>
									</div>
									<div class="global-error-msg p2 mt-8" :class="{'hide-m': !password_error}">{{ 'customer.login.error_password' | t }}</div>
								</div>
								<div class="button-container mt-12 d-mt-20">
									<input type="hidden" name="customer[accepts_marketing]" value="true" />
									<input type="submit" class="btn2 w-full" value="{{ 'customer.register.submit' | t }}" />
									<div class="tc mt-12 d-mt-20">
										<span class="p3 c-grey">
											{{ 'customer.register.read_and_agree' | t }}
											<a href="/policies/term-of-service" class="underline c-grey">Term Of Service</a>
											and
											<a href="/policies/privacy-policy" class="underline c-grey">Privacy Policy</a>
										</span>
									</div>
								</div>
							</div>
						{%- endform -%}
					</div>
				</div>
			</div>
		</div>
	</div>

	<script>
		Vue.createApp({
			delimiters: ['${', '}'],
			data() {
				return {
					first_name: null,
					last_name: null,
					email: null,
					country_code: '+65',
					phone: null,
					password: null,
					has_error: false,
					first_name_error: false,
					last_name_error: false,
					email_error: false,
					phone_error: false,
					password_error: false
				}
			},
			mounted() {
				const thisObj = this;
				document.getElementById('create_customer').addEventListener('submit', function (e) {
					e.preventDefault();

					thisObj.has_error = false;
					thisObj.first_name_error = false;
					thisObj.last_name_error = false;
					thisObj.email_error = false;
					thisObj.password_error = false;

					if (!thisObj.first_name) {
						thisObj.first_name_error = true;
						thisObj.has_error = true;
					}
					if (!thisObj.last_name) {
						thisObj.last_name_error = true;
						thisObj.has_error = true;
					}
					if (!thisObj.email) {
						thisObj.email_error = true;
						thisObj.has_error = true;
					}
					if (!thisObj.phone) {
						thisObj.phone_error = true;
						thisObj.has_error = true;
					}
					if (!thisObj.password) {
						thisObj.password_error = true;
						thisObj.has_error = true;
					}

					if (thisObj.has_error) {
						e.stopImmediatePropagation();
					}
				});
			}
		}).mount('#register-div');
	</script>
{% endif %}
{% schema %}
	{
		"name": "Register",
		"settings": [],
		"presets": [{ "name": "Register" }]
	}
{% endschema %}
