<style>
{%- for block in section.blocks -%}
.link{{ block.settings.title }}{
  display:inline-block;
  {%- if section.settings.animation != blank -%}transition:{{ section.settings.animation }};{%- endif -%}
  {%- assign aa = "padding,border" | split:"," -%}
  {%- assign bb = "top,bottom,left,right" | split:"," -%}
  {%- for m in aa -%}
  {%- for side in bb -%}
  {%- assign key = m | append:"-" | append: side -%}
  {%- if block.settings[key] != blank and block.settings[key] != 0 -%}{{ key }}:{{ block.settings[key] }}px {% if m == "border" %} solid{% endif %};{%- endif -%}
  {%- endfor -%}
  {%- endfor -%}
  {%- assign cc = "color,background-color,border-color" | split:"," -%}
  {%- for c in cc -%}
  {% if c == 'border-color' and block.settings[c] == blank %}
  {{ c }}:transparent;
  {% endif %}
  {%- if block.settings[c] != blank -%}
  {{ c }}:{{ block.settings[c] }};
  {%- endif -%}
  {%- endfor -%}
  {%- assign key = "font" | append: block.settings.family -%}
  font-family: {{ section.settings[key] }};
  {%- if block.settings.uppercase -%}text-transform:uppercase;{%- endif -%}
  font-weight: {{ block.settings.font-weight }};
  {%- if block.settings.letter-spacing != blank -%}letter-spacing:{{ block.settings.letter-spacing }}px;{%- endif -%}
  font-size:{{ block.settings.mobile-size }}px;
  font-size:{{ block.settings.mobile-size | divided_by: 16.0 }}rem;
  line-height:{{ block.settings.mobile-height }};
}
{%- for c in cc -%}
{%- assign key = "hover-" | append: c -%}
{%- if block.settings[key] != blank -%}
.link{{ block.settings.title }}:hover{
  {{ c }}:{{ block.settings[key] }};
}
{%- endif -%}
{%- endfor -%}
{%- if block.settings.tablet-size != blank or block.settings.tablet-height != blank -%}
@media only screen and (min-width: 600px){
  .link{{ block.settings.title }}{
    {%- if block.settings.tablet-size != blank -%}font-size:{{ block.settings.tablet-size }}px;
    font-size:{{ block.settings.tablet-size | divided_by: 16.0 }}rem;{%- endif -%}
    {%- if block.settings.tablet-height != blank -%}line-height:{{ block.settings.tablet-height }};{%- endif -%}
  }
}
  {%- endif -%}
  {%- if block.settings.desktop-size != blank or block.settings.desktop-height != blank -%}
  @media only screen and (min-width: 1024px){
    .link{{ block.settings.title }}{
      {%- if block.settings.desktop-size != blank -%}font-size:{{ block.settings.desktop-size }}px;
      font-size:{{ block.settings.desktop-size | divided_by: 16.0 }}rem;{%- endif -%}
      {%- if block.settings.desktop-height != blank -%}line-height:{{ block.settings.desktop-height }};{%- endif -%}
    }
  }
    {%- endif -%}
    {%- for i in (1..3) -%}
    {%- assign width = "point" | append:i | append:"-width" -%}
    {%- assign size = "point" | append:i | append:"-size" -%}
    {%- assign height = "point" | append:i | append:"-height" -%}
    {%- if block.settings[width] != blank -%}
      @media only screen and (min-width: {{ block.settings[width]}}px){
        .link{{ block.settings.title }}{
      {%- if block.settings[size] != blank -%}font-size:{{ block.settings[size] }}px;
      font-size:{{ block.settings[size] | divided_by: 16.0 }}rem;{%- endif -%}
      {%- if block.settings[height] != blank -%}line-height:{{ block.settings[height] }};{%- endif -%}
        }
      }
      {%- endif -%}
    {%- endfor -%}
    {%- endfor -%}
    {%- if section.settings.custom != blank %}{{ section.settings.custom }}{%- endif -%}
  </style>
  {% schema %}
  {
    "name": "Links",
    "settings": [
    {
      "type": "textarea",
      "id": "font1",
      "label": "Font Family 1"
    },
    {
      "type": "textarea",
      "id": "font2",
      "label": "Font Family 2"
    },
    {
      "type": "textarea",
      "id": "font3",
      "label": "Font Family 3"
    },
    {
      "type": "textarea",
      "id": "animation",
      "label": "CSS Transition codes",
      "default":"all 1s linear"
    },
    {
      "type": "textarea",
      "id": "custom",
      "label": "Custom CSS rules",
      "info":"Enter any custom CSS rules here if needed"
    }
    ],
    "blocks" : [
    {
      "type": "style",
      "name": "Style",
      "settings": [
      {
        "type":"range",
        "id":"title",
        "label":"Suffix",
        "info":"You will be able to use the CSS class in your liquid files. E.g. if the suffix is 1, the CSS class will be link1.",
        "min":1,
        "max":10,
        "default":1,
        "step":1
      },
      {
        "type":"radio",
        "id":"family",
        "label":"Family",
        "options":[
        {"value":"1","label":"Family 1"},
        {"value":"2","label":"Family 2"},
        {"value":"3","label":"Family 3"}
        ]
      },
      {
        "type": "checkbox",
        "id": "uppercase",
        "label": "Uppercase"
      },
      {
        "type": "text",
        "id": "letter-spacing",
        "label": "Letter Spacing"
      },
      {
        "type": "text",
        "id": "font-weight",
        "label": "Font Weight",
        "default":"400"
      },
      {
        "type": "range",
        "id": "padding-top",
        "label": "Padding Top",
        "min": 0,
        "max": 60,
        "step": 1,
        "default": 10,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding-bottom",
        "label": "Padding Bottom",
        "min": 0,
        "max": 60,
        "step": 1,
        "default": 10,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding-left",
        "label": "Padding Left",
        "min": 0,
        "max": 60,
        "step": 1,
        "default": 10,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "padding-right",
        "label": "Padding Right",
        "min": 0,
        "max": 60,
        "step": 1,
        "default": 10,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "border-top",
        "label": "Border Top",
        "min": 0,
        "max": 20,
        "step": 1,
        "default": 1,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "border-bottom",
        "label": "Border Bottom",
        "min": 0,
        "max": 20,
        "step": 1,
        "default": 1,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "border-left",
        "label": "Border Left",
        "min": 0,
        "max": 20,
        "step": 1,
        "default": 1,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "border-right",
        "label": "Border Right",
        "min": 0,
        "max": 20,
        "step": 1,
        "default": 1,
        "unit": "px"
      },
      {
        "type":"color",
        "id":"color",
        "label":"Colour"
      },
      {
        "type":"color",
        "id":"background-color",
        "label":"Background Colour"
      },
      {
        "type":"color",
        "id":"border-color",
        "label":"Border-Colour"
      },
      {
        "type": "header",
        "content": "Hover"
      },
      {
        "type":"color",
        "id":"hover-color",
        "label":"Colour"
      },
      {
        "type":"color",
        "id":"hover-background-color",
        "label":"Background Colour"
      },
      {
        "type":"color",
        "id":"hover-border-color",
        "label":"Border-Colour"
      },
      {
        "type": "header",
        "content": "Mobile"
      },
      {
        "type": "text",
        "id": "mobile-size",
        "label": "Font Size"
      },
      {
        "type": "text",
        "id": "mobile-height",
        "label": "Line Height"
      },
      {
        "type": "header",
        "content": "Tablet"
      },
      {
        "type": "text",
        "id": "tablet-size",
        "label": "Font Size"
      },
      {
        "type": "text",
        "id": "tablet-height",
        "label": "Line Height"
      },
      {
        "type": "header",
        "content": "Desktop"
      },
      {
        "type": "text",
        "id": "desktop-size",
        "label": "Font Size"
      },
      {
        "type": "text",
        "id": "desktop-height",
        "label": "Line Height"
      },
      {
        "type": "header",
        "content": "Break Point 1"
      },
      {
        "type": "text",
        "id": "point1-width",
        "label": "Screen Width",
        "info":"Must be bigger than the previous breakpoint"
      },
      {
        "type": "text",
        "id": "point1-size",
        "label": "Font Size"
      },
      {
        "type": "text",
        "id": "point1-height",
        "label": "Line Height"
      },
      {
        "type": "header",
        "content": "Break Point 2"
      },
      {
        "type": "text",
        "id": "point2-width",
        "label": "Screen Width",
        "info":"Must be bigger than the previous breakpoint"
      },
      {
        "type": "text",
        "id": "point2-size",
        "label": "Font Size"
      },
      {
        "type": "text",
        "id": "point2-height",
        "label": "Line Height"
      }
      ]
    }
    ] 
  }

  {% endschema %}