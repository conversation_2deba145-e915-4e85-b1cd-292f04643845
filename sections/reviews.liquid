{% liquid
    assign section_name = 'Reviews'
    assign el_id = section_name | handle | append: '-' | append: section.id
    assign image_id = el_id | append: '-image'

    assign section_padding = section.settings.padding
    assign padding_class = 'sp-' | append: section_padding
    
    if section.settings.no_padding_top
        assign padding_class = padding_class | append: ' spt-no'
    else
        if section.settings.top_half_padding
            assign padding_class = padding_class | append: ' spt-' | append: section_padding | append: '-half'
        endif
    endif

    if section.settings.no_padding_bottom
        assign padding_class = padding_class | append: ' spb-no'
    else
        if section.settings.bottom_half_padding
            assign padding_class = padding_class | append: ' spb-' | append: section_padding | append: '-half'
        endif
    endif
%}
<style>
    #{{ el_id }} .no-padding {
        padding: 0;
    }

    #{{ el_id }} .container {
        padding-left: 4px;
        padding-right: 4px;
    }

    #{{ el_id }} .reviews-list {
        background-color: {{ section.settings.background_color }};
    }
    #{{ el_id }} .review-text {
        display: -webkit-box;
        -webkit-line-clamp: 6;
        -webkit-box-orient: vertical;  
        overflow: hidden;
        text-overflow: ellipsis;
    }

    #{{ el_id }} .swiper-slide {
        height: unset;
    }

    @media only screen and (min-width: 1024px) {
        #{{ el_id }} .container {
            padding-left: 6px;
            padding-right: 6px;
        }
        #{{ el_id }} .btn-nav {
            position: initial;
            margin-top: 0;
        }

        #{{ image_id }} {
            padding-bottom: 0!important;
            height: 100%;
        }
        #{{ image_id }} img {
            object-fit: cover;
            height: 100%;
        }
    }
    @media only screen and (min-width: 1440px) {
        #{{ el_id }} .container {
            padding-left: 8px;
            padding-right: 8px;
        }
    }
</style>
<section id="{{ el_id }}" class="{{ padding_class }}">
    <div class="container">
        <div class="row flex flex-column d-flex-row">
            <div class="col-12 col-d-4 col-hd-5 no-padding">
                <div class="reviews-header h-full{% if section.settings.banner_image_m == blank %} bg-grey{% endif %}">
                    <{{ section.settings.title_element }} class="{{ section.settings.title_class }} title c-white zi-1 tc absolute ab-middle">
                        {{ section.settings.title }}
                    </{{ section.settings.title_element }}>
                    {% if section.settings.banner_image_d %}
                        {% render 'global-image-wrapper-responsive',
                            image_id: image_id,
                            mobile_image: section.settings.banner_image_m,
                            desktop_image: section.settings.banner_image_d,
                            image_alt: section.settings.title | escape,
                            image_change_breakpoint: 'desktop'
                        %}
                    {% else %}
                        {{ 'image' | placeholder_svg_tag }}
                    {% endif %}
                </div>
            </div>
            <div class="col-12 col-d-8 col-hd-7 no-padding">
                <div class="reviews-list pt-48 pb-48 d-pt-80 hd-pt-120 hhd-pt-148">
                    <div class="swiper">
                        <div class="swiper-wrapper">
                            {% for block in section.blocks %}
                                <div class="swiper-slide">
                                    <div class="review-item h-full bg-white p-24 d-p-40 rounded-8">
                                        <div class="review-rating mb-8">
                                            {% for i in (1..5) %}
                                                <span class="star{% if i > block.settings.rate %} opacity-3{% endif %}">
                                                    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M5.32046 1.92782C5.56485 1.31247 6.43581 1.31247 6.68019 1.92782L7.75528 4.63483C7.82964 4.82206 7.97791 4.97033 8.16514 5.04469L10.8721 6.11978C11.4875 6.36416 11.4875 7.23512 10.8721 7.47951L8.16514 8.5546C7.97791 8.62895 7.82964 8.77723 7.75528 8.96445L6.68019 11.6715C6.43581 12.2868 5.56485 12.2868 5.32046 11.6715L4.24537 8.96445C4.17102 8.77723 4.02274 8.62895 3.83551 8.5546L1.12851 7.47951C0.513155 7.23512 0.513154 6.36416 1.1285 6.11978L3.83551 5.04469C4.02274 4.97033 4.17102 4.82206 4.24537 4.63483L5.32046 1.92782Z" fill="#F7D170"/>
                                                    </svg>
                                                </span>
                                            {% endfor %}
                                        </div>
                                        <p class="review-author h7 mb-16">{{ block.settings.author }}</p>
                                        <p class="review-text p2">{{ block.settings.text | prepend: '&ldquo;' | append: '&rdquo;' }}</p>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="swiper-navigation pl-60 mt-24 hide-m show-d">
                            <div class="flex colgap-8">
                                <div class="btn-nav swiper-button-prev">{{ settings.icon_arrow_left }}</div>
                                <div class="btn-nav swiper-button-next">{{ settings.icon_arrow_right }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<script>
    function initSwiper_{{ section.id | remove: '-' }}() {
        new Swiper(`#{{ el_id }} .swiper`, {
            allowTouchMove: true,
            centeredSlides: true,
            loop: true,
            slidesPerView: 1.25,
            spaceBetween: 8,
            navigation: {
                prevEl: `#{{ el_id }} .swiper-button-prev`,
                nextEl: `#{{ el_id }} .swiper-button-next`,
            },
            breakpoints: {
                600: {
                    slidesPerView: 1.25,
                    spaceBetween: 8,
                },
                1024: {
                    centeredSlides: false,
                    loop: false,
                    slidesPerView: 2.5,
                    spaceBetween: 16,
                    slidesOffsetBefore: 60,
                    slidesOffsetAfter: 60,
                }
            },
            on: {
                init: function(swiper) {
                    $360.lazyLoadInstance.update();
                },
                slideChange: function(swiper) {
                    $360.lazyLoadInstance.update();
                },
                resize: function(swiper) {
                    $360.lazyLoadInstance.update();
                }
            }
        });
    }
    document.addEventListener('DOMContentLoaded', () => {
        initSwiper_{{ section.id | remove: '-' }}();
    });
    document.addEventListener('shopify:section:load', () => {
        initSwiper_{{ section.id | remove: '-' }}();
    });
</script>
{% schema %}
    {
        "name": "Reviews",
        "blocks": [
            {
                "type": "review",
                "name": "Review",
                "settings": [
                    {
                        "type": "range",
                        "id": "rate",
                        "label": "Rate star",
                        "min": 0,
                        "max": 5,
                        "step": 1,
                        "default": 5
                    },
                    {
                        "type": "text",
                        "id": "author",
                        "label": "Review author"
                    },
                    {
                        "type": "textarea",
                        "id": "text",
                        "label": "Review text"
                    }
                ]
            }
        ],
        "settings": [
            {
                "type": "header",
                "content": "General Settings"
            },
            {
                "type": "image_picker",
                "id": "banner_image_m",
                "label": "Banner Image",
                "info": "Mobile"
            },
            {
                "type": "image_picker",
                "id": "banner_image_d",
                "label": "Banner Image",
                "info": "Desktop"
            },
            {
                "type": "text",
                "id": "title",
                "label": "Title",
                "default": "Customer Reviews"
            },
            {
                "type": "select",
                "id": "title_element",
                "label": "Title element",
                "options": [
                    {"value": "h1", "label": "H1"},
                    {"value": "h2", "label": "H2"},
                    {"value": "h3", "label": "H3"},
                    {"value": "h4", "label": "H4"}
                ],
                "default": "h2"
            },
            {
                "type": "select",
                "id": "title_class",
                "label": "Title class",
                "options": [
                    {"value": "h1", "label": "H1"},
                    {"value": "h2", "label": "H2"},
                    {"value": "h3", "label": "H3"},
                    {"value": "h4", "label": "H4"}
                ],
                "default": "h2"
            },
            {
                "type": "color",
                "id": "background_color",
                "label": "Reviews background color",
                "default": "#93C9FF"
            },
            {
                "type": "header",
                "content": "Section Padding"
            },
            {
                "type": "select",
                "id": "padding",
                "label": "Padding",
                "options": [
                    {
                        "value": "no",
                        "label": "No"
                    },
                    {
                        "value": "xs",
                        "label": "Extra Small"
                    },
                    {
                        "value": "sm",
                        "label": "Small"
                    },
                    {
                        "value": "md",
                        "label": "Med"
                    },
                    {
                        "value": "lg",
                        "label": "Large"
                    }
                ],
                "default": "md"
            },
            {
                "type": "checkbox",
                "id": "top_half_padding",
                "label": "Top half padding",
                "default": false
            },
            {
                "type": "checkbox",
                "id": "no_padding_top",
                "label": "No padding top",
                "default": false
            },
            {
                "type": "checkbox",
                "id": "bottom_half_padding",
                "label": "Bottom half padding",
                "default": false
            },
            {
                "type": "checkbox",
                "id": "no_padding_bottom",
                "label": "No padding bottom",
                "default": false
            }
        ],
        "presets": [
            {
                "name": "Reviews",
                "category": "Custom",
                "blocks": [
                    {
                        "type": "review",
                        "settings": {
                            "rate": 5,
                            "author": "Sample Author Text",
                            "text": "Finally, jewellery that actually lasts! I wear my Kaleido necklace daily, even in the shower, and it still looks brand new. It's fantastic to find stylish pieces that are also so durable and don't cost a fortune."
                        }
                    },
                    {
                        "type": "review",
                        "settings": {
                            "rate": 5,
                            "author": "Sample Author Text",
                            "text": "Finally, jewellery that actually lasts! I wear my Kaleido necklace daily, even in the shower, and it still looks brand new. It's fantastic to find stylish pieces that are also so durable and don't cost a fortune."
                        }
                    },
                    {
                        "type": "review",
                        "settings": {
                            "rate": 5,
                            "author": "Sample Author Text",
                            "text": "Finally, jewellery that actually lasts! I wear my Kaleido necklace daily, even in the shower, and it still looks brand new. It's fantastic to find stylish pieces that are also so durable and don't cost a fortune."
                        }
                    },
                    {
                        "type": "review",
                        "settings": {
                            "rate": 5,
                            "author": "Sample Author Text",
                            "text": "Finally, jewellery that actually lasts! I wear my Kaleido necklace daily, even in the shower, and it still looks brand new. It's fantastic to find stylish pieces that are also so durable and don't cost a fortune."
                        }
                    }
                ],
                "settings": {
                    "title": "Sample Title",
                    "title_class": "h3",
                    "title_element": "h2",
                    "padding": "no"
                }
            }
        ]
    }
{% endschema %}