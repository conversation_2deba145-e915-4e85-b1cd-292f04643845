{%- liquid
    assign section_name = 'Order Detail'
    assign el_id = section_name | handle | append: '-' | append: section.id
-%}
<style>
    #{{ el_id }} .label {
        width: 140px;
    }
    #{{ el_id }} .only-d {
        display: none;
    }
    .v-middle {
        vertical-align: middle;
    }

    .order-detail-title svg {
        width: 14px;
        height: 14px;
    }

    .order-details .item .image-con {
        border-radius: 6px;
        overflow: hidden;
        width: 75px;
        min-width: 75px;
    }

    .order-detail-box {
        width: 100%;
    }
    
    @media only screen and (min-width: 600px) {
        table .shipping-address,
        table .billing-address {
            width: 50%;
        }
    }

    @media screen and (min-width:1024px){
        #{{ el_id }} .only-d {
            display: table-cell;
        }
        .order-details .item .image-con {
            width: 96px;
            min-width: 96px;
        }
        .order-detail-title svg {
            width: 16px;
            height: 16px;
        }
    }
</style>

<section id="{{ el_id }}" class="order-detail pt-32 d-pt-80 d-pb-80">
    <div class="container">
        <div class="row flex flex-column d-flex-row">
            <div class="col-d-1"></div>
            <div class="col-12 col-d-2">
                {%- render 'account-links' -%}
            </div>
            {%- render 'account-menu-dropdown' -%}
            <div class="col-d-1"></div>
            <div class="col-12 col-d-7">
                <div class="order-wrapper bg-white rounded-tl-16 rounded-tr-16 d-rounded-16 pt-32 pb-32 pl-20 pr-20 d-pl-32 d-pr-32">
                    <div class="order-detail-title mb-32 d-mb-60">
                        <a href="{{ routes.account_url }}" class="flex ai-center colgap-4 mb-24">
                            {{ settings.icon_arrow_left }}
                            <span class="p3 hide-d" style="margin-top: 1px;">{{ 'customer.order.back' | t }}</span>
                            <span class="p2 hide-m show-d" style="margin-top: 1px;">{{ 'customer.order.back' | t }}</span>
                        </a>
                        <h2 class="h3">Order {{ order.name }}</h2>
                    </div>
                    <div class="order-detail-box">
                        {% comment %} DATE AND STATUS {% endcomment %}
                        <div class="date-status pb-24 bb-1 b-brown-20">
                            <div class="flex colgap-16 mb-12">
                                <p class="label p2 bold-600">{{ 'customer.order.date' | t }}</p>
                                <p class="p2">{{ order.created_at | date: '%d %b %Y' }}</p>
                            </div>
                            <div class="flex colgap-16 mb-12">
                                <p class="label p2 bold-600">{{ 'customer.orders.amount' | t }}</p>
                                <p class="p2">{{ order.total_price | money }}</p>
                            </div>
                            <div class="flex colgap-16">
                                <p class="label p2 bold-600">{{ 'customer.orders.status' | t }}</p>
                                <p class="p2">{{ order.fulfillment_status_label }}</p>
                            </div>
                        </div>

                        {% # delivery address & billing address %}
                        <div class="delivery-billing pt-24 pb-24 bb-1 b-brown-20">
                            <div class="flex colgap-16 mb-12">
                                <p class="label p2 bold-600">{{ 'customer.order.shipping_address' | t }}</p>
                                <p class="p2">
                                    {{- order.shipping_address.name -}}
                                    <br>
                                    {% if order.shipping_address.address1 != blank -%}
                                        {{- order.shipping_address.address1 -}}
                                        <br>
                                    {% endif %}
                                    {% if order.shipping_address.address2 != blank -%}
                                        {{- order.shipping_address.address2 -}}
                                        <br>
                                    {% endif %}
                                    {%- if order.shipping_address.city != blank -%}
                                        {{ order.shipping_address.city }}
                                        <br>
                                    {%- endif -%}
                                    {%- if order.shipping_address.province != blank -%}
                                        {{- order.shipping_address.province -}}
                                        <br>
                                    {% endif %}
                                    {{ order.shipping_address.country }} {{ order.shipping_address.zip -}}
                                </p>
                            </div>
                            <div class="flex colgap-16">
                                <p class="label p2 bold-600">{{ 'customer.order.billing_address' | t }}</p>
                                <p class="p2">
                                    {{- order.billing_address.name -}}
                                    <br>
                                    {% if order.billing_address.address1 != blank -%}
                                        {{- order.billing_address.address1 -}}
                                        <br>
                                    {% endif %}
                                    {% if order.billing_address.address2 != blank -%}
                                        {{- order.billing_address.address2 -}}
                                        <br>
                                    {% endif %}
                                    {%- if order.billing_address.city != blank -%}
                                        {{ order.billing_address.city }}
                                        <br>
                                    {%- endif -%}
                                    {%- if order.billing_address.province != blank -%}
                                        {{- order.billing_address.province -}}
                                        <br>
                                    {% endif %}
                                    {{ order.billing_address.country }} {{ order.billing_address.zip -}}
                                </p>
                            </div>
                        </div>

                        {% # order items %}
                        <div class="order-items pt-24">
                            <table class="order-details w-full">
                                <colgroup>
                                    <col span="1" width="70%">
                                    <col span="1" width="auto">
                                    <col span="1" width="auto">
                                </colgroup>
                                <thead>
                                    <tr class="pb-12">
                                        <td class="p2 bold-600">{{ 'customer.order.items' | t }}</td>
                                        <td class="tr p2 bold-600">
                                            <span class="hide-hd">Qty</span>
                                            <span class="hide-m show-hd">{{ 'customer.order.quantity' | t }}</span>
                                        </td>
                                        <td class="p2 tr bold-600">
                                            <span class="hide-d">Amt</span>
                                            <span class="hide-m show-d">{{ 'customer.orders.amount' | t }}</span>
                                        </td>
                                    </tr>
                                </thead>
                                <tbody>
                                {%- for line_item in order.line_items -%}
                                    <tr class="item{% if forloop.last %} bb-1 b-brown-20{% endif %}">
                                        <td class="pt-12{% if forloop.last %} pb-24{% endif %}">
                                            <div class="flex ai-center jc-start pr-16">
                                                <div class="image-con mr-16 bg-background">
                                                    {%- render 'global-image-wrapper',
                                                        image: line_item.image,
                                                        image_alt: line_item.title | escape
                                                    -%}
                                                </div>
                                                <div class="text-con">
                                                {%- if line_item.product.tags contains "hidden" -%}
                                                    <p class="p2 mb-8">{{ line_item.title }}</p>
                                                {%- else -%}
                                                    <p class="p2 mb-8">{{ line_item.title }}</p>
                                                    <p class="p2 c-dark-grey">
                                                        {% for opt in line_item.options_with_values %}
                                                            {%- if opt.value != 'Default Title' -%}
                                                                {%- if opt.name == 'Denominations' or opt.name contains 'Denominations' -%}
                                                                    {{ opt.value | remove_first: '.00' }}{% unless forloop.last %}/{% endunless %}    
                                                                {%- else -%}
                                                                    {{ opt.value }}{% unless forloop.last %}/{% endunless %}
                                                                {%- endif -%}
                                                            {% endif %}
                                                        {% endfor %}
                                                    </p>
                                                    {%- if line_item.selling_plan_allocation -%}
                                                        <p class="p2 opacity-5 mb-5">{{ line_item.selling_plan_allocation.selling_plan.name }}</p>
                                                    {%- endif -%}
                                                    {% if line_item.properties.size > 0 %}
                                                        <p class="p2 c-dark-grey">
                                                            {% for property in line_item.properties %}
                                                                {{ property.last }}{% unless forloop.last %}/{% endunless %}
                                                            {% endfor %}
                                                        </p>
                                                    {% endif %}
                                                {%- endif -%}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="v-middle tr pt-12{% if forloop.last %} pb-24{% endif %}">
                                            <p class="p2">{{ line_item.quantity }}</p>
                                        </td>
                                        <td class="v-middle pt-12{% if forloop.last %} pb-24{% endif %} ">
                                            <div class="final-price tr flex flex-column d-row-reverse jc-start p2">
                                                {%- if line_item.final_line_price < line_item.original_line_price -%}
                                                    <span class="{% if line_item.final_line_price < line_item.original_line_price %}t-ml-4{% endif %}">{{ line_item.final_line_price | money }}</span>
                                                {%- endif -%}
                                                <span class="{% if line_item.final_line_price < line_item.original_line_price %}striked opacity-4{% endif %}">
                                                    {{ line_item.original_line_price | money }}
                                                </span>
                                                <span class="striked opacity-4 d-mr-4">
                                                    {{ line_item.original_line_price | money }}
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                {%- endfor -%}
                                <tr class="item">
                                    <td colspan="3" class="pt-24">
                                        <div class="flex d-colgap-100 jc-between d-jc-end">
                                            <div>
                                                <div class="relative tl p2 bold-700 pb-12">{{ 'customer.order.subtotal' | t }}</div>
                                            
                                                {%- for shipping_method in order.shipping_methods -%}
                                                    <div class="relative tl p2 pb-12">{{ shipping_method.title }}</div>
                                                {%- endfor -%}
                                                
                                                {%- if order.cart_level_discount_applications != blank or order.discount_applications -%}
                                                    {%- for discount in order.cart_level_discount_applications -%}
                                                    <div class="relative tl p2 pb-12">{{ discount.title }}</div>
                                                    {%- endfor -%}
                                                    {%- for discount in order.discount_applications -%}
                                                        <div class="relative tl p2 pb-12">{{ discount.title }}</div>
                                                    {%- endfor -%}
                                                {%- endif -%}

                                                {%- if order.tax_lines != blank -%}
                                                    {%- for tax in order.tax_lines -%}
                                                    <div class="relative tl p2 pb-12">
                                                        {{- tax.title }}
                                                        {{ tax.rate | times: 100 }}%
                                                    </div>
                                                    {%- endfor -%}
                                                {%- endif -%}

                                                <div class="relative tl p2 bold-700">{{ 'customer.order.total' | t }}</div>
                                            </div>
                                            <div>
                                                <div class="relative tr p2 pb-12">{{ order.line_items_subtotal_price | money }}</div>
                                                
                                                {%- for shipping_method in order.shipping_methods -%}
                                                    {% if shipping_method.price > 0 %}
                                                    <div class="relative tr p2 pb-12">{{ shipping_method.price | money }}</div>
                                                    {% else %}
                                                    <div class="relative tr p2 pb-12">Free</div>
                                                    {% endif %}
                                                {%- endfor -%}
                                                
                                                {%- if order.cart_level_discount_applications != blank or order.discount_applications -%}
                                                    {%- for discount in order.cart_level_discount_applications -%}
                                                    <div class="relative tr p2 pb-12">
                                                        {{ discount.total_allocated_amount | money }}
                                                    </div>
                                                    {%- endfor -%}
                                                    {%- for discount in order.discount_applications -%}
                                                        <div class="relative tr p2 pb-12">
                                                            {{ discount.total_allocated_amount | money }}
                                                        </div>
                                                        {%- endfor -%}
                                                {%- endif -%}

                                                {%- if order.tax_lines != blank -%}
                                                    {%- for tax in order.tax_lines -%}
                                                    <div class="relative tr p2 pb-12">{{ tax.price | money }}</div>
                                                    {%- endfor -%}
                                                {%- endif -%}
                                                
                                                <div class="relative tr p2 bold-700">{{ order.total_price | money }}</div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% schema %}
    {
        "name": "Account Order",
        "settings": [],
        "presets": [{ "name": "Account Order" }]
    }
{% endschema %}