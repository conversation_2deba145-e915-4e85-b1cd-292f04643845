{% liquid
    assign section_name = 'Grid Gallery'
    assign el_id = section_name | handle | append: '-' | append: section.id

    assign section_padding = section.settings.padding
    assign padding_class = 'sp-' | append: section_padding
    
    if section.settings.no_padding_top
        assign padding_class = padding_class | append: ' spt-no'
    else
        if section.settings.top_half_padding
            assign padding_class = padding_class | append: ' spt-' | append: section_padding | append: '-half'
        endif
    endif

    if section.settings.no_padding_bottom
        assign padding_class = padding_class | append: ' spb-no'
    else
        if section.settings.bottom_half_padding
            assign padding_class = padding_class | append: ' spb-' | append: section_padding | append: '-half'
        endif
    endif
%}
<style>
    #{{ el_id }} .grid-gallery {
        display: grid;
        grid-template-columns: repeat(2, auto);
        gap: 8px;
    }
    #{{ el_id }} .grid-gallery__big {
        grid-column: span 2;
    }
    #{{ el_id }} .grid-gallery__big.left {
        grid-row: 1 / 3;
    }
    #{{ el_id }} .grid-gallery__big.center {
        grid-row: 2 / 4;
    }
    #{{ el_id }} .grid-gallery__big.right {
        grid-row: 3 / 5;
    }

    #{{ el_id }} .w-min-cont {
        width: min-content;
    }

    #{{ el_id }} .global-image-wrapper .overlay {
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 80%, rgba(0, 0, 0, 0.5) 100%);
    }
    @media only screen and (min-width: 1024px) {
        #{{ el_id }} .grid-gallery {
            grid-template-columns: repeat(4, auto);
            grid-template-rows: repeat(2, auto);
            gap: 16px;
        }
        #{{ el_id }} .grid-gallery__big.left,
        #{{ el_id }} .grid-gallery__big.center,
        #{{ el_id }} .grid-gallery__big.right {
            grid-row: 1 / 3;
        }
        #{{ el_id }} .grid-gallery__big.left {
            grid-column: 1 / 3;
        }
        #{{ el_id }} .grid-gallery__big.center {
            grid-column: 2 / 4;
        }
        #{{ el_id }} .grid-gallery__big.right {
            grid-column: 3 / 5;
        }
    }
</style>
<section id="{{ el_id }}" class="{{ padding_class }}">
    <div class="container">
        <div class="grid-title mb-20 d-mb-40 flex flex-column d-flex-row ai-center d-jc-between">
            <{{ section.settings.title_element }} class="{{ section.settings.title_class }} tc mb-16 d-mb-0">
                {{ section.settings.title }}
            </{{ section.settings.title_element }}>
            {%- if section.settings.link_label != blank -%}
                <a href="{{ section.settings.link_url }}" class="btn1 w-min-cont" style="background-color:{{ section.settings.link_bg_color }};">
                    {{ section.settings.link_label }}
                </a>
            {%- endif -%}
        </div>
        <div class="grid-gallery">
            {%- for block in section.blocks -%}
                {%- assign image_alt = section.settings.title | append: ' ' | append: forloop.index -%}
                {% if block.type == 'big_gallery' %}
                    {%- if block.settings.link_url -%}
                    <a href="{{ block.settings.link_url }}" class="block grid-gallery__big {{ block.settings.position }}">
                    {%- else -%}
                    <div class="grid-gallery__big {{ block.settings.position }}">
                    {%- endif -%}
                        <div class="gallery-wrapper relative rounded-8">
                            <div class="gallery-image{% if block.settings.image == blank %} bg-grey{% endif %}">
                                {% if block.settings.image %}
                                    {% render 'global-image-wrapper',
                                        image: block.settings.image,
                                        image_alt: image_alt | escape,
                                        overlay: true
                                    %}
                                {% else %}
                                    {{ 'image' | placeholder_svg_tag }}
                                {% endif %}
                            </div>
                            {%- if block.settings.link_label -%}
                            <div class="hide-m show-d absolute left-28 bottom-28 zi-1">
                                <span class="link4 c-white">{{ block.settings.link_label }}</span>
                            </div>
                            {%- endif -%}
                        </div>
                    {%- if block.settings.link_url -%}
                    </a>
                    {%- else -%}
                    </div>
                    {%- endif -%}
                {% elsif block.type == 'small_gallery' %}
                    {%- if block.settings.link_url -%}
                    <a href="{{ block.settings.link_url }}" class="block grid-gallery__small">
                    {%- else -%}
                    <div class="grid-gallery__small">
                    {%- endif -%}
                        <div class="gallery-wrapper relative rounded-8">
                            <div class="gallery-image{% if block.settings.image == blank %} bg-grey{% endif %}">
                                {% if block.settings.image %}
                                    {% render 'global-image-wrapper',
                                        image: block.settings.image,
                                        image_alt: image_alt | escape,
                                        overlay: true
                                    %}
                                {% else %}
                                    {{ 'image' | placeholder_svg_tag }}
                                {% endif %}
                            </div>
                            {%- if block.settings.link_label -%}
                            <div class="hide-m show-d absolute left-28 bottom-28 zi-1">
                                <span class="link4 c-white">{{ block.settings.link_label }}</span>
                            </div>
                            {%- endif -%}
                        </div>
                    {%- if block.settings.link_url -%}
                    </a>
                    {%- else -%}
                    </div>
                    {%- endif -%}
                {% endif %}
            {%- endfor -%}
        </div>
    </div>
</section>
{% schema %}
    {
        "name": "Grid Gallery",
        "blocks": [
            {
                "type": "big_gallery",
                "name": "Big Gallery",
                "settings": [
                    {
                        "type": "image_picker",
                        "id": "image",
                        "label": "Image"
                    },
                    {
                        "type": "select",
                        "id": "position",
                        "label": "Position",
                        "options": [
                            {
                                "value": "left",
                                "label": "Left"
                            },
                            {
                                "value": "center",
                                "label": "Center"
                            },
                            {
                                "value": "right",
                                "label": "Right"
                            }
                        ],
                        "default": "left",
                        "info": "Left as top and right as bottom in Mobile view"
                    },
                    {
                        "type": "text",
                        "id": "link_label",
                        "label": "Link Label"
                    },
                    {
                        "type": "url",
                        "id": "link_url",
                        "label": "Link URL"
                    }
                ],
                "limit": 1
            },
            {
                "type": "small_gallery",
                "name": "Small Gallery",
                "settings": [
                    {
                        "type": "image_picker",
                        "id": "image",
                        "label": "Image"
                    },
                    {
                        "type": "text",
                        "id": "link_label",
                        "label": "Link Label"
                    },
                    {
                        "type": "url",
                        "id": "link_url",
                        "label": "Link URL"
                    }
                ],
                "limit": 4
            }
        ],
        "settings": [
            {
                "type": "header",
                "content": "General settings"
            },
            {
                "type": "text",
                "id": "title",
                "label": "Title"
            },
            {
                "type": "select",
                "id": "title_element",
                "label": "Title element",
                "options": [
                    {"value": "h1", "label": "H1"},
                    {"value": "h2", "label": "H2"},
                    {"value": "h3", "label": "H3"},
                    {"value": "h4", "label": "H4"}
                ],
                "default": "h2"
            },
            {
                "type": "select",
                "id": "title_class",
                "label": "Title class",
                "options": [
                    {"value": "h1", "label": "H1"},
                    {"value": "h2", "label": "H2"},
                    {"value": "h3", "label": "H3"},
                    {"value": "h4", "label": "H4"}
                ],
                "default": "h2"
            },
            {
                "type": "text",
                "id": "link_label",
                "label": "Link Label"
            },
            {
                "type": "color",
                "id": "link_bg_color",
                "label": "Link background color"
            },
            {
                "type": "url",
                "id": "link_url",
                "label": "Link URL"
            },
            {
                "type": "header",
                "content": "Section Padding"
            },
            {
                "type": "select",
                "id": "padding",
                "label": "Padding",
                "options": [
                    {
                        "value": "no",
                        "label": "No"
                    },
                    {
                        "value": "xs",
                        "label": "Extra Small"
                    },
                    {
                        "value": "sm",
                        "label": "Small"
                    },
                    {
                        "value": "md",
                        "label": "Med"
                    },
                    {
                        "value": "lg",
                        "label": "Large"
                    }
                ],
                "default": "md"
            },
            {
                "type": "checkbox",
                "id": "top_half_padding",
                "label": "Top half padding",
                "default": false
            },
            {
                "type": "checkbox",
                "id": "no_padding_top",
                "label": "No padding top",
                "default": false
            },
            {
                "type": "checkbox",
                "id": "bottom_half_padding",
                "label": "Bottom half padding",
                "default": false
            },
            {
                "type": "checkbox",
                "id": "no_padding_bottom",
                "label": "No padding bottom",
                "default": false
            }
        ],
        "presets": [
            {
                "name": "Grid Gallery",
                "category": "Custom",
                "blocks": [
                    {
                        "type": "big_gallery",
                        "settings": {
                            "position": "left",
                            "link_label": "Shop the Look",
                            "link_url": "#"
                        }
                    },
                    {
                        "type": "small_gallery",
                        "settings": {
                            "link_label": "Shop the Look",
                            "link_url": "#"
                        }
                    },
                    {
                        "type": "small_gallery",
                        "settings": {
                            "link_label": "Shop the Look",
                            "link_url": "#"
                        }
                    },
                    {
                        "type": "small_gallery",
                        "settings": {
                            "link_label": "Shop the Look",
                            "link_url": "#"
                        }
                    },
                    {
                        "type": "small_gallery",
                        "settings": {
                            "link_label": "Shop the Look",
                            "link_url": "#"
                        }
                    }
                ],
                "settings": {
                    "title": "Grid Gallery Sample Title",
                    "link_label": "@kaleidojewellery"
                }
            }
        ]
    }
{% endschema %}