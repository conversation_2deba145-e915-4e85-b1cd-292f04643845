{% liquid
  
  assign section_name = "Collection & Search"
  assign el_id = section_name | handle | append: "-" | append: section.id

  assign section_padding = section.settings.padding
  assign padding_class = "sp-" | append: section_padding

  if section.settings.no_padding_top
    assign padding_class = padding_class | append: " spt-no"
  else
    if section.settings.top_half_padding
      assign padding_class = padding_class | append: " spt-" | append: section_padding | append: "-half"
    endif
  endif

  if section.settings.no_padding_bottom
    assign padding_class = padding_class | append: " spb-no"
  else
    if section.settings.bottom_half_padding
      assign padding_class = padding_class | append: " spb-" | append: section_padding | append: "-half"
    endif
  endif

%}

{{ "collection-and-search.css" | asset_url | stylesheet_tag }}

{% capture sort_values %}
  {{ "collections.sorting.best_selling" | t }}|
  {{ "collections.sorting.date_descending" | t }}|
  {{ "collections.sorting.price_ascending" | t }}|
  {{ "collections.sorting.price_descending" | t }}
{% endcapture %}

{%- if request.page_type == "collection" -%}

  {%- assign products_count = collection.products_count -%}
  {%- assign bipl = collection.metafields.product_list_banner -%}
  {%- assign item_per_page = settings.collection_products_number -%}
  
  {%- if bipl.image_desktop != blank -%}
    {%- assign item_per_page = settings.collection_products_number | plus: 2 -%}
  {%- endif  -%}
  
  {% comment %} Exclude filters {% endcomment %}
  {%- assign shop_exclude_filters = shop.metafields.collection_filter.exclude -%}
  {%- assign collection_exclude_filters = collection.metafields.collection_filter.exclude -%}
  {%- assign exclude_filters = shop_exclude_filters -%}
  {%- if collection_exclude_filters != blank -%}
    {% capture exclude_filters %}{{ exclude_filters }}|{{ collection_exclude_filters }}{% endcapture %}
  {%- endif -%}
  {% comment %} End Exclude filters {% endcomment %}
  
  {% comment %} Rename filters {% endcomment %}
  {%- assign rename_filters = collection.metafields.collection_filter.rename | split: "|" -%}
  {% comment %} End Rename filters {% endcomment %}
  
  {%- assign filters = collection.filters -%}

{%- else -%}
  
  {%- assign products_count = search.results_count -%}
  {%- assign item_per_page = settings.search_products_number -%}
  
  {% comment %} Exclude filters {% endcomment %}
  {%- assign exclude_filters = shop.metafields.collection_filter.exclude -%}
  {% comment %} End Exclude filters {% endcomment %}
  
  {%- assign filters = search.filters -%}
{%- endif -%}

{% liquid
  
  assign sort_values = sort_values | split: "|"
  assign sort_option_values = "best-selling,created-descending,price-ascending,price-descending" | split: ","
  assign sort_label = "collections.sorting.title" | t | append: ":"
  assign default_text = "collections.sorting.best_selling" | t

  assign has_toolbar = true
  if section.settings.hide_filter and section.settings.hide_sort
    assign has_toolbar = false
  endif

  assign section_class = padding_class
  if products_count <= item_per_page
    assign section_class = section_class | append: " no-pagination"
    endif
  if settings.pagination_type == "show_more" or settings.hide_pagination
    assign section_class = section_class | append: " hide-normal-pagination"
  endif

  assign filter_sort_text = "collections.general.filter_and_sort" | t
  if section.settings.hide_filter
    assign filter_sort_text = "collections.sorting.sort" | t
  endif
  if section.settings.hide_sort
    assign filter_sort_text = "collections.filters.filter" | t
  endif
%}

{% capture overlay %}
  <div @click="toggleDrawer" class="cns__overlay zi-7"></div>
{% endcapture %}

{% capture filter_drawer %}
  <div class="cns__drawer" :data-show="filterVisible">
    {{ overlay }}
    <div class="cns__drawer-inner zi-8 flex flex-column">
      <div class="cns__drawer-header">
        <div class="container pt-16 pb-16 d-pl-16 d-pr-16 b-border bb-1">
          <div class="flex ai-center jc-between">
            <h6 class="h6">
              {{ "collections.general.filter_and_sort" | t }}
            </h6>
            <div class="flex ai-center colgap-8 d-colgap-12">
              {% unless section.settings.hide_filtered_list %}
                <button
                  class="p5 uppercase c-grey"
                  v-if="filtersObj.length > 0"
                  @click="clearFilter"
                >
                  {{ "collections.filters.clear_group" | t }}
                </button>
              {% endunless %}
              <button
                class="cns__close-btn"
                @click="toggleDrawer"
              >
                {{ settings.icon_close }}
              </button>
            </div>
          </div>
          {% unless section.settings.hide_filtered_list %}
            <div class="cns__filtered mt-8 flex flex-wrap colgap-8 rowgap-8" v-if="filtersObj.length" v-cloak>
              {% assign filtered_btn_classes = "rounded-4 pt-4 pb-4 pr-8 pl-8 bg-background flex colgap-4 ai-center" %}
              <template v-for="(filtered, i) in filtersObj" :key="i">
                <button
                  v-if="filtered.key != 'filter.v.price.gte' && filtered.key != 'filter.v.price.lte'"
                  @click="removeFilter(i)"
                  class="{{ filtered_btn_classes }}"
                >
                  <span class="p4 c-black">
                    ${filterValueLabelMap[filtered.key][filtered.value] || filtered.value}
                  </span>
                  <i class="flex ai-center">{{ settings.icon_close }}</i>
                </button>
              </template>
              <button
                v-if="filterGroup.priceMin || filterGroup.priceMax"
                @click="removeFilterPrice"
                class="{{ filtered_btn_classes }}"
              >
                <span class="p4 c-black">
                  ${formatMoney((filterGroup.priceMin || 0) * 100)} - ${formatMoney((filterGroup.priceMax || maxPriceRange) * 100)}
                </span>
                <i class="flex ai-center">{{ settings.icon_close }}</i>
              </button>
            </div>
          {% endunless %}
        </div>
      </div>
      <div class="cns__drawer-main">
        <div class="container pt-20 d-pl-20 d-pr-20">
          <div class="flex flex-column rowgap-12">
            {% unless section.settings.hide_sort %}
              {% liquid
                assign title = "collections.sorting.title" | t
              %}
              {% capture desc_html %}
                <ul class="flex flex-column rowgap-8 pt-12">
                  {%- for sort_value in sort_values -%}
                    <li>
                      <label class="global-radio" data-text="{{ sort_value | escape }}">
                        <input
                          type="radio"
                          name="sort"
                          value="{{ sort_option_values[forloop.index0] }}"
                          v-model="sortBy"
                          @change="applyFilter"
                        />
                        <span class="checkmark"></span>
                        <p class="p2">{{ sort_value }}</p>
                      </label>
                    </li>
                  {%- endfor -%}
                </ul>
              {% endcapture %}
              {% render "accordion",
                title: title,
                desc_html: desc_html,
                title_class: "p2 bold-500",
                title_padding_class: "pt-0 pb-0",
                wrapper_class: "show-m hide-d",
                unlink_expand: true
              %}
            {% endunless %}
            <div>
              {% for filter in filters %}
                {% liquid
                  assign max_price_range = 0
                  unless filter.range_max == blank
                    assign max_price_range = filter.range_max | money_without_currency | replace: ",", ""
                  endunless
                  assign param_name = filter.values[0].param_name
                  assign cb_identifier = "cns-filter-" | append: filter.label | handleize
                %}
                {% capture desc_html %}
                  {% case filter.type %}
                    {% when "boolean", "list" -%}
                      <ul class="flex flex-column rowgap-8">
                        {% for filter_value in filter.values %}
                          {% if param_name == "filter.v.availability" and filter_value.value == "0" %}
                            {% continue %}
                          {% endif %}
                          <li>
                            {% liquid
                              assign label_class = "global-checkbox"
                              if param_name == "filter.v.availability"
                                assign label_class = "global-toggle"
                              endif
                            %}
                            <label class="{{ label_class }}">
                              <input 
                                type="checkbox" 
                                id="{{ cb_identifier }}-{{ forloop.index0 }}" 
                                class="{{ cb_identifier }}"
                                name="{{ param_name }}" 
                                value="{{ param_name }}={{ filter_value.value }}" 
                                v-model="filters" 
                              />
                              <span class="checkmark"></span>
                              <p class="p2">{{ filter_value.label }}</p>
                            </label>
                          </li>
                        {% endfor %}
                      </ul>
                    {% when "price_range" %}
                      <div class="mt-12">
                        <div class="cns__price-slider"></div>
                        <div class="flex mt-8 jc-between ai-center">
                          <span class="p2">{{ cart.currency.symbol }}${!filterPrice.min ? 0 : filterPrice.min}</span>
                          <span class="p2">{{ cart.currency.symbol }}${!filterPrice.max ? {{ max_price_range }} : filterPrice.max}</span>
                        </div>
                      </div>
                  {% endcase %}
                {% endcapture %}
                {% liquid
                  assign title_padding_class = ""
                  assign wrapper_class = "cns__accordion b-border"
                  assign desc_class = ""
                  unless forloop.first
                    assign title_padding_class = title_padding_class | append: " pt-16"
                    assign wrapper_class = wrapper_class | append: " bt-1"
                  endunless
                  unless forloop.last
                    assign title_padding_class = title_padding_class | append: " pb-12"
                    assign wrapper_class = wrapper_class | append: " pb-4"
                    assign desc_class = desc_class | append: " mb-12"
                  endunless
                  assign vue_onclick = ""
                  assign open = false
                  if param_name == "filter.v.availability"
                    assign vue_onclick = "null"
                    assign open = true
                    assign wrapper_class = wrapper_class | append: " always-open"
                  endif
                %}
                {% render "accordion",
                  title: filter.label,
                  desc_html: desc_html,
                  desc_class: desc_class,
                  wrapper_class: wrapper_class,
                  title_padding_class: title_padding_class,
                  title_class: "p2 bold-500",
                  unlink_expand: true,
                  vue_onclick: vue_onclick,
                  open: open
                %}
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
      <div class="cns__drawer-footer">
        <div class="container pt-12 pb-12 d-pl-20 d-pr-20">
          <button class="btn1 w-full" @click="toggleDrawer">
            {{ "collections.general.view_products_with_count" | t }}
          </button>
        </div>
      </div>
    </div>
  </div>
{% endcapture %}

{% capture top_bar %}
  <div class="cns__topbar flex ai-center jc-between">
    {% unless section.settings.hide_filter %}
      <div class="flex colgap-8">
        <button class="p2" @click="toggleDrawer">
          <i>{{ settings.icon_filter }}</i>
          <span>{{ "collections.filters.all" | t }}</span>
        </button>
        {% for block in section.blocks %}
          <button class="p2" v-if="isDesktop" @click="toggleDrawer(e, '{{ block.settings.text }}')">
            {{ block.settings.text }}
          </button>
        {% endfor %}
      </div>
    {% endunless %}
    <div class="flex ai-center colgap-24">
      <span class="p2">{{ "collections.general.items_count" | t }}</span>
      {% unless section.settings.hide_sort %}
        {%- render "global-select",
          id: "collection-sort-by",
          label: sort_label,
          default_value: "best-selling",
          default_text: default_text,
          selected_inline: true,
          values: sort_values,
          option_values: sort_option_values,
          global_select_class: "cns__select p-0 b-0 hide-m show-d zi-1",
          label_class: "p2 mr-4",
          selected_text_class: "p2",
          list_option_class: "p2",
          vue_model: "sortBy",
          vue_onchange: "applyFilter",
        -%}
      {% endunless %}
    </div>
  </div>
{% endcapture %}

{%- if request.page_type == "search" -%}
  <div class="pt-32 pb-32 d-pt-60 d-pb-60">
    <div class="container">
      {%- assign terms = search.terms | capitalize -%}
      <h1 class="h2 tl">{{ 'general.search.results_for' | t: terms: terms }}</h1>
    </div>
  </div>
{%- endif -%}

<div id="{{ el_id }}" class="{{ section_class }}">
  <Teleport to="body">
    {{ filter_drawer }}
  </Teleport>
  <div class="cns__top-bar container bg-white sticky sticky-below-header pt-16 pb-16 zi-1">
    {{ top_bar }}
  </div>
  <div class="container pt-20 bt-1 b-border d-pt-0 d-b-0">
    <div
      ref="collection-products"
      class="row"
      :class="[isMobile ? 'ml-0 mr-0' : '']"
    >
      {%- render "collection-and-search-products" -%}
    </div>
    {% if settings.pagination_type == "show_more" %}
      <div class="col-12">
        <div class="mt-36 d-mt-60">
          <p class="p2 tc">{{ 'collections.general.showing_text_html' | t }}</p>
        </div>
        <div class="flex jc-center mt-12 d-mt-16">
          <button
            @click="showMore"
            ref="show-more-btn"
            class="btn2"
            :class="{ 'w-full': isMobile }"
          >
            {{ 'collections.general.load_more_with_count' | t: count: item_per_page }}
          </button>
        </div>
      </div>
    {% endif %}
  </div>
</div>

{% liquid
  assign items_per_page = settings.collection_products_number
  if request.page_type == "search"
    assign items_per_page = settings.search_products_number
  endif
  assign prev_page = current_page | minus: 1
  assign prev_displayed_count = items_per_page | times: prev_page

  assign products_count = search.results_count
  assign product_displayed_count = prev_displayed_count | plus: search.results.size
  if request.page_type == "collection"
    assign products_count = collection.products_count
    assign product_displayed_count = prev_displayed_count | plus: collection.products.size
  endif
%}

<script>
  Vue.createApp({
    delimiters: ['${', '}'],
    data() {
      return {
        firstLoad: true,
        filterValueLabelMap: {
          {% for filter in filters %}
            '{{ filter.values[0].param_name }}': {
              {% for filter_value in filter.values %}
                '{{ filter_value.value }}': '{{ filter_value.label }}',
              {% endfor %}
            },
          {% endfor %}
        },
        filters: [],
        filterPrice: { min: null, max: null },
        filtersObj: [],
        filterGroup: {},
        sortBy: null,
        searchKeyword: null,
        maxPriceRange: {{ max_price_range }},
        expandedFilter: null,
        filterVisible: false,
        currentPage: {{ current_page }},
        productsDisplayedCount: {{ product_displayed_count }},
        totalProducts: {{ products_count }},
        itemsPerPage: {{ items_per_page }},
        priceSlider: null,
        isMobile: window.innerWidth < 1024,
        isDesktop: window.innerWidth >= 1024,
      }
    },
    mounted() {
      addEventListener('resize', () => {
        this.isMobile = window.innerWidth < 1024;
        this.isDesktop = window.innerWidth >= 1024;
      })
      this.init()
      this.initPriceSlider()
      setTimeout(() => {
        this.firstLoad = false;
      }, 500);
    },
    methods: {
      init() {
        let urlParams = window.location.href.split('?');
        if (urlParams.length > 1) {
          this.filters = urlParams[1].split('&').filter(f => {
            const keyVal = f.split('=');
            if (keyVal[0] == 'filter.v.price.gte') {
              this.filterPrice.min = keyVal[1];
            }
            if (keyVal[0] == 'filter.v.price.lte') {
              this.filterPrice.max = keyVal[1];
            }
            if (keyVal[0] == 'sort_by') {
              this.sortBy = keyVal[1];
              $360.setGlobalSelectDivValue('collection-sort-by', this.sortBy)
            }
            if(keyVal[0] == 'q') {
              this.searchKeyword = keyVal[1];
            }
            const acceptedParams = [
              'filter.p.tag',
              'filter.p.product_type',
              'filter.p.vendor',
              'filter.p.m',
              'filter.v.availability',
              'filter.v.option',
              'filter.v.m'
            ];
            for (let i = 0; i < acceptedParams.length; i++) {
              if (keyVal[0].includes(acceptedParams[i])) {
                return f;
              }
            }
          });
          this.filters = this.filters.map(f => decodeURI(f));
        }

        // Initial page load based on URL (if page param exists)
        const initialParams = new URLSearchParams(window.location.search);
        const initialPageNumFromUrl = parseInt(initialParams.get('page')) || 1;
        const serverRenderedPage = parseInt('{{ current_page }}') || 1;

        if (initialPageNumFromUrl !== serverRenderedPage) {
          this.currentPage = initialPageNumFromUrl;
          // Filters and sort are already parsed from URL into this.filters / this.sortBy.
          // this.currentPage is now correctly set to page from URL.
          // buildGetProductsUrl() will construct the URL with these current states.
          this.getProducts(this.buildGetProductsUrl());
        } else {
          // If page from URL (or default 1) is same as server rendered,
          // ensure this.currentPage reflects this, especially if applyFilter(false) from watcher changed it.
          this.currentPage = initialPageNumFromUrl;
        }

        $360.swiperInCard('#{{ el_id }} .cns__products');

        document.addEventListener('click', (e) => {
          const target = e.target;

          if(target.classList.contains('pagination-page')) {
              e.preventDefault();
              let pageNum = 1;
              if(target.classList.contains('arrow')) {
                  if(target.getAttribute('data-go') == 'prev') {
                      pageNum = this.currentPage - 1;
                  } else {
                      pageNum = this.currentPage + 1;
                  }
              } else {
                  pageNum = parseInt(target.getAttribute('data-page'));
              }

              this.goToPage(pageNum);
          }
        });
      },
      initPriceSlider() {
        this.priceSlider = document.querySelector('.cns__price-slider');
        if (this.priceSlider) {
          noUiSlider.create(this.priceSlider, {
            start: [0, this.maxPriceRange],
            connect: true,
            step: 0.10,
            range: {
              min: 0,
              max: this.maxPriceRange
            },
          });
          this.priceSlider.noUiSlider.on('slide', () => {
            const prices = this.priceSlider.noUiSlider.get();
            this.filterPrice.min = parseFloat(prices[0]);
            this.filterPrice.max = parseFloat(prices[1]);
          });
          this.priceSlider.noUiSlider.on('change', () => {
            this.applyFilter();
          });
          this.priceSlider.noUiSlider.set([this.filterPrice.min, this.filterPrice.max]);
        }
      },
      removeFilter(index) {
        this.filters.splice(index, 1);
        this.applyFilter();
      },
      removeFilterPrice() {
        this.filterPrice.min = null;
        this.filterPrice.max = null;
        if (this.priceSlider) {
          this.priceSlider.noUiSlider.set([0, this.maxPriceRange]);
        }
        this.applyFilter();
      },
      clearFilter() {
        this.filters = [];
        this.filterPrice.min = null;
        this.filterPrice.max = null;
        if (this.priceSlider) {
          this.priceSlider.noUiSlider.set([0, this.maxPriceRange]);
        }
      },
      formatMoney(num) {
        return $360.formatMoney(num, true);
      },
      applyFilter(loadProducts = true) {
        if(!this.firstLoad) {
          this.currentPage = 1;
        }
        let queryStrings = this.searchKeyword ? `&q=${this.searchKeyword}&type=product` : '';
        
        this.filtersObj = [];
        this.filterGroup = {};
        
        let qfParam, qfActiveFilter;
        let containQuickfilter = false;

        if (typeof quickFilterVue !== 'undefined') {
          qfParam = quickFilterVue.paramName;
          qfActiveFilter = quickFilterVue.activeFilter;
        }
        
        this.filters.forEach(f => {
          const keyVal = f.split('=');
          if (keyVal[0] != 'filter.v.price.gte' && keyVal[0] != 'filter.v.price.lte') {
            if (keyVal[0] == qfParam && !containQuickfilter) {
              containQuickfilter = true;
            }
            
            const filter = `${keyVal[0]}=${encodeURIComponent(keyVal[1])}`;
            queryStrings = `${queryStrings}&${filter}`;
            
            this.filtersObj.push({
              key: keyVal[0],
              value: keyVal[1],
            });
            
            if (this.filterGroup.hasOwnProperty(keyVal[0])) {
              this.filterGroup[keyVal[0]] = [...this.filterGroup[keyVal[0]], keyVal[1]];
            } else {
              this.filterGroup[keyVal[0]] = [keyVal[1]];
            }
          }
        });
        
        if (!containQuickfilter && qfActiveFilter != 'clear' && typeof quickFilterVue !== 'undefined') {
          quickFilterVue.activeFilter = 'clear'
        }
        
        if (this.filterPrice.min > 0) {
          queryStrings = `${queryStrings}&filter.v.price.gte=${this.filterPrice.min}`;
          this.filtersObj.push({
            key: 'filter.v.price.gte',
            value: this.filterPrice.min,
          });
          this.filterGroup.priceMin = this.filterPrice.min;
        }
        if (this.filterPrice.max > 0) {
          queryStrings = `${queryStrings}&filter.v.price.lte=${this.filterPrice.max}`;
          this.filtersObj.push({
            key: 'filter.v.price.lte',
            value: this.filterPrice.max,
          });
          this.filterGroup.priceMax = this.filterPrice.max;
        }
        
        if (loadProducts) {
          queryStrings = queryStrings.substring(1);
          let url = window.location.href.split('?')[0];
          let updatedUrl = queryStrings == '' ? url : `${url}?${queryStrings}`;
          if (this.sortBy) {
            updatedUrl = queryStrings == '' ? `${updatedUrl}?sort_by=${this.sortBy}` : `${updatedUrl}&sort_by=${this.sortBy}`;
          }
          window.history.replaceState({ path: updatedUrl }, '', updatedUrl);
          this.getProducts(updatedUrl)
        }
      },
      showMore() {
        const button = this.$refs['show-more-btn'];
        button.style.height = `${button.offsetHeight}px`;
        button.style.width = `${button.offsetWidth}px`;
        button.innerHTML = $360.btnLoading();
        button.setAttribute('disabled', true);
        this.currentPage++;
        this.getProducts(this.buildGetProductsUrl(), true);
      },
      goToPage(pageNum) {
        $360.scrollTo('#{{ el_id }}', -70);
        this.currentPage = pageNum;
        const newUrl = this.buildGetProductsUrl();
        window.history.pushState({ path: newUrl, page: pageNum }, '', newUrl);
        this.getProducts(newUrl);
      },
      buildGetProductsUrl() {
        let currentPath = window.location.pathname;
        let params = new URLSearchParams(window.location.search);
        let pageParams = new URLSearchParams();
        for (const [key, value] of params.entries()) {
          if (key !== 'page' && key !== 'view') {
            pageParams.append(key, value);
          }
        }
        pageParams.set('page', this.currentPage); 
        return `${currentPath}?${pageParams.toString()}`;
      },
      async getProducts(collUrl, showMore = false) {
        const urlSplitted = collUrl.split('?');
        const url = urlSplitted.length > 1 ? `${collUrl}&view=product-list` : `${collUrl}?view=product-list`;
        const { data } = await axios.get(url);
        const htmlDom = new DOMParser().parseFromString(data, 'text/html');
        const productCount = htmlDom.querySelectorAll('.product-card').length;
        if (showMore) {
          this.productsDisplayedCount += productCount;
          this.$refs['collection-products'].insertAdjacentHTML('beforeend', data);
          const button = this.$refs['show-more-btn'];
          button.innerHTML = "{{ 'collections.general.load_more_with_count' | t: count: item_per_page }}";
          button.removeAttribute('disabled');
          button.style.height = '';
          button.style.width = '';
        } else {
          // this.productsDisplayedCount = productCount;
          const prevPage = this.currentPage - 1;
          const prevDisplayedCount = this.itemsPerPage * prevPage;

          this.productsDisplayedCount = prevDisplayedCount + productCount;
          if(htmlDom.querySelector('.product-card-wrapper')) {
            this.totalProducts = parseInt(htmlDom.querySelector('.product-card-wrapper').getAttribute('data-total-products'));
          } else {
            this.totalProducts = 0;
          }
          this.$refs['collection-products'].innerHTML = data;
        }

        document.querySelector('.coll-pagination .displayed-count').innerHTML = this.productsDisplayedCount;
        document.querySelector('.coll-pagination .total-products').innerHTML = this.totalProducts;

        setTimeout(function() {
          $360.lazyLoadInstance.update(); 
          document.dispatchEvent(new CustomEvent("swym:product-list-updated"));
          $360.swiperInCard('#{{ el_id }} .cns__products');
        }, 500);
      },
      toggleDrawer(e, text = null) {
        if (text) {
          const $accordions = document.querySelectorAll('.cns__drawer .accordion')
          $accordions.forEach($accordion => {
            const $accordion_title = $accordion.querySelector('.accordion-title')
            const title = $accordion_title.querySelector('.title').innerHTML
            const isActive = $accordion_title.classList.contains('active')
            if (title !== text && isActive) {
              $accordion_title.click()
            }
            if (title === text && !isActive) {
              $accordion_title.click()
            }
          })
        }
        if (this.filterVisible) {
          $360.showFloatingApp();
          $360.enableScroll();
        } else {
          $360.hideFloatingApp();
          $360.disableScroll();
        }
        this.filterVisible = !this.filterVisible
      },
    },
    watch: {
      filters: {
        handler() {
          if(this.firstLoad) {
            this.applyFilter(false);
          } else {
            this.applyFilter();
          }
        }
      },
      filtersObj: {
        handler(val) {
          // console.log({ val })
        }
      },
    }
  }).mount('#{{ el_id }}')
</script>

{% schema %}
{
  "name": "Collection & Search",
  "settings": [
    { "type": "header", "content": "General Settings"},
      {
        "type": "checkbox",
        "id": "hide_filter",
        "label": "Hide filter"
      },
      {
        "type": "checkbox",
        "id": "hide_sort",
        "label": "Hide sort"
      },
      {
        "type": "checkbox",
        "id": "hide_filtered_list",
        "label": "Hide filtered list"
      },
    { "type": "header", "content": "Search Not Found Settings" },
      {
        "type": "select",
        "id": "title_element",
        "label": "Header Title Element",
        "options": [
          { "value": "h1", "label": "h1" },
          { "value": "h2", "label": "h2" },
          { "value": "h3", "label": "h3" },
          { "value": "h4", "label": "h4" },
          { "value": "h5", "label": "h5" },
          { "value": "h6", "label": "h6" },
        ],
        "default": "h1"
      },
      {
        "type": "select",
        "id": "title_class",
        "label": "Header Title Class",
        "options": [
          { "value": "h1", "label": "H1" },
          { "value": "h2", "label": "H2" },
          { "value": "h3", "label": "H3" },
          { "value": "h4", "label": "H4" },
          { "value": "h5", "label": "H5" },
          { "value": "h6", "label": "H6" },
          { "value": "h7", "label": "H7" },
          { "value": "h8", "label": "H8" },
        ],
        "default": "h1"
      },
      {
        "type": "select",
        "id": "text_class",
        "label": "Body Text Class",
        "options": [
          { "value": "p1", "label": "P1" },
          { "value": "p2", "label": "P2" },
          { "value": "p3", "label": "P3" },
          { "value": "p4", "label": "P4" },
          { "value": "p5", "label": "P5" },
        ],
        "default": "p1"
      },
      {
        "type": "text",
        "id": "btn_label",
        "label": "Button Label",
        "default": "Shop All"
      },
      {
        "type": "select",
        "id": "btn_class",
        "label": "Button Class",
        "options": [
          { "value": "btn1", "label": "btn1" },
          { "value": "btn2", "label": "btn2" },
          { "value": "btn3", "label": "btn3" },
          { "value": "btn4", "label": "btn4" },
          { "value": "btn5", "label": "btn5" }
        ],
        "default": "btn1"
      },
      {
        "type": "url",
        "id": "btn_url",
        "label": "Button URL"
      },
    { "type": "header", "content": "Section Padding Settings" },
      {
        "type": "select",
        "id": "padding",
        "label": "Padding",
        "options": [
          { "value": "no", "label": "No" },
          { "value": "xs", "label": "Extra Small" },
          { "value": "sm", "label": "Small" },
          { "value": "md", "label": "Med" },
          { "value": "lg", "label": "Large" }
        ],
        "default": "no"
      },
      {
        "type": "checkbox",
        "id": "top_half_padding",
        "label": "Top half padding",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "no_padding_top",
        "label": "No padding top",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "bottom_half_padding",
        "label": "Bottom half padding",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "no_padding_bottom",
        "label": "No padding bottom",
        "default": false
      }
  ],
  "blocks": [
    {
      "type": "button",
			"name": "Button",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "Text",
          "default": "Button Text"
        },
      ]
    }
  ],
  "presets": [
    {
      "name": "Collection & Search",
      "blocks": []
    }
  ]
}
{% endschema %}