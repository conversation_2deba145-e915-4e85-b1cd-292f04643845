{% liquid
    assign section_name = 'Running Text'
    assign el_id = section_name | handle | append: '-' | append: section.id
%}
<style>
    #{{ el_id }} {
        background: {{ section.settings.background_color }};
        padding: 18px 0;
    }
    #{{ el_id }} .running-text-icon {
        width: 20px;
    }
    #{{ el_id }} .running-text-section {
        overflow: hidden;
        width: 100%;
    }
    #{{ el_id }} .running-text-wrapper {
        white-space: nowrap;
        overflow: hidden;
        position: relative;
    }
    #{{ el_id }} .running-text-marquee {
        /* Starting position */
        -moz-transform:translateX(0px);
        -webkit-transform:translateX(0px);  
        transform:translateX(0px);

        display: inline-flex;
        animation: running-text-marquee 20s linear infinite;
    }
    #{{ el_id }} .speed-slow .running-text-marquee { animation-duration: 30s; }
    #{{ el_id }} .speed-normal .running-text-marquee { animation-duration: 20s; }
    #{{ el_id }} .speed-fast .running-text-marquee { animation-duration: 10s; }
    #{{ el_id }} .running-text-item {
        display: inline-block;
        margin-right: 24px;
    }
    @keyframes running-text-marquee {
        0% { transform: translateX(0);}
        100% { transform: translateX(-50%);}
    }
</style>
<section id="{{ el_id }}">
    <div class="running-text-wrapper speed-{{ section.settings.speed }}">
        <div class="running-text-marquee">
            {% for block in section.blocks %}
                <div class="flex ai-center colgap-8">
                    {%- if block.settings.icon -%}
                        <span class="running-text-icon">
                            {% render 'global-image-wrapper',
                                image: block.settings.icon,
                                image_alt: block.settings.text | escape,
                                additional_class: 'no-bg',
                                preload: true
                            %}
                        </span>
                    {%- endif -%}
                    <span class="running-text-item h8">{{ block.settings.text }}</span>
                </div>
            {% endfor %}
        </div>
        <div class="running-text-marquee">
            {% for block in section.blocks %}
                <div class="flex ai-center colgap-8">
                    {%- if block.settings.icon -%}
                        <span class="running-text-icon">
                            {% render 'global-image-wrapper',
                                image: block.settings.icon,
                                image_alt: block.settings.text | escape,
                                additional_class: 'no-bg',
                                preload: true
                            %}
                        </span>
                    {%- endif -%}
                    <span class="running-text-item h8">{{ block.settings.text }}</span>
                </div>
            {% endfor %}
        </div>
    </div>
</section>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('#{{ el_id }} .running-text-marquee').forEach((el) => {
            if (el) {
                // Duplicate content for seamless loop
                el.innerHTML += el.innerHTML;
            }
        });
    });
    document.addEventListener('shopify:page:load', function() {
        document.querySelectorAll('#{{ el_id }} .running-text-marquee').forEach((el) => {
            if (el) {
                // Duplicate content for seamless loop
                el.innerHTML += el.innerHTML;
            }
        });
    });
</script>
{% schema %}
    {
        "name": "Running Text",
        "blocks": [
            {
                "type": "text",
                "name": "Text Item",
                "settings": [
                    {
                        "type": "image_picker",
                        "id": "icon",
                        "label": "Icon text"
                    },
                    {
                        "type": "text",
                        "id": "text",
                        "label": "Text",
                        "default": "Sample running text"
                    }
                ]
            }
        ],
        "max_blocks": 10,
        "settings": [
            {
                "type": "color",
                "id": "background_color",
                "label": "Background color",
                "default": "#93C9FF"
            },
            {
                "type": "select",
                "id": "speed",
                "label": "Scroll Speed",
                "options": [
                    { "value": "slow", "label": "Slow" },
                    { "value": "normal", "label": "Normal" },
                    { "value": "fast", "label": "Fast" }
                ],
                "default": "normal"
            }
        ],
        "presets": [
            {
                "name": "Running Text",
                "blocks": [
                    { "type": "text", "settings": { "icon": "", "text": "Fast shipping worldwide" } },
                    { "type": "text", "settings": { "icon": "", "text": "24/7 customer support" } },
                    { "type": "text", "settings": { "icon": "", "text": "Secure payment options" } },
                    { "type": "text", "settings": { "icon": "", "text": "100% satisfaction guarantee" } },
                    { "type": "text", "settings": { "icon": "", "text": "New arrivals every week" } }
                ]
            }
        ]
    }
{% endschema %}