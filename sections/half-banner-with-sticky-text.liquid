{% liquid
    assign section_name = 'Half Banner + Sticky Text'
    assign el_id = section_name | handle | append: '-' | append: section.id
%}
<style>
    #{{ el_id }} .banner-wrapper {
        display: grid;
        grid-template-columns: 1fr;
        gap: 8px;
    }
    #{{ el_id }} .banner-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        padding: 24px;
    }
    #{{ el_id }} .banner-content .banner-text {
        max-width: 480px;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 74.4px;
    }
    @media only screen and (min-width: 1024px) {
        #{{ el_id }} .banner-wrapper {
            grid-template-columns: repeat(2, 1fr);
        }
        #{{ el_id }} .banner-content {
            padding: 40px;
        }
        #{{ el_id }} .banner-content .banner-text {
            margin-bottom: 90.4px;
        }
    }
</style>
<section id="{{ el_id }}">
    <div class="banner-wrapper">
        {% for block in section.blocks %}
            <div class="banner-block relative{% if block.settings.image == blank %} bg-grey{% endif %}">
                {% if block.settings.banner_image %}
                    {% render 'global-image-wrapper',
                        image: block.settings.banner_image,
                        image_alt: block.settings.banner_title | escape,
                        overlay: true
                    %}
                {% else %}
                    {{ 'image' | placeholder_svg_tag }}
                {% endif %}
                <div class="banner-content zi-1">
                    <div class="banner-text tc c-white sticky-below-header">
                        <h2 class="h2">{{ block.settings.banner_title }}</h2>
                        <p class="p2">{{ block.settings.banner_text }}</p>
                    </div>
                    {% if block.settings.link_label != blank %}
                        <a href="{{ block.settings.link_url }}" class="btn1 tc w-full"
                           style="background-color:{{ block.settings.link_bg_color }};color:{{ block.settings.link_text_color }};"
                        >
                            {{ block.settings.link_label }}
                        </a>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>

</section>
{% schema %}
    {
        "name": "Half Banner + Sticky Text",
        "blocks": [
            {
                "type": "banner_sticky_text",
                "name": "Banner + Sticky text",
                "settings": [
                    {
                        "type": "image_picker",
                        "id": "banner_image",
                        "label": "Banner Image"
                    },
                    {
                        "type": "text",
                        "id": "banner_title",
                        "label": "Banner Title",
                        "default": "Your Banner Title Here"
                    },
                    {
                        "type": "text",
                        "id": "banner_text",
                        "label": "Banner Text",
                        "default": "Your banner Text Here"
                    },
                    {
                        "type": "color",
                        "id": "link_bg_color",
                        "label": "Link Background Color"
                    },
                    {
                        "type": "color",
                        "id": "link_text_color",
                        "label": "Link text Color"
                    },
                    {
                        "type": "text",
                        "id": "link_label",
                        "label": "Link Label"
                    },
                    {
                        "type": "url",
                        "id": "link_url",
                        "label": "Link URL"
                    }
                ]
            }
        ],
        "max_blocks": 2,
        "settings": [],
        "presets": [
            {
                "name": "Half Banner + Sticky Text",
                "category": "Custom",
                "blocks": [
                    {
                        "type": "banner_sticky_text",
                        "settings": {
                            "banner_title": "Sample Banner Title 1",
                            "banner_text": "This is a sample text for the banner.",
                            "link_label": "Learn More",
                            "link_url": "#"
                        }
                    },
                    {
                        "type": "banner_sticky_text",
                        "settings": {
                            "banner_title": "Sample Banner Title 2",
                            "banner_text": "This is a sample text for the banner.",
                            "link_label": "Learn More",
                            "link_url": "#"
                        }
                    }
                ]
            }
        ]
    }
{% endschema %}