{%- if predictive_search.performed -%}
    <div id="predictive-search-results">
        {%- assign has_result = false -%}
        {%- if predictive_search.resources.products.size > 0 -%}
            {%- assign has_result = true -%}
        {%- endif -%}
        {%- if settings.pred_search_show_articles and predictive_search.resources.articles.size > 0 -%}
            {%- assign has_result = true -%}
        {%- endif -%}
        {%- if settings.pred_search_show_pages and predictive_search.resources.pages.size > 0 -%}
            {%- assign has_result = true -%}
        {%- endif -%}
        {%- if settings.pred_search_show_collections and predictive_search.resources.collections.size > 0 -%}
            {% assign has_collections = true %}
        {%- endif -%}

        {%- if has_result == false -%}
            <div class="flex flex-column ai-start mt-24 mb-24 d-mt-40 d-mb-40">
                <p class="p2 tc text-not-found">{{ 'general.search.not_found_text' | t: terms: predictive_search.terms }}</p>
                {% comment %} <a href="{{ settings.pred_search_no_result_btn_url }}" title="{{ settings.pred_search_no_result_btn_label | escape }}" class="btn1 block mt-16 d-mt-24">{{ settings.pred_search_no_result_btn_label }}</a> {% endcomment %}
            </div>
        {%- else -%}
        <div class="results-wrapper row-d pt-24 pb-24 d-pt-32 d-pb-0">
            <div class="{% if has_collections %}col-d-4{% else %}col-d-2{% endif %}">
            <div class="results-wrapper-top pb-28 d-colgap-16 jc-between grid-flex rowgap-12 {% if has_collections %}m-2  d-minus-6 {% endif %}">

                {% comment %} Queries {% endcomment %}
                {%- if predictive_search.resources.queries.size > 0 -%}
                    <div class="result-group queries">
                        <h3 class="p4 c-grey mb-12 d-mb-16">{{ 'header.suggested_queries' | t  }}</h3>
                        <ul class="query-list">
                            {%- for query in predictive_search.resources.queries, limit: 5 -%}
                                <li class="lh-0 {% unless forloop.last %}mb-12{% endunless %}">
                                    <a href="{{ routes.search_url }}?q={{ query.text | url_encode }}" title="{{ query.text | escape }}" class="p2 c-black capitalize">{{ query.text | highlight: predictive_search.terms }}</a>
                                </li>
                            {%- endfor -%}
                        </ul>
                    </div>
                {%- endif -%}

                {% comment %} Pages {% endcomment %}
                {%- if settings.pred_search_show_pages -%}
                    <div class="result-group pages">
                        <h3 class="p4 c-grey mb-12 d-mb-16">{{ 'general.search.pages.title' | t  }}</h3>
                        {%- if predictive_search.resources.pages.size > 0 -%}
                            <ul class="page-list">
                                {%- for page in predictive_search.resources.pages -%}
                                    <li class="lh-0 {% unless forloop.last %}mb-12{% endunless %}">
                                        <a href="{{ page.url }}" title="{{ page.title | escape }}" class="p2">{{ page.title }}</a>
                                    </li>
                                {%- endfor -%}
                            </ul>
                        {%- else -%}
                            <p class="p2">{{ 'general.search.not_found' | t }}</p>
                        {%- endif -%}
                    </div>
                {%- endif -%}
                {% comment %} End Pages {% endcomment %}

                 {% comment %} Collections {% endcomment %}
                {%- if settings.pred_search_show_collections -%}
                    {%- if predictive_search.resources.collections.size > 0 -%}

                    <div class="result-group collections">
                        <h3 class="p4 c-grey mb-12 d-mb-16">{{ 'general.search.collections.title' | t  }}</h3>
                        {%- if predictive_search.resources.collections.size > 0 -%}
                            <ul class="collection-list">
                                {%- for collection in predictive_search.resources.collections -%}
                                    <li class="flex ai-center colgap-4 lh-0 {% unless forloop.last %}mb-12{% endunless %}">
                                        {%- assign parent_collection = collection.metafields.info.parent_collection.value -%}
                                        {%- if parent_collection != blank -%}
                                            <a href="{{ parent_collection.url }}" title="{{ parent_collection.title | escape }}" class="c-maroon-2 p2">{{ parent_collection.title }}</a>
                                            <span class="c-maroon-2 p2">/</span>
                                        {%- endif -%}
                                        <a href="{{ collection.url }}" title="{{ collection.title | escape }}" class="p2">{{ collection.title }}</a>
                                    </li>
                                {%- endfor -%}
                            </ul>
                        {%- else -%}
                            <p class="p2">{{ 'general.search.not_found' | t }}</p>
                        {%- endif -%}
                    </div>
                    {%- endif -%}

                {%- endif -%}
                {% comment %} End Collections {% endcomment %}

                {% comment %} Articles {% endcomment %}
                {%- if settings.pred_search_show_articles -%}
                    <div class="result-group articles">
                        <div class="flex jc-between ai-center mb-16 d-mb-20 d-ai-start">
                        <div class="flex jc-start ai-center">
                            {%- if predictive_search.resources.articles.size > 0 -%}
                                <span class="p2 c-grey mr-4">{{ predictive_search.resources.articles.size }}</span>
                            {%- endif -%}
                            <h3 class="p4 c-grey mb-12 d-mb-16">{{ 'general.search.articles.title' | t }}</h3>
                        </div>
                    </div>
                        {%- if predictive_search.resources.articles.size > 0 -%}
                            <ul class="article-list">
                                {%- for article in predictive_search.resources.articles, limit: 3 -%}
                                    <div class="article-list-item {% if forloop.first %}pb-20{% elsif forloop.last %}pt-20{% else %}pt-20 pb-20{% endif %} {% unless forloop.last %}bb-1 b-stroke{% endunless %}">
                                    {% if article.tags != blank %}
                                        <div class="flex flex-wrap mb-12 colgap-8">
                                        {% for tag in article.tags %}
                                                <span class="p2 c-grey pl-8 pr-8 pb-2 rounded-100 b-1 b-border">
                                                    {{ tag }}
                                                </span>
                                            {% endfor %}
                                        </div>    
                                    {% endif %}
                                    <li class="h5">
                                        <a href="{{ article.url }}" title="{{ article.title | escape }}" class="p2 c-black">{{ article.title }}</a>
                                        </li>
                                    </div>
                                {%- endfor -%}
                            </ul>
                            <div class="view-all mt-16">
                                {%- assign view_all = 'general.search.products.view_all' | t: count: predictive_search.resources.products.size -%}
                                {% assign view_all_title = view_all | escape %}
                                {%- assign url = routes.search_url | append: '?q=' | append: predictive_search.terms | append: '&type=article' -%}
                                
                                <a href="{{ url }}" title="{{ view_all_title }}" class="link-icon w-max-cont link2">
                                    <span class="text">{{ view_all }}</span>
                                </a>
                            </div>   
                        {%- else -%}
                            <p class="p2">{{ 'general.search.not_found' | t }}</p>
                        {%- endif -%}
                    </div>
                {%- endif -%}
                {% comment %} End Articles {% endcomment %}
                
            </div>
            </div>

            <div class="{% if has_collections %}col-d-8{% else %}col-d-10{% endif %}">
            <div class="results-wrapper-bottom pt-28 d-pt-0">
                {% comment %} Products {% endcomment %}
                <div class="result-group col-12 pl-0 pr-0 pb-24 d-pb-48 products relative">
                    <div class="flex jc-between ai-center mb-16 d-mb-20 d-ai-start">
                        <h3 class="p4 c-grey capitalize mb-12 d-mb-16">{{ 'general.search.products.title' | t }}</h3>
                        <div class="view-all ai-start hide-m d-flex">    
                            {%- assign view_all = 'general.search.products.view_all' | t: term: predictive_search.terms -%}
                            {% assign view_all_title = view_all | escape %}
                            {%- assign url = routes.search_url | append: '?q=' | append: predictive_search.terms | append: '&type=product&options[unavailable_products]=hide&options[prefix]=last' -%}
                        
                            <a href="{{ url }}" title="{{ view_all_title }}" class="link-icon w-max-cont c-black link3">
                                <span class="text">{{ view_all }}</span>
                            </a>
                        </div>
                        </div>
                    {%- if predictive_search.resources.products.size > 0 -%}
                        <ul class="product-list {% if has_collections %} d-max-3 {% endif %} row flex flex-wrap rowgap-8 t-rowgap-12 pb-40 d-pb-0">
                            {%- for product in predictive_search.resources.products, limit: 6 -%}
                                <li class="each col-6 col-t-4 {% if has_collections %}col-d-4 col-hd-3{% else %}col-d-3 col-hd-1per5{% endif %}">
                                        {%- render 'product-card', product: product, hide_atc: false -%}
                                </li>
                                {% comment %} <li class="each show-m hide-d col-12">
                                        {%- render 'product-card-2', product: product, bg_class: 'bg-light-grey' , wrapper_class: ''-%}
                                </li> {% endcomment %}
                                {%- endfor -%}
                        </ul>
                        <div class="view-all show-m hide-d">
                            {%- assign view_all = 'general.search.products.view_all' | t: term: predictive_search.terms -%}
                            {% assign view_all_title = view_all | escape %}
                            {%- assign url = routes.search_url | append: '?q=' | append: predictive_search.terms | append: '&type=product&options[unavailable_products]=hide&options[prefix]=last' -%}
                           
                            <a href="{{ url }}" title="{{ view_all_title }}" class="link-icon w-full btn6 tc">
                                <span class="text">{{ view_all }}</span>
                            </a>
                        </div>
                    {%- else -%}
                        <p class="p2">{{ 'general.search.not_found' | t }}</p>
                    {%- endif -%}
                </div>
                {% comment %} End Products {% endcomment %}
            </div>
            </div>
        </div>
        {%- endif -%}
    </div>
{%- endif -%}
  