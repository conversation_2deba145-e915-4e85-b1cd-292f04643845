{%- assign el_id = 'error-page-' | append: section.id -%}
{%- render 'section-padding', el_id: el_id, section: section -%}
<div id="{{ el_id }}" style="background-color:{{ section.settings.bg_color }};">
  <div class="container">
    <div class="row flex column-reverse d-flex-row d-ai-center">
      <div class="col-12 col-d-5">
        <div class="context d-ml-50 d-mr-70">
          {% if section.settings.small_title != blank %}
            <p class="p3 mb-15">{{ section.settings.small_title }}</p>
          {% endif %}

          <{{ section.settings.title_element }} class="h2">
            {{ section.settings.title }}
          </{{ section.settings.title_element }}>

          {% if section.settings.text != blank %}
            <div class="rte p2 mt-10">{{ section.settings.text }}</div>
          {% endif %}
          
          {% if section.settings.button_label != blank and section.settings.button_link != blank %}
            <a href="{{ section.settings.button_link }}" class="btn2 mt-30">{{ section.settings.button_label }}</a>
          {% endif %}
        </div>
      </div>
      <div class="col-12 col-d-7 mb-40 d-mb-0">
        {% render 'global-image-wrapper', image: section.settings.image, additional_class: 'no-bg' %}
      </div>
    </div>
  </div>
</div>
{% schema %}
  {
    "name": "404",
    "settings": [
      {
        "type": "header",
        "content": "Section Padding"
      },
      {
        "type": "number",
        "id": "top_padding_m",
        "label": "Top padding (Mobile)",
        "default": 0
      },
      {
        "type": "number",
        "id": "bottom_padding_m",
        "label": "Bottom padding (Mobile)",
        "default": 0
      },
      {
        "type": "number",
        "id": "top_padding_t",
        "label": "Top padding (Tablet)",
        "default": 0
      },
      {
        "type": "number",
        "id": "bottom_padding_t",
        "label": "Bottom padding (Tablet)",
        "default": 0
      },
      {
        "type": "number",
        "id": "top_padding",
        "label": "Top padding (Desktop)",
        "default": 0
      },
      {
        "type": "number",
        "id": "bottom_padding",
        "label": "Bottom padding (Desktop)",
        "default": 0
      },
      {
        "type": "number",
        "id": "top_padding_hd",
        "label": "Top padding (Desktop 1366px)",
        "default": 0
      },
      {
        "type": "number",
        "id": "bottom_padding_hd",
        "label": "Bottom padding (Desktop 1366px)",
        "default": 0
      },
      {
        "type": "number",
        "id": "top_padding_hhd",
        "label": "Top padding (Desktop 1920px)",
        "default": 0
      },
      {
        "type": "number",
        "id": "bottom_padding_hhd",
        "label": "Bottom padding (Desktop 1920px)",
        "default": 0
      },
      {
        "type": "header",
        "content": "General Settings"
      },
      {
        "type": "image_picker",
        "id": "image",
        "label": "Image"
      },
      {
        "type": "text",
        "id": "small_title",
        "label": "Small Title"
      },
      {
        "type": "text",
        "id": "title",
        "label": "Title"
      },
      {
        "type": "select",
        "id": "title_element",
        "label": "Title element",
        "options": [
          {
            "value": "h1",
            "label": "h1"
          }, {
            "value": "h2",
            "label": "h2"
          }, {
            "value": "h3",
            "label": "h3"
          }, {
            "value": "h4",
            "label": "h4"
          }
        ],
        "default": "h2"
      },
      {
        "type": "richtext",
        "id": "text",
        "label": "Text"
      },
      {
        "type": "text",
        "id": "button_label",
        "label": "Button label"
      }, 
      {
        "type": "url",
        "id": "button_link",
        "label": "Button Link"
      }, 
      {
        "type": "color",
        "id": "bg_color",
        "label": "Background Color"
      }
    ]
  }
{% endschema %}