<style>
{%- for block in section.blocks -%}
  .p{{ block.settings.title }}{%- if block.settings.default -%},body,button,input,select,textarea,p,div,.rte,.rte p,.rte ul,.rte ol{%- endif -%}{
    {%- assign key = "font" | append: block.settings.family -%}
    font-family: {{ section.settings[key] }};
    {%- if block.settings.uppercase -%}text-transform:uppercase;{%- endif -%}
    {%- if block.settings.capitalize -%}text-transform:capitalize;{%- endif -%}

    {%- if block.settings.mobile-spacing != blank -%}letter-spacing:{{ block.settings.mobile-spacing }}px;{%- endif -%}
    font-weight: {{ block.settings.font-weight }};
    font-size:{{ block.settings.mobile-size }}px;
    font-size:{{ block.settings.mobile-size | divided_by: 16.0 }}rem;
    line-height:{{ block.settings.mobile-height }};
  }
  .rte.p{{ block.settings.title }} div,
  .rte.p{{ block.settings.title }} p,
  .rte.p{{ block.settings.title }} ul li,
  .rte.p{{ block.settings.title }} ol li {
    {%- assign key = "font" | append: block.settings.family -%}
    font-family: {{ section.settings[key] }};
    {%- if block.settings.uppercase -%}text-transform:uppercase;{%- endif -%}
    {%- if block.settings.capitalize -%}text-transform:capitalize;{%- endif -%}

    {%- if block.settings.mobile-spacing != blank -%}letter-spacing:{{ block.settings.mobile-spacing }}px;{%- endif -%}
    font-weight: {{ block.settings.font-weight }};
    font-size:{{ block.settings.mobile-size }}px;
    font-size:{{ block.settings.mobile-size | divided_by: 16.0 }}rem;
    line-height:{{ block.settings.mobile-height }};
  }

  {%- if block.settings.tablet-size != blank or block.settings.tablet-height != blank -%}
    @media only screen and (min-width: 600px){
      .p{{ block.settings.title }}{%- if block.settings.default -%},body,button,input,select,textarea,p,div,.rte,.rte p,.rte ul,.rte ol{%- endif -%}{
        {%- if block.settings.tablet-size != blank -%}font-size:{{ block.settings.tablet-size }}px;
        {%- if block.settings.tablet-spacing != blank -%}letter-spacing:{{ block.settings.tablet-spacing }}px;{%- endif -%}
        font-size:{{ block.settings.tablet-size | divided_by: 16.0 }}rem;{%- endif -%}
        {%- if block.settings.tablet-height != blank -%}line-height:{{ block.settings.tablet-height }};{%- endif -%}
      }
      .rte.p{{ block.settings.title }} div,
      .rte.p{{ block.settings.title }} p,
      .rte.p{{ block.settings.title }} ul li,
      .rte.p{{ block.settings.title }} ol li {
        {%- if block.settings.tablet-size != blank -%}font-size:{{ block.settings.tablet-size }}px;
        {%- if block.settings.tablet-spacing != blank -%}letter-spacing:{{ block.settings.tablet-spacing }}px;{%- endif -%}
        font-size:{{ block.settings.tablet-size | divided_by: 16.0 }}rem;{%- endif -%}
        {%- if block.settings.tablet-height != blank -%}line-height:{{ block.settings.tablet-height }};{%- endif -%}
      }
    }
  {%- endif -%}

  {%- if block.settings.desktop-size != blank or block.settings.desktop-height != blank -%}
    @media only screen and (min-width: 1024px){
      .p{{ block.settings.title }}{%- if block.settings.default -%},body,button,input,select,textarea,p,div,.rte,.rte p,.rte ul,.rte ol{%- endif -%}{
        {%- if block.settings.desktop-size != blank -%}font-size:{{ block.settings.desktop-size }}px;
        {%- if block.settings.desktop-spacing != blank -%}letter-spacing:{{ block.settings.desktop-spacing }}px;{%- endif -%}
        font-size:{{ block.settings.desktop-size | divided_by: 16.0 }}rem;{%- endif -%}
        {%- if block.settings.desktop-height != blank -%}line-height:{{ block.settings.desktop-height }};{%- endif -%}
      }
      .rte.p{{ block.settings.title }} div,
      .rte.p{{ block.settings.title }} p,
      .rte.p{{ block.settings.title }} ul li,
      .rte.p{{ block.settings.title }} ol li {
        {%- if block.settings.desktop-size != blank -%}font-size:{{ block.settings.desktop-size }}px;
        {%- if block.settings.desktop-spacing != blank -%}letter-spacing:{{ block.settings.desktop-spacing }}px;{%- endif -%}
        font-size:{{ block.settings.desktop-size | divided_by: 16.0 }}rem;{%- endif -%}
        {%- if block.settings.desktop-height != blank -%}line-height:{{ block.settings.desktop-height }};{%- endif -%}
      }
    }
  {%- endif -%}

  {%- for i in (1..3) -%}
    {%- assign width = "point" | append:i | append:"-width" -%}
    {%- assign size = "point" | append:i | append:"-size" -%}
    {%- assign height = "point" | append:i | append:"-height" -%}
    {%- assign spacing = "point" | append:i | append:"-spacing" -%}
    {%- if block.settings[width] != blank -%}
      @media only screen and (min-width: {{ block.settings[width]}}px){
        .p{{ block.settings.title }}{%- if block.settings.default -%},body,button,input,select,textarea,p,div,.rte,.rte p,.rte ul,.rte ol{%- endif -%}{
          {%- if block.settings[size] != blank -%}font-size:{{ block.settings[size] }}px;
          font-size:{{ block.settings[size] | divided_by: 16.0 }}rem;{%- endif -%}
          {%- if block.settings[height] != blank -%}line-height:{{ block.settings[height] }};{%- endif -%}
          {%- if block.settings[spacing] != blank -%}letter-spacing:{{ block.settings[spacing] }}px;{%- endif -%}
        }
        .rte.p{{ block.settings.title }} div,
        .rte.p{{ block.settings.title }} p,
        .rte.p{{ block.settings.title }} ul li,
        .rte.p{{ block.settings.title }} ol li {
          {%- if block.settings[size] != blank -%}font-size:{{ block.settings[size] }}px;
          font-size:{{ block.settings[size] | divided_by: 16.0 }}rem;{%- endif -%}
          {%- if block.settings[height] != blank -%}line-height:{{ block.settings[height] }};{%- endif -%}
          {%- if block.settings[spacing] != blank -%}letter-spacing:{{ block.settings[spacing] }}px;{%- endif -%}
        }
      }
    {%- endif -%}
  {%- endfor -%}
{%- endfor -%}
{%- if section.settings.custom != blank %}{{ section.settings.custom }}{%- endif -%}
</style>
  {% schema %}
  {
    "name": "Typography - Body",
    "settings": [
    {
      "type": "textarea",
      "id": "font1",
      "label": "Font Family 1"
    },
    {
      "type": "textarea",
      "id": "font2",
      "label": "Font Family 2"
    },
    {
      "type": "textarea",
      "id": "font3",
      "label": "Font Family 3"
    },
    {
      "type": "textarea",
      "id": "custom",
      "label": "Custom CSS rules",
      "info":"Enter any custom CSS rules here if needed"
    }
    ],
    "blocks" : [
    {
      "type": "style",
      "name": "Style",
      "settings": [
      {
        "type": "checkbox",
        "id": "default",
        "label": "Default font for paragraph, anchor links and rte"
      },
      {
        "type":"range",
        "id":"title",
        "label":"Suffix",
        "info":"You will be able to use the CSS class in your liquid files. E.g. if the suffix is 1, the CSS class will be p1.",
        "min":1,
        "max":10,
        "default":1,
        "step":1
      },
      {
        "type":"radio",
        "id":"family",
        "label":"Family",
        "options":[
        {"value":"1","label":"Family 1"},
        {"value":"2","label":"Family 2"},
        {"value":"3","label":"Family 3"}
        ]
      },
      {
        "type": "checkbox",
        "id": "uppercase",
        "label": "Uppercase"
      },
      {
        "type": "checkbox",
        "id": "capitalize",
        "label": "Capitalize"
      },
      {
        "type": "text",
        "id": "font-weight",
        "label": "Font Weight",
        "default":"400"
      },
      {
        "type": "header",
        "content": "Mobile"
      },
      {
        "type": "text",
        "id": "mobile-size",
        "label": "Font Size"
      },
      {
        "type": "text",
        "id": "mobile-height",
        "label": "Line Height"
      },
      {
        "type": "text",
        "id": "mobile-spacing",
        "label": "Letter Spacing"
      },
      {
        "type": "header",
        "content": "Tablet (600px)"
      },
      {
        "type": "text",
        "id": "tablet-size",
        "label": "Font Size"
      },
      {
        "type": "text",
        "id": "tablet-height",
        "label": "Line Height"
      },
      {
        "type": "text",
        "id": "tablet-spacing",
        "label": "Letter Spacing"
      },
      {
        "type": "header",
        "content": "Desktop (1024px)"
      },
      {
        "type": "text",
        "id": "desktop-size",
        "label": "Font Size"
      },
      {
        "type": "text",
        "id": "desktop-height",
        "label": "Line Height"
      },
      {
        "type": "text",
        "id": "desktop-spacing",
        "label": "Letter Spacing"
      },
      {
        "type": "header",
        "content": "Break Point 1"
      },
      {
        "type": "text",
        "id": "point1-width",
        "label": "Screen Width",
        "info":"Must be bigger than the previous breakpoint"
      },
      {
        "type": "text",
        "id": "point1-size",
        "label": "Font Size"
      },
      {
        "type": "text",
        "id": "point1-height",
        "label": "Line Height"
      },
      {
        "type": "text",
        "id": "point1-spacing",
        "label": "Letter Spacing"
      },
      {
        "type": "header",
        "content": "Break Point 2"
      },
      {
        "type": "text",
        "id": "point2-width",
        "label": "Screen Width",
        "info":"Must be bigger than the previous breakpoint"
      },
      {
        "type": "text",
        "id": "point2-size",
        "label": "Font Size"
      },
      {
        "type": "text",
        "id": "point2-height",
        "label": "Line Height"
      },
      {
        "type": "text",
        "id": "point2-spacing",
        "label": "Letter Spacing"
      },
      {
        "type": "header",
        "content": "Break Point 3"
      },
      {
        "type": "text",
        "id": "point3-width",
        "label": "Screen Width",
        "info":"Must be bigger than the previous breakpoint"
      },
      {
        "type": "text",
        "id": "point3-size",
        "label": "Font Size"
      },
      {
        "type": "text",
        "id": "point3-height",
        "label": "Line Height"
      },
      {
        "type": "text",
        "id": "point3-spacing",
        "label": "Letter Spacing"
      }
      ]
    }
    ] 
  }

  {% endschema %}
