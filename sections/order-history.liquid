{%- liquid
    assign section_name = 'Order History'
    assign el_id = section_name | handle | append: '-' | append: section.id
-%}
<style>
    
</style>
<section id="{{ el_id }}" class="pt-32 d-pt-80 d-pb-80">
    <div class="container">
        <div class="row flex flex-column d-flex-row">
            <div class="col-d-1"></div>
            <div class="col-12 col-d-2">
                {%- render 'account-links' -%}
            </div>
            {%- render 'account-menu-dropdown' -%}
            <div class="col-d-1"></div>
            <div class="col-12 col-d-7">
                <div class="order-wrapper bg-white rounded-tl-16 rounded-tr-16 d-rounded-16 pt-32 pb-32">
                    <div class="order-history-title mb-32 d-mb-60">
                        <h2 class="h3 mb-8 d-mb-12 tl">{{ 'customer.orders.title' | t }}</h2>
                        {%- if customer.orders.size > 0 -%}
                            <p class="p2 tl">{{ 'customer.orders.text' | t }}</p>
                        {%- endif -%}
                    </div>
                    <div class="order-main-box">
                        {%- if customer.orders.size > 0 -%}
                            <div class="hide-d">
                                <div class="order-list">
                                    {%- for order in customer.orders -%}
                                        <div class="row ml-0 mr-0{% unless forloop.first %} pt-24{% endunless %}{% unless forloop.last %} pb-24 bb-1 b-brown-20{% endunless %}">
                                            <div class="col-6 pl-0 pr-0">
                                                <p class="p2 bold-600 mb-12">{{ 'customer.orders.order_number' | t }}</p>
                                                <p class="p2 bold-600 mb-12">{{ 'customer.orders.date' | t }}</p>
                                                <p class="p2 bold-600 mb-12">{{ 'customer.orders.amount' | t }}</p>
                                                <p class="p2 bold-600">{{ 'customer.orders.status' | t }}</p>
                                            </div>
                                            <div class="col-6 pl-0 pr-0">
                                                <a class="p2 underline mb-12 block" href="{{ order.customer_url }}">{{ order.name }}</a>
                                                <p class="p2 mb-12">{{ order.created_at | date: '%d %b %Y' }}</p>
                                                <p class="p2 mb-12">{{ order.total_price | money }}</p>
                                                <p class="p2">{{ order.fulfillment_status_label }}</p>
                                            </div>
                                        </div>
                                    {%- endfor -%}
                                </div>
                            </div>
                            <div class="hide-m show-d">
                                <div class="order-table pl-0 pr-0 middle-pl-0 middle-pr-0">
                                    <table class="data-list">
                                        <colgroup>
                                            <col class="number">
                                            <col class="date">
                                            <col class="total">
                                            <col class="status">
                                        </colgroup>
                                        <thead>
                                            <tr>
                                                <td class="pb-8 p2 bold-700">{{ 'customer.orders.order_number' | t }}</td>
                                                <td class="pb-8 p2 bold-700">{{ 'customer.orders.date' | t }}</td>
                                                <td class="pb-8 p2 bold-700">{{ 'customer.orders.amount' | t }}</td>
                                                <td class="pb-8 p2 bold-700">{{ 'customer.orders.order_status' | t }}</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {%- for order in customer.orders -%}
                                                <tr class="">
                                                    <td class="p2 pt-24{% unless forloop.last %} pb-20 d-pb-24{% endunless %} ws-nowrap">
                                                        <a class="p2 underline" href="{{ order.customer_url }}">{{ order.name }}</a>
                                                    </td>
                                                    <td class="p2 pt-24{% unless forloop.last %} pb-24{% endunless %} ws-nowrap">{{ order.created_at | date: '%d %b %Y' }}</td>
                                                    <td class="p2 pt-24{% unless forloop.last %} pb-24{% endunless %}">{{ order.total_price | money }}</td>
                                                    <td class="p2 pt-24{% unless forloop.last %} pb-24{% endunless %}">{{ order.fulfillment_status_label }}</td>
                                                </tr>
                                            {%- endfor -%}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        {%- else -%}
                            <div class="order-empty tc pt-16 pb-32 d-pt-20 d-pb-88">
                                <p class="p2 mb-24">{{ 'customer.orders.none' | t }}</p>
                                <a href="{{ section.settings.link_url }}" class="{{ section.settings.link_type }}{% if section.settings.link_type != 'underline' %}{{ section.settings.link_class }}{% else %} p1{% endif %}">
                                    {{ section.settings.link_label }}
                                </a>
                            </div>
                        {%- endif -%}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% schema %}
    {
        "name": "Order History",
        "settings": [
            {
                "type": "header",
                "content": "General Settings"
            },
            {
                "type": "text",
                "id": "link_label",
                "label": "Link label",
                "info": "Use for when there is no any orders"
            },
            {
                "type": "url",
                "id": "link_url",
                "label": "Link URL",
                "info": "Use for when there is no any orders"
            },
            {
                "type": "select",
                "id": "link_type",
                "label": "Link type",
                "options": [
                    {"value": "btn", "label": "Button"},
                    {"value": "link", "label": "Link"},
                    {"value": "underline", "label": "Uderline"}
                ]
            },
            {
                "type": "range",
                "id": "link_class",
                "label": "Link class (Button Style)",
                "min": 1,
                "max": 11,
                "step": 1,
                "default": 1,
                "info": "Button up to 11, Link up to 7"
            }
        ],
        "presets": [{ "name": "Order History" }]
    }
{% endschema %}