{% if customer %}
	<script>window.location.href = '/account'</script>
{% else %}
	{%- assign el_id = 'forgot-password-' | append: section.id -%}
	<style>
		#{{ el_id }} {
			height: 100vh;
			display: flex;
			align-items: center;
		}
	</style>
	<div id="{{ el_id }}" class="sp-md">
		<div class="container w-full">
			<div class="row">
				<div class="col-12 col-t-8 col-d-4 push-t-2 push-d-4">
					<div v-if="showSuccess" id="success-div">
						<div class="tc success-text">
							<h1 class="h4 mb-8 d-mb-16">{{ 'customer.recover_password.success_title' | t }}</h1>
							<p class="p2 mb-12 d-mb-20">
								{{ 'customer.recover_password.success_text' | t }}
								<a {% if section.settings.contact_us_url != blank %}href="{{ section.settings.contact_us_url }}" {% endif %}class='p2 underline'>
									{{ 'customer.recover_password.contact_us' | t }}
								</a>
							</p>
                                <a href="{{ routes.account_login_url }}" class="btn2 tc w-full">
                                    <span class="p2 c-link-grey">{{ 'customer.login.submit' | t }}</span>
                                </a>
						</div>
					</div>
			
					<div v-if="showForm" id="forgot-div">
						{%- form 'recover_customer_password', id: 'recover_customer_password' -%}
                            <h1 class="h4 tc mb-8">{{ 'customer.recover_password.title' | t }}</h1>
                            <p class="p2 tc">{{ 'customer.recover_password.subtext' | t }}</p>
                
                            {%- if form.errors -%}
                            <div class="form-errors mb-30 global-error-msg rte p2">
                                {{ form.errors | default_errors }}
                            </div>
                            {%- endif -%}
                
							<div class="mt-16 d-mt-20">
								<div class="mb-12 d-mb-20">
									<div class="global-input">
										<input type="email" v-model="resetForm.email" name="email" id="email-reset-pass" class="global-input-account p2"
											:class="{'global-input-error': resetForm.email_error}" placeholder="{{ 'customer.recover_password.email' | t }}" />
									</div>
									<div v-if="resetForm.email_error" class="global-error-msg p2 mt-8">{{ 'customer.recover_password.error_email' | t }}</div>
								</div>
								<input type="hidden" name="return_to" value="/pages/forgot-password#recover-success" />
								<div class="button-container tc">
									<button type="submit" class="btn2 w-full mb-12">{{ 'customer.recover_password.submit' | t }}</button>
									<a href="{{ routes.account_login_url }}" class="p2 c-grey">
										Cancel
									</a>
								</div>
							</div>
						{%- endform -%}
					</div>
				</div>
			</div>
		</div>
	</div>

	<script>
		Vue.createApp({
			delimiters: ['${', '}'],
			data() {
				return {
					resetForm: {
						email: null,
						has_error: false,
						email_error: false,
					},
					showSuccess: false,
                    showForm: true
				}
			},
			mounted() {				
				if (window.location.hash == '#recover-success') {
					this.showSuccess = true;
                    this.showForm = false;
				}

				const thisObj = this;

				document.getElementById('recover_customer_password').addEventListener('submit', function (e) {
					e.preventDefault();

					thisObj.resetForm.has_error = false;
					thisObj.resetForm.email_error = false;

					if (!thisObj.resetForm.email) {
						thisObj.resetForm.email_error = true;
						thisObj.resetForm.has_error = true;
					}

					if (thisObj.resetForm.has_error) {
						e.stopImmediatePropagation();
					}
				});
			},
			methods: {}
		}).mount('#{{ el_id }}');
	</script>
{% endif %}
{% schema %}
{
	"name": "Forgot Password",
	"settings": [
		{
			"type": "url",
			"id": "contact_us_url",
			"label": "Contact Us Link",
			"info": "For the success message"
		}
	],
	"presets": [{ "name": "Forgot Password" }]
}
{% endschema %}