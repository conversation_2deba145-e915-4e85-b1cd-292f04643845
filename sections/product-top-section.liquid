{%- if product.tags contains 'hidden' -%}
<script>
window.location.href = '/';
</script>
{%- else -%}

{% if product.metafields.info.main_product != blank %}
    {% assign main_product = product.metafields.info.main_product.value %}
    {% if main_product != blank %}
        <script>
        window.location.href = '{{ main_product.url }}';
        </script>
    {% endif %}
{% endif %}

{%- if product.gift_card? -%}
{{ 'datepicker.material.css' | asset_url | stylesheet_tag }}
{{ 'datepicker.js' | asset_url | script_tag }}
{% endif %}
        
{% comment %} Wrap everything that needs to be replaced by Section Rendering API {% endcomment %}
<div class="product-section-wrapper" data-section-id="{{ section.id }}">
    {{ 'product-top-section-images.css' | asset_url | stylesheet_tag }}
    {{ 'product-top-section-info.css' | asset_url | stylesheet_tag }}
    
    <div class="product-top-section bg-white">
        {%- render 'product-top-section-content', product: product -%} 
    </div>
    {% comment %} This element will be move to body {% endcomment %}
    {% comment %} The images content will be taken from product-images.liquid snippet by js {% endcomment %}
    {% comment %} <div class="product-images-popup zi-7 fixed top-0 left-0 w-full h-full hide-m"></div> {% endcomment %}
    </div>

    {%- render 'product-form-script', product: product -%}
    <script>
    // function stickyAtcAddItem(button) {
    //     const args = {
    //         qty: 1,
    //         variantId: document.getElementById('atc-btn').getAttribute('data-variant-id'),
    //         button: button
    //     };
    //     $360.addToCart(args);
    // }

    // function positionFloatingApp(type) {
    //     const tidioChatEl = document.getElementById('tidio-chat-iframe');
    //     if(!tidioChatEl) {
    //         return;
    //     }
    //     tidioChatEl.style.transition = 'all .5s cubic-bezier(.25,.46,.45,.94)';
    //     const stickyAtcHeight = document.querySelector('.sticky-atc').offsetHeight;

    //     if(type == 'up') {
    //         tidioChatEl.style.bottom = `${stickyAtcHeight}px`;
    //     } else {
    //         tidioChatEl.style.bottom = '20px';
    //     }
    // }

    function showStickyAtc() {
        const stickyAtc = document.querySelector('.sticky-atc');
        const formCta = document.querySelector('.product-form .atc-wrapper');
        const rightSide = document.querySelector('.right-side');
        const productRecommendations = document.querySelector('.product-recommendations');
        const siteFooter = document.querySelector('.site-footer');
        const recentlyViewed = document.querySelector('.recently-viewed');
        let height = document.querySelector('#site-header').clientHeight;

        function handleResize() {
            // show/hide sticky atc based Waypoint
            if(formCta) {
                new Waypoint({
                    element: formCta,
                    handler: function(direction) {
                            if(direction == 'down') {
                                stickyAtc.classList.add('active');
                            } else {
                                stickyAtc.classList.remove('active');
                            }
                        },
                    offset: '-50%'
                    });
            }
            if(siteFooter) {
                new Waypoint({
                    element: siteFooter,
                    handler: function(direction) {
                        if(direction == 'down') {
                            stickyAtc.classList.remove('active');
                        }
                    },
                });
            }

            if(productRecommendations) {
                setTimeout(() => {
                new Waypoint({
                    element: productRecommendations,
                    handler: function(direction) {
                        if(direction == 'down') {
                            if(stickyAtc.classList.contains('active')){
                                stickyAtc.classList.remove('active');
                            }
                        } else {
                            if(!stickyAtc.classList.contains('active')){
                                stickyAtc.classList.add('active');
                            }
                        }
                    },
                        // offset: '50%'
                    });
                }, 500);
            }
            if(recentlyViewed) {
                setTimeout(() => {
                new Waypoint({
                    element: recentlyViewed,
                    handler: function(direction) {
                        if(direction == 'down') {
                            if(stickyAtc.classList.contains('active')){
                                stickyAtc.classList.remove('active');
                            }
                        } 
                        
                        },
                    });
                }, 500);
            }

            // END show/hide sticky atc based Waypoint
        }
        // Attach the resize event listener
        window.addEventListener("resize", handleResize);


        // Call the function initially to handle initial page load
        handleResize();
}


    // function initAddToWishlist(){
    //     function swymCallbackFn(swat){
    //             swat.initializeActionButtons(".product-info-title-wrapper");
    //     }
    //     if(!window.SwymCallbacks){
    //     window.SwymCallbacks = [];
    //     }
    //     window.SwymCallbacks.push(swymCallbackFn);
    // }



    document.addEventListener('DOMContentLoaded', function(e) {
        // initAddToWishlist();
        showStickyAtc();

        // new Waypoint({
        //     element: document.querySelector('.product-images'),
        //     handler: function(direction) {
        //     },
        //     offset: '0%'
        // });
        
        // Set cookie for recently viewed products
        const recentlyViewedCookie = $360.getCookie('shopify_recently_viewed');
        let rcvVal = [];
        if(recentlyViewedCookie) {
            const rvcSplit = recentlyViewedCookie.split('=');
            const rcvParse = JSON.parse(rvcSplit[1]);
            if(rcvParse.length > 20) {
                // Limit up to 20 records
                rcvVal = rcvParse.slice(-20);
            } else {
                rcvVal = rcvParse;
            }
        }
        const productHandle = '{{ product.handle }}';
        if(!rcvVal.includes(productHandle)) {
            rcvVal.push('{{ product.handle }}');
            $360.setCookie('shopify_recently_viewed', JSON.stringify(rcvVal), 30, 'day');
        }
    });

    // Initialize when Shopify section is loaded
    document.addEventListener('shopify:section:load', function() {
    });

    window.addEventListener('pdp:productDataUpdated', function() {   
        // initAddToWishlist();
    });
    </script>

{% comment %} End product-section-wrapper {% endcomment %}

{%- endif -%}
{% schema %}
{
    "name": "Product Top Section",
    "settings": [
    ],
    "blocks": [{
            "type": "stock_info",
            "name": "Stock Info",
            "settings": [
                {
                    "type": "image_picker",
                    "id": "icon",
                    "label": "Icon"
                },
                {
                    "type": "richtext",
                    "id": "in_stock_text",
                    "label": "In Stock Text"
                },
                {
                    "type": "richtext",
                    "id": "out_of_stock_text",
                    "label": "Out of Stock Text"
                }
            ]
        },
        {
            "type": "info",
            "name": "Info",
            "settings": [
                {
                    "type": "image_picker",
                    "id": "icon",
                    "label": "Icon"
                },
                {
                    "type": "richtext",
                    "id": "text",
                    "label": "Text"
                }
            ]
        },
        
    ],
    "presets": [
        {
            "name": "Product Top Section",
            "blocks": []
        }
    ]
}
{% endschema %}
