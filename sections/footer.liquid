{% liquid
	assign t_color = section.settings.text_color
	assign text_color = 'c-' | append: section.settings.text_color
	assign border_color_var = 'border'
	assign border_color = 'b-border'
	if section.settings.text_color == 'black'
		assign border_color_var = 'light-grey'
		assign border_color = 'b-light-grey'
	endif
%}

<style>
#site-footer {
	overflow: hidden;
}
#site-footer .global-social a {
	width: 24px;
	height: 24px;
}
#site-footer .global-social svg {
	height: 24px;
}
#site-footer .global-social svg path {
	fill: var(--{{ t_color }});
}
#site-footer .accordion .title-icon .icon {
	width: 20px;
	height: 20px;
}
#site-footer .accordion .title-icon .icon svg path {
	stroke: var(--{{ t_color }});
}
#site-footer .bottom-content {
	width: calc(100% + 32px);
	margin-left: -16px;
}
#site-footer .images-wrapper {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	grid-column-gap: 8px;
}
#site-footer .images-wrapper.images-wrapper--desktop {
	display: none;
}
#site-footer .logo-container {
	width: calc(100% + 8px);
	margin-left: -4px;
}
#site-footer .bottom-menu a {
	padding: 0 8px;
}
#site-footer .bottom-menu a::after {
	content: '|';
	position: absolute;
	top: 50%;
	right: -2px;
	transform: translateY(-50%);
}
#site-footer .bottom-menu a:first-child {
	padding-left: 0;
}
#site-footer .bottom-menu a:last-child::after {
	display: none;
}

@media only screen and (min-width: 1024px) {
	#site-footer .right-side-cont {
		max-width: 450px;
		margin-left: auto;
	}
	#site-footer .accordion .icon-wrapper {
		display: none;
	}
	#site-footer .bottom-content {
		width: 100%;
		margin-left: 0;
	}
	#site-footer .menus-wrapper {
		width: 50%;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-column-gap: 16px;
	}
	#site-footer .images-wrapper {
		width: 50%;
		display: flex;
		justify-content: flex-end;
		column-gap: 20px;
	}
	#site-footer .images-wrapper.images-wrapper--mobile {
		display: none;
	}
	#site-footer .images-wrapper.images-wrapper--desktop {
		display: flex;
	}
	#site-footer .images-wrapper .image-container {
		width: 215px;
	}
	#site-footer .logo-container {
		width: 450px;
		margin-left: 0;
		position: absolute;
		left: -5px;
		bottom: 0;
	}
}

@media only screen and (min-width: 1440px) {
	#site-footer .logo-container {
		width: 565px;
	}
}
</style>

<footer id="site-footer" class="relative" style="background-color: {{ section.settings.bg_color }};">
	<div class="container">
		<div class="row top-content pt-36 pb-36 d-pt-48 d-pb-48 hd-pt-60 hd-pb-60 hhd-pt-72 hhd-pb-72">
			<div class="col-12 col-d-5 col-hd-5 mb-16 d-mb-0">
				{% unless section.settings.text == blank %}
					<div class="h3 {{ text_color }}">
						{{ section.settings.text | newline_to_br }}
					</div>
				{% endunless %}
			</div>

			<div class="col-12 col-d-7">
				<div class="right-side-cont">
					{% form "customer", id: "footer-newsletter", class: "ml-auto" %}
						{%- if form.posted_successfully? -%}
							<p class="p2 {{ text_color }}" data-type="success">{{ 'general.newsletter_form.confirmation' | t }}</p>
						{%- else -%}
							{%- if customer.accepts_marketing == true -%}
								<p class="p2 {{ text_color }}" data-type="accept">{{ 'general.newsletter_form.subscribed' | t }}</p>
							{%- endif -%}
						{%- endif -%}
						{%- unless customer.accepts_marketing == true or form.posted_successfully? -%}
							{%- unless form.errors == blank -%}
								<div class="p2 {{ text_color }}" data-type="errors">{{ form.errors | default_errors }}</div>
							{%- endunless -%}
							<input type="hidden" name="contact[tags]" value="newsletter" />
							<div class="flex colgap-8">
								<div class="w-full bg-white global-input">
									<input type="text" name="contact[email]" class="p2" placeholder="{{ "general.newsletter_form.email_placeholder" | t }}" />
								</div>
								<button type="submit" class="btn5">{{ "general.newsletter_form.submit" | t }}</button>
							</div>
						{%- endunless -%}
					{% endform %}

					<div class="mt-20">
						<div class="p1 bold-500">{{ 'general.social.follow_us' | t }}</div>
						{% render 'social-links', additional_class: 'colgap-12 mt-8 d-mt-12' %}
					</div>

					{% if section.settings.image_1 != blank or section.settings.image_2 != blank %}
						<div class="images-wrapper images-wrapper--mobile mt-24">
							{% for num in (1..2) %}
								{% assign image_setting ="image_" | append: num %}
								{% assign footer_image = section.settings[image_setting] %}
								{% if footer_image != blank %}
									<div class="image-container">
										{% render 'global-image-wrapper', 
											image: footer_image, 
											image_alt: 'Footer Image ' | t: number: num 
											additional_class: 'rounded-8',
										%}
									</div>
								{% endif %}
							{% endfor %}
						</div>
					{% endif %}
				</div>
			</div>
		</div>

		<div class="bottom-content d-flex d-bt-1 {{ border_color }} d-pt-48 d-pb-48 hd-pt-60 hd-pb-60 hhd-pt-72 hhd-pb-72 relative">
			<div class="menus-wrapper">
				{%- for menu in linklists[section.settings.menu].links -%}
					<div class="flex-1">
						{% capture title %}
							<p class="p7 bold-600 uppercase {{ text_color }}">{{ menu.title }}</p>
						{% endcapture %}
						{% capture desc_html %}
							<ul class="flex flex-column rowgap-12">
								{% for link in menu.links %}
									<li class="flex">
										<a href="{{ link.url }}" title="{{ link.title | escape }}" class="p2 {{ text_color }}">
											{{ link.title }}
										</a>
									</li>
								{% endfor %}
							</ul>
						{% endcapture %}
						{% assign title_padding_class = "ai-center pt-16 pb-8 pl-16 pr-16 d-pt-0 d-pb-0 d-pl-0 d-pr-0" %}
						{% assign wrapper_class = border_color | append: ' bt-1 d-bt-0 pb-4 d-pb-0' %}
						{% render "accordion",
							title: title,
							title_class: "p1",
							title_padding_class: title_padding_class,
							wrapper_class: wrapper_class,
							desc_no_pt: true,
							desc_html: desc_html,
							desc_class: "pt-0 pb-12 pl-16 pr-16 d-pt-12 d-pb-0 d-pl-0 d-pr-0",
							unlink_expand: true,
							open: true,
							vue_onclick: "this.menuTitleClick"
						%}
					</div>
				{%- endfor -%}
			</div>

			{% if section.settings.image_1 != blank or section.settings.image_2 != blank %}
				<div class="images-wrapper images-wrapper--desktop">
					{% for num in (1..2) %}
						{% assign image_setting ="image_" | append: num %}
						{% assign footer_image = section.settings[image_setting] %}
						{% if footer_image != blank %}
							<div class="image-container">
								{% render 'global-image-wrapper', 
									image: footer_image, 
									image_alt: 'Footer Image ' | t: number: num 
									additional_class: 'rounded-8',
								%}
							</div>
						{% endif %}
					{% endfor %}
				</div>
			{% endif %}

			{% if section.settings.logo != blank %}
				<div class="logo-container">
					{% render 'global-image-wrapper', 
						image: section.settings.logo, 
						image_alt: 'Footer Logo',
						additional_class: 'no-bg',
					%}
				</div>
			{% endif %}
		</div>
	</div>

	<div class="container bt-1 {{ border_color }} pt-16 pb-16">
		<div class="flex flex-wrap colgap-12 rowgap-4">
			<p class="p4 c-grey">©{{ "now" | date:'%Y' }} {{ shop.name }}. All Rights Reserved. Designed by 360&5</p>
			<div class="bottom-menu flex">
				{%- for menu in linklists[section.settings.secondary_menu].links -%}
					<a href="{{ menu.url }}" class="p4 c-grey relative">{{ menu.title }}</a>
				{%- endfor -%}
			</div>
		</div>
	</div>
</footer>

{% schema %}
{
	"name": "Footer",
	"settings": [
		{
			"type": "color",
			"id": "bg_color",
			"label": "Background Color",
			"default": "#ffffff"
		},
		{
			"type": "image_picker",
			"id": "logo",
			"label": "Logo"
		},
		{
			"type": "select",
			"id": "text_color",
			"label": "Text Color",
			"options": [
				{ "value": "black", "label": "Dark" },
				{ "value": "white", "label": "Light" }
			],
			"default": "white"
    	},
		{
			"type": "textarea",
			"id": "text",
			"label": "Text"
		},
		{
			"type": "link_list",
			"id": "menu",
			"label": "Main Menu",
			"default": "footer"
		},
		{
			"type": "link_list",
			"id": "secondary_menu",
			"label": "Secondary Menu"
		},
		{
			"type": "image_picker",
			"id": "image_1",
			"label": "Image 1"
		},
		{
			"type": "image_picker",
			"id": "image_2",
			"label": "Image 2"
		}
	]
}
  
{% endschema %}