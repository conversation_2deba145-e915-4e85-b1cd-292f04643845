{% liquid
  assign section_padding = section.settings.padding
  assign padding_class = 'sp-' | append: section_padding
  if section.settings.no_padding_top
    assign padding_class = padding_class | append: ' spt-no'
  else
    if section.settings.top_half_padding
      assign padding_class = padding_class | append: ' spt-' | append: section_padding | append: '-half'
    endif
  endif

  if section.settings.no_padding_bottom
    assign padding_class = padding_class | append: ' spb-no'
  else
    if section.settings.bottom_half_padding
      assign padding_class = padding_class | append: ' spb-' | append: section_padding | append: '-half'
    endif
  endif
%}

{% if request.visual_preview_mode %}
  <img src="{{ 'section-preview_gift-card.jpg' | asset_url }}" width="100%" height="100%" />
{% else %}

<style>
  .container {

  }
  .divider {
    background-color: var(--brown-60);
    height: 1px;
    width: 100%;
  }
  #site-header,
  #site-footer,
  #ticker-tape-ticker-tape,
  #icons-text-icons-text {
    display: none;
  }
  .logo-wrapper {
  }
  .icon-con {
    width: 180px;
  }
  .shop-btn {
    margin-top: auto;
  }
  @media only screen and (max-width: 1024px) {
    .img-col {
      padding-inline: 20px;
    }
  }
  @media only screen and (min-width: 1024px) {
    .icon-con {
      width: 250px;
    }
  }
 
</style>

<style type="text/css" media="print">
  @page {
    size: landscape;
    margin: 0;
  }
  body {
    padding-inline: 4em;
  }
  .row {
    display: flex;
    align-items: center;
  }
  .img-col {
    width: 50%;
  }
  .text-col {
    width: 50%;
  }
  .text-col {
    padding-left: 6.6667%;
  }
  .print-only {
    display: block;
  }

  @media (max-width: 8.5in) {
    .global-content-top-margin {
      margin-top: 6em !important;
    }
    .site-header {
      top: 1em !important;
    }
  }

  @media (min-width: 8.5in) and (max-width: 11in) {
    .global-content-top-margin {
      margin-top: 5em !important;
    }
  }

  @media (min-width: 11in) {
    .global-content-top-margin {
      margin-top: 4em !important;
    }
  }
  .header-con__inner {
    justify-content: center;
  }
  .logo-main {
    display: block !important;
  }
  .announcement-bar,
  #site-footer,
  .header-search-form,
  .btn1,
  .print-btn,
  .header-nav,
  .header-right,
  .logo-white,
  .logo-mark,
  #ticker-tape-ticker-tape {
    display: none !important;
  }
</style>

{% comment %}
  GIFT CARD PREVIEW VALUES

  This section uses request.design_mode to automatically show preview values when viewed in the theme editor.

  When in design mode, the following gift card values will be displayed:
  - Recipient name: "John Doe"
  - Message: "Happy Birthday! Enjoy your new riding gear."
  - Sender name: "Jane Smith"
  - Gift card code: "1234-5678-9012-3456"
  - Expiration date: "2024-12-31"
  - Balance: "$100.00"

  The actual gift_card object structure is:
  gift_card = {
    "recipient": {
      "name": "John Doe"
    },
    "message": "Happy Birthday! Enjoy your new riding gear.",
    "customer": {
      "name": "Jane Smith"
    },
    "code": "1234-5678-9012-3456",
    "enabled": true,
    "expired": false,
    "expires_on": "2024-12-31",
    "balance": 100.00,
    "initial_value": 100.00
  }
{% endcomment %}

{% comment %}
  Using request.design_mode to automatically show preview values in the theme editor
{% endcomment %}

{% comment %}
  Find the purchased gift card's variant
  based on its initial value.
{% endcomment %}

{% assign gift_card_product = gift_card.product %}

{% if gift_card_product %}
  {% assign current_gift_card_variant = null %}

  {% for variant in gift_card_product.variants %}
    {% if variant.price == gift_card.initial_value %}
      {% assign current_gift_card_variant = variant %}
      {% break %} 
    {% endif %}
  {% endfor %}

{% endif %}

{% assign preview_mode = request.design_mode %}
{% if preview_mode %}
  {% capture recipient_name %}John Doe{% endcapture %}
  {% capture message %}Happy Birthday! Enjoy your new riding gear.{% endcapture %}
  {% capture customer_name %}Jane Smith{% endcapture %}
  {% capture code %}1234-5678-9012-3456{% endcapture %}
  {% capture expires_on %}2024-12-31{% endcapture %}
  {% capture balance %}$100.00{% endcapture %}
{% else %}
  {% assign recipient_name = '' %}
  {% assign message = '' %}
  {% assign customer_name = '' %}
  {% assign code = '' %}
  {% assign expires_on = '' %}
  {% assign balance = '' %}
{% endif %}

<div class="logo-wrapper flex jc-center pt-16 pb-16 d-pt-20 d-pb-20">
  {%- if section.settings.logo == blank -%}
    <p class="p1">{{ shop.name }}</p>
  {%- else -%}
    <div class="icon-con">
      {%- render 'global-image-wrapper',
        image: section.settings.logo,
        image_alt: shop.name,
        additional_class: 'logo-main no-bg',
        preload: true
      -%}
    </div>
  {%- endif -%}
</div>

<div class="{{ padding_class }}">
  <div class="d-pl-56 d-pr-56 hd-max-1200 d-mx-auto flex flex-column d-flex-row d-colgap-16 d-ai-stretch">
    <div class="img-col col-12 col-d-6 mb-16 d-pl-0 d-pr-0 d-mb-0">   
      {% if current_gift_card_variant %}
        {% render 'global-image-wrapper', image: current_gift_card_variant.image, image_alt: current_gift_card_variant.title | escape, additional_class: 'rounded-12' %}
      {% else %}
        {% render 'global-image-wrapper', image: section.settings.image, image_alt: 'Gift Card Illustration', additional_class: 'rounded-12' %}
      {% endif %}
    </div>
    <div class="text-col bg-brown rounded-tl-16 rounded-tr-16 d-rounded-12 col-12 col-d-6 pl-20 pr-20 pt-32 pb-32 d-p-40 flex flex-column">
      <div class="quote flex flex-column">
        {% if gift_card.recipient.name != blank or preview_mode %}
          <p class="p2 c-white mb-24">
            {{ 'gift_cards.issued.hi' | t }} {% if preview_mode %}{{ recipient_name }}{% else %}{{ gift_card.recipient.nickname }}{% endif %},
          </p>
        {% endif %}


        {% if gift_card.customer.name != blank or preview_mode %}
          <div class="h4 c-white capitalize">
            {% if preview_mode %}
              {{ 'gift_cards.issued.your_have_received_from' | t: sender_name: customer_name }}
            {% else %}
              {{ 'gift_cards.issued.your_have_received_from' | t: sender_name: gift_card.customer.name }}
            {% endif %}
          </div>
        {% endif %}

        {% if gift_card.message != blank or preview_mode %}
          <div class="p1 c-white mt-16">
            "{% if preview_mode %}{{ message }}{% else %}{{ gift_card.message }}{% endif %}"
          </div>
        {% endif %}

      </div>

      <div class="divider mt-24 mb-24"></div>

      {% if gift_card.balance != blank or preview_mode %}
        <div class="h5 c-white mb-16">
          {% if gift_card.balance != blank %}
            {{ 'gift_cards.issued.remaining_html' | t: balance: gift_card.balance | money }}
          {% else %}
            {{ 'gift_cards.issued.remaining_html' | t: balance: balance | money }}
          {% endif %}
        </div>
      {% endif %}

      <div class="p2 c-white mb-12">{{ 'gift_cards.issued.redeem_html' | t }}</div>

      <div
        class="no-print bg-white relative p-12 w-full c-brown mb-24 rounded-6"
        onclick="selectText('gift-card-code-digits');"
      >
        <div id="gift-card-code-digits" class="uppercase p2">
          {% if preview_mode %}{{ code }}{% else %}{{ gift_card.code | format_code }}{% endif %}
        </div>
        <div class="copy-icon absolute pl-20 top-12 right-12">{{ settings.icon_copy }}</div>
      </div>

      {% unless gift_card.enabled %}
        <div class="p4 c-brown-40 mb-32">{{ 'gift_cards.issued.disabled' | t }}</div>
      {% endunless %}

      {% if gift_card.expired and gift_card.enabled %}
        <div class="p4 c-brown-40 mb-32">
          {{ 'gift_cards.issued.expired' | t }}
          <span >{{ gift_card.expires_on | date: '%d/%m/%y' }}</span>
        </div>
      {% endif %}

      {% if preview_mode %}
        <div class="p4 c-brown-40 mb-32">
          {{ 'gift_cards.issued.active' | t }} <span>{{ expires_on | date: '%d/%m/%y' }}</span>
        </div>
      {% endif %}

      {% if gift_card.expired != true and gift_card.expires_on and gift_card.enabled or preview_mode %}
        <div class="p4 c-brown-40 mb-32">
          {{ 'gift_cards.issued.active' | t }}
          <span>
            {%- if preview_mode -%}
              {{- expires_on | date: '%d/%m/%y' -}}
            {%- else -%}
              {{- gift_card.expires_on | date: '%d/%m/%y' -}}
            {%- endif -%}
          </span>
        </div>
      {% endif %}

      <div class="shop-btn flex flex-column d-flex-row rowgap-12 d-rowgap-0 colgap-12 mb-0">
        <a href="{{ shop.url }}" class="btn1 no-icon w-full tc">{{ 'layout.cart.go_shopping' | t }}</a>

        {% comment %} <a href="#" ref="nofollow" class="print-btn btn3 no-icon w-full tc" onclick="window.print();">
          {{- 'gift_cards.issued.print' | t -}}
        </a> {% endcomment %}
      </div>

    </div>
  </div>
</div>

<script type="text/javascript">
  function selectText(element) {
    var doc = document;
    var text = doc.getElementById(element);

    if (doc.body.createTextRange) {
      // ms
      var range = doc.body.createTextRange();
      range.moveToElementText(text);
      range.select();
    } else if (window.getSelection) {
      // moz, opera, webkit
      var selection = window.getSelection();
      var range = doc.createRange();
      range.selectNodeContents(text);
      selection.removeAllRanges();
      selection.addRange(range);
    }
  }
</script>

{% endif %}

{% schema %}
{
  "name": "Gift Card",
  "settings": [
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Logo"
    },
    {
      "type": "header",
      "content": "Section Content Padding"
    },
    {
      "type": "select",
      "id": "padding",
      "label": "Padding",
      "options": [
        {
          "value": "no",
          "label": "No"
        },
        {
          "value": "sm",
          "label": "Small"
        },
        {
          "value": "md",
          "label": "Med"
        },
        {
          "value": "lg",
          "label": "Large"
        }
      ],
      "default": "no"
    },
    {
      "type": "checkbox",
      "id": "top_half_padding",
      "label": "Top half padding",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "no_padding_top",
      "label": "No padding top",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "bottom_half_padding",
      "label": "Bottom half padding",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "no_padding_bottom",
      "label": "No padding bottom",
      "default": false
    },
    {
      "type": "header",
      "content": "General Settings"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "This image will be used if no variant is found for the gift card product."
    }
  ],
  "presets": [
    {
      "name": "Gift Card"
    }
  ]
}
{% endschema %}
