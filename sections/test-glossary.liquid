{%- assign section_name = 'Test Glossary' -%}
{%- assign el_id = section_name | handle | append: '-' | append: section.id -%}
<style>
#{{ el_id }} .btn-item,
#{{ el_id }} .link-item,
#{{ el_id }} .text-item {
    width: calc(50% - 10px);
}
@media screen and (min-width: 1024px){
    #{{ el_id }} .btn-item,
    #{{ el_id }} .link-item,
    #{{ el_id }} .text-item {
        width: calc(33.33% - 13.33px);
    }
}
</style>

<div id="{{ el_id }}" class="relative">
    <div class="relative pt-40 pb-40">
        <div class="container">
            <p class="p1 bold-700 bb-1 b-border">Button Glossary</p>
            <div class="container-max mx-auto flex jc-start ai-start flex-wrap colgap-20 rowgap-20 mt-40">
                {% for item in (1..6) %}
                <div class="col-6 col-d-4 btn-item">
                    <button class="btn{{ forloop.index }}">Button {{ forloop.index }}</button>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    <div class="container mt-80">
        <p class="p1 bold-700 bb-1 b-border">Link Glossary</p>
        <div class="container-max mx-auto flex jc-start ai-start flex-wrap colgap-20 rowgap-20 mt-40">
            {% for item in (1..4) %}
            <div class="col-6 col-d-3 link-item">
                <a href="#" class="link{{ forloop.index }}">Link {{ forloop.index }}</a>
            </div>
            {% endfor %}
        </div>
    </div>
    <div class="container mt-80">
        <p class="p1 bold-700 bb-1 b-border">Heading Glossary</p>
        <div class="container-max mx-auto flex jc-start ai-start flex-wrap rowgap-20 mt-40">
            {% for item in (1..8) %}
            <div class="col-12">
                <h{{ forloop.index }} class="h{{ forloop.index }}">Heading {{ forloop.index }}</h{{ forloop.index }}>
            </div>
            {% endfor %}
        </div>
    </div>
    <div class="container mt-80">
        <p class="p1 bold-700 bb-1 b-border">Body Copy Glossary</p>
        <div class="container-max mx-auto flex jc-start ai-start flex-wrap rowgap-20 mt-40">
            {% for item in (1..5) %}
            <div class="col-12">
                <p class="p{{ forloop.index }}">Body copy {{ forloop.index }}</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

{% schema %}
    {
        "name": "Test Glossary",
        "presets": [
            { 
                "name": "Test Glossary",
                "blocks" : []
            }
        ]
    }
{% endschema %}
