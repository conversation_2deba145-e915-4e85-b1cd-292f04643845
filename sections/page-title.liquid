{% liquid
    assign section_name = 'Page Title'
    assign el_id = section_name | handle | append: '-' | append: section.id

    assign section_padding = section.settings.padding
    assign padding_class = 'sp-' | append: section_padding
    
    if section.settings.no_padding_top
        assign padding_class = padding_class | append: ' spt-no'
    else
        if section.settings.top_half_padding
            assign padding_class = padding_class | append: ' spt-' | append: section_padding | append: '-half'
        endif
    endif

    if section.settings.no_padding_bottom
        assign padding_class = padding_class | append: ' spb-no'
    else
        if section.settings.bottom_half_padding
            assign padding_class = padding_class | append: ' spb-' | append: section_padding | append: '-half'
        endif
    endif

    assign has_content = false
    if section.settings.title != blank or section.settings.text != blank or section.settings.text_richtext != blank
        assign has_content = true
    endif

    assign bg_color = section.settings.bg_color | default: '#f9f8f4'
%}

{% if request.visual_preview_mode %}
    <img src="{{ "section-preview-page-title.png" | asset_url }}" width="100%" height="100%" />
{% else %}
{%- if has_content == true -%}
<style>
#{{ el_id }} .breadcrumb-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
}
#{{ el_id }} .title-wrp {
    max-width: {{ section.settings.title_w_m }}px;
}
#{{ el_id }} .text-wrp {
    max-width: {{ section.settings.text_w_m }}px;
}
#{{ el_id }} .title-wrp.light,
#{{ el_id }} .text-wrp.light {
    color: var(--white);
}
#{{ el_id }} .title-wrp.dark,
#{{ el_id }} .text-wrp.dark {
    color: var(--black);
}
#{{ el_id }} .breadcrumb-wrapper.light .breadcrumb .bc-item {
    color: var(--white);
}
#{{ el_id }} .breadcrumb-wrapper.dark .breadcrumb .bc-item {
    color: var(--grey);
}
@media only screen and (min-width: 600px){
    #{{ el_id }} .title-wrp {
        max-width: {{ section.settings.title_w_t }}px;
    }
    #{{ el_id }} .text-wrp {
        max-width: {{ section.settings.text_w_t }}px;
    }
}
@media only screen and (min-width: 1024px){
    #{{ el_id }} .title-wrp {
        max-width: {{ section.settings.title_w_d }}px;
    }
    #{{ el_id }} .text-wrp {
        max-width: {{ section.settings.text_w_d }}px;
    }
}
</style>
<div id="{{ el_id }}" class="relative overflow {{ padding_class }}" style="background-color: {{ bg_color }};">
    {%- if section.settings.show_breadcrumbs -%}
        {% render 'breadcrumbs', wrapper_class: section.settings.text_color %}
    {%- endif -%}
    <div class="relative container">
        {%- if section.settings.title != blank -%}
            <{{section.settings.title_element }} class="relative title-wrp {{ section.settings.title_size }} {{ section.settings.text_color }} c-brown-100 tc mx-auto">{{ section.settings.title | newline_to_br }}</{{section.settings.title_element }}>
        {%- endif -%}

        {%- if section.settings.text != blank or section.settings.text_richtext != blank -%}
            <div class="relative text-wrp tc mx-auto p2 {{ section.settings.text_color }} {% if section.settings.text_input_type == 'richtext' %}rte{% endif %} mt-12 d-mt-20">
                {% if section.settings.text_input_type == 'richtext' %}
                    {{ section.settings.text_richtext }}
                {% else %}
                    {{ section.settings.text | newline_to_br }}
                {% endif %}
            </div>
        {%- endif -%}
    </div>
</div>
{%- endif -%}
{%- endif -%}

{% schema %}
{
	"name": "Page Title",
	"settings": [
		{
			"type": "header",
			"content": "General Settings"
		},
        {
            "type": "checkbox",
            "id": "show_breadcrumbs",
            "label": "Show Breadcrumbs",
            "default": true
        },
        {
			"type": "color",
			"id": "bg_color",
			"label": "Background Color",
            "default": "#f9f8f4"
		},
        {
            "type": "select",
            "id": "text_color",
            "label": "Text Color",
            "options": [
                {
                    "value": "dark",
                    "label": "Dark"
                },
                {
                    "value": "light",
                    "label": "Light"
                }
            ],
            "default": "dark"
        },
		{
			"type": "text",
			"id": "title",
			"label": "Title"
		},
        {
            "type": "select",
            "id": "title_element",
            "label": "Title Element",
            "options": [
                {
                    "value": "h1",
                    "label": "H1"
                }, {
                    "value": "h2",
                    "label": "H2"
                }, {
                    "value": "h3",
                    "label": "H3"
                }, {
                    "value": "h4",
                    "label": "H4"
                },
                {
                    "value": "h5",
                    "label": "H5"
                },
                {
                    "value": "h6",
                    "label": "H6"
                }
            ],
            "default": "h1"
        },
        {
            "type": "select",
            "id": "title_size",
            "label": "Title Font Size",
            "options": [
                {
                    "value": "h1",
                    "label": "h1"
                },
                {
                    "value": "h2",
                    "label": "h2"
                },
                {
                    "value": "h3",
                    "label": "h3"
                },
                {
                    "value": "h4",
                    "label": "h4"
                },
                {
                    "value": "h5",
                    "label": "h5"
                },
                {
                    "value": "h6",
                    "label": "h6"
                },
                {
                    "value": "p1",
                    "label": "P1"
                },
                {
                    "value": "p2",
                    "label": "P2"
                },
                {
                    "value": "p3",
                    "label": "P3"
                },
                {
                    "value": "p4",
                    "label": "P4"
                },
                {
                    "value": "p5",
                    "label": "P5"
                },
                {
                    "value": "p6",
                    "label": "P6"
                },
                {
                    "value": "p7",
                    "label": "P7"
                }
            ],
            "default": "h3"
        },
        {
            "type": "select",
            "id": "text_input_type",
            "label": "Text Input Type",
            "options": [
                {
                    "value": "textarea",
                    "label": "Textarea"
                },
                {
                    "value": "richtext",
                    "label": "Rich Text"
                }
            ],
            "default": "textarea"
        },
        {
			"type": "textarea",
			"id": "text",
			"label": "Text",
            "visible_if": "{{ section.settings.text_input_type == 'textarea' }}"
		},
        {
			"type": "richtext",
			"id": "text_richtext",
			"label": "Rich Text",
            "visible_if": "{{ section.settings.text_input_type == 'richtext' }}"
		},
        {
			"type": "header",
			"content": "Title Text Width"
		},
        {
			"type": "number",
			"id": "title_w_m",
			"label": "Title Width for Mobile",
            "default": 720
		},
        {
			"type": "number",
			"id": "title_w_t",
			"label": "Title Width for Tablet",
            "default": 720
		},
        {
			"type": "number",
			"id": "title_w_d",
			"label": "Title Width for Desktop",
            "default": 720
		},
        {
			"type": "header",
			"content": "Long Text Width"
		},
        {
			"type": "number",
			"id": "text_w_m",
			"label": "Text Width for Mobile",
            "default": 720
		},
        {
			"type": "number",
			"id": "text_w_t",
			"label": "Text Width for Tablet",
            "default": 720
		},
        {
			"type": "number",
			"id": "text_w_d",
			"label": "Text Width for Desktop",
            "default": 720
		},
        {
			"type": "header",
			"content": "Section Padding"
		},
		{
            "type": "select",
            "id": "padding",
            "label": "Padding",
            "options": [
                {
                    "value": "no",
                    "label": "No"
                },
                {
                    "value": "sm",
                    "label": "Small"
                },
                {
                    "value": "md",
                    "label": "Med"
                },
                {
                    "value": "lg",
                    "label": "Large"
                }
            ],
            "default": "md"
        },
        {
            "type": "checkbox",
            "id": "top_half_padding",
            "label": "Top half padding",
            "default": false
        },
        {
            "type": "checkbox",
            "id": "no_padding_top",
            "label": "No padding top",
            "default": false
        },
        {
            "type": "checkbox",
            "id": "bottom_half_padding",
            "label": "Bottom half padding",
            "default": false
        },
        {
            "type": "checkbox",
            "id": "no_padding_bottom",
            "label": "No padding bottom",
            "default": false
        }
	],
	"presets": [
		{
			"name": "Page Title",
			"settings": {
				"show_breadcrumbs": true,
				"title": "Page Title",
				"title_element": "h1",
				"title_size": "h2",
				"text_input_type": "textarea",
				"text": "Welcome to our page. Here you'll find everything you need to know about our products and services.",
				"text_color": "dark",
				"bg_color": "#f9f8f4",
				"padding": "md",
				"title_w_m": 720,
				"title_w_t": 720,
				"title_w_d": 720,
				"text_w_m": 720,
				"text_w_t": 720,
				"text_w_d": 720
			}
		}
	]
}
{% endschema %}
