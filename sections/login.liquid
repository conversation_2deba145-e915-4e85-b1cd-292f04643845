{% if customer %}
	<script>window.location.href = '/account'</script>
{% else %}
	{%- assign el_id = 'login-' | append: section.id -%}
	<style>
		@media only screen and (min-width:1024px) {
			#{{ el_id }} {
				height: 100vh;
			}
		}
	</style>
	<div id="{{ el_id }}">
		<div class="container h-full">
			<div class="row h-full flex flex-wrap">
				<div class="col-12 col-d-6 flex ai-center bb-1 d-bb-0 d-br-1 b-border sp-md">
					<div class="flex jc-center w-full">
						<div id="login-div" class="max-440 w-full">
							{%- form 'customer_login', id: 'customer_login' -%}
								<h1 class="h4 tc">{{ 'customer.login.title' | t }}</h1>

								<div class="mt-16 d-mt-20">
									{%- if form.errors -%}
									<div class="form-errors global-error-msg mb-16 d-mb-20 rte p2">
										{{ form.errors | default_errors }}
									</div>
									{%- endif -%}

									<div class="mb-8">
										<div class="global-input">
											<input type="email" v-model="loginForm.email" name="customer[email]" id="email" 
											class="global-input-account p2" :class="{'global-input-account-error': loginForm.email_error}"
											placeholder="{{ 'customer.login.email' | t }}" />
										</div>
										<div class="global-error-msg p2 mt-8" :class="{'hide-m': !loginForm.email_error}">{{ 'customer.login.error_email' | t }}</div>
									</div>
									<div class="mb-12 d-mb-20">
										<div class="password-field global-input relative">
											<input type="password" v-model="loginForm.password" name="customer[password]" id="password"
											class="global-input-account p2" :class="{'global-input-account-error': loginForm.password_error}" 
											placeholder="{{ 'customer.login.password' | t }}" />
											<a class="absolute icon-password">
												<span class="icon-show">
													{{ settings.icon_show_password }}
												</span>
												<span class="icon-hide hidden">
													{{ settings.icon_hide_password }}
												</span>
											</a>
										</div>
										<div class="global-error-msg p2 mt-8" :class="{'hide-m': !loginForm.password_error}">{{ 'customer.login.error_password' | t }}</div>
									</div>
									<div class="button-container tc">
										<input type="hidden" name="return_to" value="{{ routes.account_url }}">
										<button type="submit" class="btn2 w-full mb-8">{{ 'customer.login.submit' | t }}</button>
										<a href="/pages/forgot-password" class="underline p2 c-grey">
											{{ 'customer.login.forgot_password' | t }}
										</a>
									</div>
								</div>
							{%- endform -%}
						</div>
					</div>
				</div>
				<div class="col-12 col-d-6 flex ai-center sp-md">
					<div class="flex jc-center w-full">
						<div id="register-text" class="max-440 w-full">
							<div class="tc mb-20">
								<h2 class="h4">{{ 'customer.register.title' | t }}</h2>
								<p class="p2">{{ 'customer.register.sub_title' | t }}</p>
							</div>
							<div class="flex colgap-32 d-pl-28 d-pr-28">
								<div class="point-1 tc">
									<i class="icon-point mb-8">
										<svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
											<path d="M15.3337 2.49805L15.8727 4.89005C16.0972 5.88373 16.5986 6.79346 17.319 7.5138C18.0393 8.23414 18.9491 8.7356 19.9427 8.96005L22.3337 9.49805L19.9417 10.037C18.9481 10.2615 18.0383 10.763 17.318 11.4833C16.5976 12.2036 16.0962 13.1134 15.8717 14.107L15.3337 16.498L14.7947 14.106C14.5703 13.1124 14.0688 12.2026 13.3485 11.4823C12.6282 10.762 11.7184 10.2605 10.7247 10.036L8.33374 9.49805L10.7257 8.95905C11.7194 8.7346 12.6292 8.23314 13.3495 7.5128C14.0698 6.79246 14.5713 5.88273 14.7957 4.88905L15.3337 2.49805ZM7.33374 12.498L7.71874 14.206C7.87907 14.9158 8.23725 15.5655 8.75175 16.08C9.26625 16.5945 9.91601 16.9527 10.6257 17.113L12.3337 17.498L10.6257 17.883C9.91601 18.0434 9.26625 18.4016 8.75175 18.9161C8.23725 19.4306 7.87907 20.0803 7.71874 20.79L7.33374 22.498L6.94874 20.79C6.78841 20.0803 6.43024 19.4306 5.91574 18.9161C5.40123 18.4016 4.75147 18.0434 4.04174 17.883L2.33374 17.498L4.04174 17.113C4.75147 16.9527 5.40123 16.5945 5.91574 16.08C6.43024 15.5655 6.78841 14.9158 6.94874 14.206L7.33374 12.498Z" fill="#E8A5EA" stroke="#E8A5EA" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
										</svg>
									</i>
									<p class="p2">{{ 'customer.register.point_1' | t }}</p>
								</div>
								<div class="point-2 tc">
									<i class="icon-point mb-8">
										<svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
											<path d="M15.3337 2.49805L15.8727 4.89005C16.0972 5.88373 16.5986 6.79346 17.319 7.5138C18.0393 8.23414 18.9491 8.7356 19.9427 8.96005L22.3337 9.49805L19.9417 10.037C18.9481 10.2615 18.0383 10.763 17.318 11.4833C16.5976 12.2036 16.0962 13.1134 15.8717 14.107L15.3337 16.498L14.7947 14.106C14.5703 13.1124 14.0688 12.2026 13.3485 11.4823C12.6282 10.762 11.7184 10.2605 10.7247 10.036L8.33374 9.49805L10.7257 8.95905C11.7194 8.7346 12.6292 8.23314 13.3495 7.5128C14.0698 6.79246 14.5713 5.88273 14.7957 4.88905L15.3337 2.49805ZM7.33374 12.498L7.71874 14.206C7.87907 14.9158 8.23725 15.5655 8.75175 16.08C9.26625 16.5945 9.91601 16.9527 10.6257 17.113L12.3337 17.498L10.6257 17.883C9.91601 18.0434 9.26625 18.4016 8.75175 18.9161C8.23725 19.4306 7.87907 20.0803 7.71874 20.79L7.33374 22.498L6.94874 20.79C6.78841 20.0803 6.43024 19.4306 5.91574 18.9161C5.40123 18.4016 4.75147 18.0434 4.04174 17.883L2.33374 17.498L4.04174 17.113C4.75147 16.9527 5.40123 16.5945 5.91574 16.08C6.43024 15.5655 6.78841 14.9158 6.94874 14.206L7.33374 12.498Z" fill="#E8A5EA" stroke="#E8A5EA" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
										</svg>
									</i>
									<p class="p2">{{ 'customer.register.point_2' | t }}</p>
								</div>
								<div class="point-3 tc">
									<i class="icon-point mb-8">
										<svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
											<path d="M15.3337 2.49805L15.8727 4.89005C16.0972 5.88373 16.5986 6.79346 17.319 7.5138C18.0393 8.23414 18.9491 8.7356 19.9427 8.96005L22.3337 9.49805L19.9417 10.037C18.9481 10.2615 18.0383 10.763 17.318 11.4833C16.5976 12.2036 16.0962 13.1134 15.8717 14.107L15.3337 16.498L14.7947 14.106C14.5703 13.1124 14.0688 12.2026 13.3485 11.4823C12.6282 10.762 11.7184 10.2605 10.7247 10.036L8.33374 9.49805L10.7257 8.95905C11.7194 8.7346 12.6292 8.23314 13.3495 7.5128C14.0698 6.79246 14.5713 5.88273 14.7957 4.88905L15.3337 2.49805ZM7.33374 12.498L7.71874 14.206C7.87907 14.9158 8.23725 15.5655 8.75175 16.08C9.26625 16.5945 9.91601 16.9527 10.6257 17.113L12.3337 17.498L10.6257 17.883C9.91601 18.0434 9.26625 18.4016 8.75175 18.9161C8.23725 19.4306 7.87907 20.0803 7.71874 20.79L7.33374 22.498L6.94874 20.79C6.78841 20.0803 6.43024 19.4306 5.91574 18.9161C5.40123 18.4016 4.75147 18.0434 4.04174 17.883L2.33374 17.498L4.04174 17.113C4.75147 16.9527 5.40123 16.5945 5.91574 16.08C6.43024 15.5655 6.78841 14.9158 6.94874 14.206L7.33374 12.498Z" fill="#E8A5EA" stroke="#E8A5EA" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
										</svg>
									</i>
									<p class="p2">{{ 'customer.register.point_3' | t }}</p>
								</div>
							</div>
							<a href="{{ routes.account_register_url }}" class="btn2 tc w-full mt-20">
								Register Now
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<script>
		Vue.createApp({
			delimiters: ['${', '}'],
			data() {
				return {
					loginForm: {
						email: null,
						password: null,
						email_error: false,
						password_error: false,
						has_error: false
					},
					showSuccess: false,
					showReset: false,
					showLogin: true,
				}
			},
			mounted() {
				const thisObj = this;
				document.getElementById('customer_login').addEventListener('submit', (e) => {
					e.preventDefault();

					thisObj.loginForm.has_error = false;
					thisObj.loginForm.email_error = false;
					thisObj.loginForm.password_error = false;

					if (!thisObj.loginForm.email) {
						thisObj.loginForm.email_error = true;
						thisObj.loginForm.has_error = true;
					}
					if (!thisObj.loginForm.password) {
						thisObj.loginForm.password_error = true;
						thisObj.loginForm.has_error = true;
					}

					if (thisObj.loginForm.has_error) {
						e.stopImmediatePropagation();
					}
				});
			},
			methods: {}
		}).mount('#login-div');
	</script>
{% endif %}
{% schema %}
	{
		"name": "Login",
		"settings": [],
		"presets": [{ "name": "Login" }]
	}
{% endschema %}