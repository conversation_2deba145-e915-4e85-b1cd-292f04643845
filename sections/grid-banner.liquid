{% liquid
    assign section_name = '<PERSON>rid Banner'
    assign el_id = section_name | handle | append: '-' | append: section.id

    assign section_padding = section.settings.padding
    assign padding_class = 'sp-' | append: section_padding
    
    if section.settings.no_padding_top
        assign padding_class = padding_class | append: ' spt-no'
    else
        if section.settings.top_half_padding
            assign padding_class = padding_class | append: ' spt-' | append: section_padding | append: '-half'
        endif
    endif

    if section.settings.no_padding_bottom
        assign padding_class = padding_class | append: ' spb-no'
    else
        if section.settings.bottom_half_padding
            assign padding_class = padding_class | append: ' spb-' | append: section_padding | append: '-half'
        endif
    endif
%}
{{ 'splide.min.js' | asset_url | script_tag }}
{{ 'splide.min.css' | asset_url | stylesheet_tag }}
{{ 'splide-core.min.css' | asset_url | stylesheet_tag }}

<style>
    #{{ el_id }} .banner-image {
        border-radius: 6px;
        overflow: hidden;
    }
    #{{ el_id }} .splide .splide__arrows,
    #{{ el_id }} .splide .splide__pagination {
        display: none;
    }
    #{{ el_id }} .splide {
        width: calc(100% + 32px);
        margin-left: -16px;
    }
    
    #{{ el_id }} .logo {
        bottom: -10px;
    }
    @media only screen and (min-width: 600px) {
        #{{ el_id }} .logo {
            bottom: -20px;
        }

        #{{ el_id }} .splide {
            width: calc(100% + 40px);
            margin-left: -20px;
        }
    }
    @media only screen and (min-width: 1024px) {
        #{{ el_id }} .logo {
            bottom: -30px;
        }
        #{{ el_id }} .banner-image {
            border-radius: 8px;
        }

        #{{ el_id }} .splide {
            width: 100%;
            margin-left: 0;
            padding-left: 0;
        }
    }
    @media only screen and (min-width: 1440px) {
        #{{ el_id }} .logo {
            bottom: -40px;
        }
    }
</style>
<section id="{{ el_id }}" class="{{ padding_class }}">
    <div class="container">
        <div class="row">
            <div class="col-12 col-d-10 col-hd-9">
                {%- if section.settings.logo_image -%}
                    {% render 'global-image-wrapper',
                        image: section.settings.logo_image,
                        image_alt: section_name | escape,
                        additional_class: 'no-bg logo',
                        preload: true
                    %}
                {%- else -%}
                    {{ 'lifestyle-1' | placeholder_svg_tag }}
                {%- endif -%}
            </div>
        </div>
        <div class="splide">
            <div class="splide__slider">
                <div class="splide__track">
                    <div class="splide__list">
                        {%- for block in section.blocks -%}
                            <div class="splide__slide">
                                <a href="{{ block.settings.link }}" class="banner-wrapper">
                                    <div class="banner-image mb-12{% if block.settings.image == blank %} bg-grey{% endif %}">
                                        {% if block.settings.image_d %}
                                            {%- assign image_id = block.id | append: '-image' -%}
                                            {% render 'global-image-wrapper-responsive',
                                                image_id: image_id,
                                                mobile_image: block.settings.image_m,
                                                desktop_image: block.settings.image_d,
                                                image_alt: block.settings.title | escape
                                            %}
                                        {% else %}
                                            {{ 'image' | placeholder_svg_tag }}
                                        {% endif %}
                                    </div>
                                    <div class="banner-content">
                                        <h2 class="h7 mb-4">{{ block.settings.title }}</h2>
                                        <p class="p2">{{ block.settings.text }}</p>
                                    </div>
                                </a>
                            </div>
                        {%- endfor -%}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<script>
    const splide_{{ section.index }} = new Splide('#{{ el_id }} .splide', {
        perPage: 1,
        fixedWidth: '260px',
        gap: '12px',
        padding: { left: 16, right: 16 },
        mediaQuery: 'min',
        breakpoints: {
            600: { perPage: 2, fixedWidth: '230px', gap: '12px', padding: { left: 20, right: 20 } },
            1024: { perPage: 3, fixedWidth: false, gap: '16px', padding: { left: 0, right: 0 } }
        }
    });

    document.addEventListener('DOMContentLoaded', () => {
        splide_{{ section.index }}.mount();
    });
    document.addEventListener('shopify:section:load', () => {
        new Splide('#{{ el_id }} .splide', {
            perPage: 1,
            fixedWidth: '260px',
            gap: '12px',
            padding: { left: 16, right: 16 },
            mediaQuery: 'min',
            breakpoints: {
                600: { perPage: 2, fixedWidth: '300px', gap: '12px', padding: { left: 32, right: 32 } },
                1024: { perPage: 3, fixedWidth: false, gap: '16px', padding: { left: 0, right: 0 } }
            }
        }).mount();
    });
</script>
{% schema %}
    {
        "name": "Grid Banner",
        "blocks": [
            {
                "name": "Banner",
                "type": "banner",
                "settings": [
                    {
                        "type": "image_picker",
                        "id": "image_m",
                        "label": "Image (Mobile)"
                    },
                    {
                        "type": "image_picker",
                        "id": "image_d",
                        "label": "Image (Desktop)"
                    },
                    {
                        "type": "text",
                        "id": "title",
                        "label": "Title"
                    },
                    {
                        "type": "text",
                        "id": "text",
                        "label": "Text"
                    },
                    {
                        "type": "url",
                        "id": "link",
                        "label": "Link"
                    }
                ]
            }
        ],
        "max_blocks": 3,
        "settings": [
            {
                "type": "header",
                "content": "General Settings"
            },
            {
                "type": "image_picker",
                "id": "logo_image",
                "label": "Logo Image"
            },
            {
                "type": "header",
                "content": "Section Padding"
            },
            {
                "type": "select",
                "id": "padding",
                "label": "Padding",
                "options": [
                    {
                        "value": "no",
                        "label": "No"
                    },
                    {
                        "value": "xs",
                        "label": "Extra Small"
                    },
                    {
                        "value": "sm",
                        "label": "Small"
                    },
                    {
                        "value": "md",
                        "label": "Med"
                    },
                    {
                        "value": "lg",
                        "label": "Large"
                    }
                ],
                "default": "md"
            },
            {
                "type": "checkbox",
                "id": "top_half_padding",
                "label": "Top half padding",
                "default": false
            },
            {
                "type": "checkbox",
                "id": "no_padding_top",
                "label": "No padding top",
                "default": false
            },
            {
                "type": "checkbox",
                "id": "bottom_half_padding",
                "label": "Bottom half padding",
                "default": false
            },
            {
                "type": "checkbox",
                "id": "no_padding_bottom",
                "label": "No padding bottom",
                "default": false
            }
        ],
        "presets": [
            { 
                "name": "Grid Banner",
                "blocks": [
                    {
                        "type": "banner",
                        "settings": {
                            "title": "Simple Grid Banner Title 1",
                            "text": "This is a sample text for the banner.",
                            "link": "#"
                        }
                    },
                    {
                        "type": "banner",
                        "settings": {
                            "title": "Simple Grid Banner Title 2",
                            "text": "This is a sample text for the banner.",
                            "link": "#"
                        }
                    },
                    {
                        "type": "banner",
                        "settings": {
                            "title": "Simple Grid Banner Title 3",
                            "text": "This is a sample text for the banner.",
                            "link": "#"
                        }
                    }
                ]
            }
        ]
    }
{% endschema %}