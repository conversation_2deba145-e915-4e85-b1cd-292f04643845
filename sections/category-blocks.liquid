{% liquid
    assign section_name = 'Category Blocks'
    assign el_id = section_name | handle | append: '-' | append: section.id
%}
<style>
    #{{ el_id }} .category-blocks {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        padding: 8px;
    }
    #{{ el_id }} .global-image-wrapper,
    #{{ el_id }} .global-image-wrapper img {
        height: 100%;
    }
    #{{ el_id }} .global-image-wrapper img {
        object-fit: cover;
    }
    #{{ el_id }} .global-image-wrapper .overlay {
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 70%, rgba(0, 0, 0, 0.5) 100%);
    }
    @media only screen and (min-width: 600px) {
        #{{ el_id }} .category-blocks {
            grid-template-columns: repeat(3, 1fr);
        } 
    }
    @media only screen and (min-width: 1024px) {
        #{{ el_id }} .category-blocks {
            grid-template-columns: repeat({{ section.blocks.size }}, 1fr);
        }
    }
</style>
<section id="{{ el_id }}">
    <div class="category-blocks">
        {% for block in section.blocks %}
            <div class="category-block relative">
                {% if block.settings.link %}
                <a href="{{ block.settings.link }}" class="block rounded-8 h-full{% if block.settings.image == blank %} bg-grey{% endif %}">
                {% else %}
                <div class="rounded-8 h-full{% if block.settings.image == blank %} bg-grey{% endif %}">
                {% endif %}
                    {% if block.settings.image %}
                        {% render 'global-image-wrapper',
                            image: block.settings.image,
                            image_alt: block.settings.title | escape,
                            overlay: true
                        %}
                    {% else %}
                        {{ 'image' | placeholder_svg_tag }}
                    {% endif %}
                    <div class="category-title absolute left-20 bottom-20 zi-1">
                        <h3 class="h8 c-white">{{ block.settings.title }}</h3>
                    </div>
                {% if block.settings.link %}
                </a>
                {% else %}
                </div>
                {% endif %}
            </div>
        {% endfor %}
    </div>
</section>
{% schema %}
    {
        "name": "Category Block",
        "blocks": [
            {
                "type": "category",
                "name": "Category",
                "settings": [
                    {
                        "type": "image_picker",
                        "id": "image",
                        "label": "Image"
                    },
                    {
                        "type": "text",
                        "id": "title",
                        "label": "Title",
                        "default": "Category Title"
                    },
                    {
                        "type": "url",
                        "id": "link",
                        "label": "Link"
                    }
                ]
            }
        ],
        "max_blocks": 6,
        "settings": [],
        "presets": [
            {
                "name": "Category Block",
                "category": "Custom",
                "blocks": [
                    {
                        "type": "category",
                        "settings": {
                            "title": "Category Title 1",
                            "link": "#"
                        }
                    },
                    {
                        "type": "category",
                        "settings": {
                            "title": "Category Title 2",
                            "link": "#"
                        }
                    },
                    {
                        "type": "category",
                        "settings": {
                            "title": "Category Title 3",
                            "link": "#"
                        }
                    },
                    {
                        "type": "category",
                        "settings": {
                            "title": "Category Title 4",
                            "link": "#"
                        }
                    },
                    {
                        "type": "category",
                        "settings": {
                            "title": "Category Title 5",
                            "link": "#"
                        }
                    },
                    {
                        "type": "category",
                        "settings": {
                            "title": "Category Title 6",
                            "link": "#"
                        }
                    }
                ],
                "settings": {}
            }
        ]
    }
{% endschema %}