<div id="header-con">
  <header id="site-header" class="relative bg-white site-header bb-1 b-border zi-6" :class="{'submenu-active': hasSubmenuActive}">
    {%- if settings.show_announcement -%}
      <div v-if="showHeaderBar" @mouseover="hideDropdown" class="announcement-bar" data-end="{{ settings.announcement_end_time | escape }}" style="background-color:{{ settings.announcement_bg }}">
        <div class="ann-bar-inner">
          {% capture ann_content %}
            <span style="color:{{ settings.announcement_color }}">{{ settings.announcement_text }}</span>
            {%- if settings.announcement_end_time != blank -%}
              <span v-if="annBarCountDown" class="hide-m show-d t-ml-10 t-mr-10" style="color:{{ settings.announcement_color }}">|</span>
            {%- endif -%}
            <span v-if="annBarCountDown" style="color:{{ settings.announcement_color }}" v-cloak>${annBarCountDown}</span>
          {% endcapture %}
          {%- assign ann_text_class = 'the-text bounding-text flex flex-column d-flex-row ai-center jc-center d-colgap-8 p3' -%}
          {%- if settings.announcement_link != blank -%}
            <a href="{{ settings.announcement_link }}" class="{{ ann_text_class }}">{{ ann_content }}</a>
          {%- else -%}
            <p class="{{ ann_text_class }}">{{ ann_content }}</p>
          {%- endif -%}
        </div>
      </div>
    {%- endif -%}

    <div class="relative header-con zi-1">
      <div class="relative flex header-con__inner ai-center jc-between">
        <div class="header-left">
          <button @click="toggleMobileMenu" type="button" class="mr-8 header-mobile-menu">
            <span class="flex icon icon-menu ai-center jc-center">{{ settings.icon_menu }}</span>
            <span class="icon icon-close-menu ai-center jc-center hide-m">{{ settings.icon_close }}</span>
          </button>

          <a @mouseover="hideDropdown" href="{{ routes.root_url }}" class="block pt-8 pb-8 header-logo d-pt-0 d-pb-0">
            {%- if section.settings.logo == blank -%}
              <p class="p1">{{ shop.name }}</p>
            {%- else -%}
              {%- render 'global-image-wrapper',
                image: section.settings.logo,
                image_alt: shop.name,
                additional_class: 'logo-main no-bg',
                preload: true
              -%}
            {%- endif -%}
          </a>
        </div>

        <nav class="header-nav w-max-cont hide-m show-d">
          <ul class="flex jc-center colgap-28">
            {%- for main_menu in linklists[section.settings.main_linklist].links -%}
              <li class="parent relative {% if main_menu.links != blank %}has-child{% endif %}">
                <a
                  @mouseover="showDropdown('{{ forloop.index0 }}')"
                  @mouseleave="handleLeaveMenu"
                  href="{{ main_menu.url }}"
                  title="{{ main_menu.title | escape }}"
                  class="relative flex header-link cursor jc-center ai-center"
                  :class="{active: activeSubmenu == '{{ forloop.index0 }}'}"
                  data-index="{{ forloop.index0 }}"
                  {% if main_menu.url == "#" %}rel="nofollow"{% endif %}
                >
                  <span class="text h8">{{ main_menu.title }}</span>
                  {%- if main_menu.links != blank -%}
                    <span class="flex ml-4 icon ai-center jc-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="8" viewBox="0 0 14 8" fill="none">
                        <path d="M1 1L7.00098 7L13 1" stroke="#1F1F1F" stroke-width="1.2" stroke-linecap="square"/>
                      </svg>
                    </span>
                  {%- endif -%}
                </a>
              </li>
            {%- endfor -%}
          </ul>
        </nav>

        <div class="header-right">
          <ul @mouseover="hideDropdown" class="flex d-colgap-12 ai-center">
            <li>
              <button
                @click="handleShowSearch"
                type="button"
                class="flex ai-center"
              >
                <span class="flex icon ai-center jc-center">{{ settings.icon_search }}</span>
              </button>
            </li>
            <li>
              <a
                href="{{ routes.account_url }}"
                title="{{ 'header.account' | t | escape }}"
                class="flex auth-btn ai-center jc-center"
              >
              {% comment %} {% capture initial_thumbnail_style %}
                {% liquid 
                    assign first_char = ""
                    if customer.first_name == blank
                      assign first_char = customer.name | slice: 0, 1 | upcase
                    else
                      assign first_char = customer.first_name | slice: 0, 1 | upcase
                    endif
                %}
                  <span class="flex w-full h-full acc-initial jc-center ai-center rounded-50c c-white l2">{{ first_char }}</span>
              {% endcapture %} {% endcomment %}
              {% capture account_icon_style %}
                <span class="flex icon ai-center jc-center">{{ settings.icon_account }}</span>
              {% endcapture %}
                {% if customer %}
                  {{ account_icon_style }}
                {% else %}
                  {{ account_icon_style }}
                {% endif %}
              </a>
            </li>
            <li class="relative flex cart-link">
              <a
                href="{{ routes.cart_url }}"
                class="relative flex header-cart-link header-icon-heights header-icons header-carts ai-center"
                @click.prevent="showHeaderCart"
              >
                <div class="flex cart-icon icon ai-center jc-center">{{ settings.icon_cart }}</div>
                <span class="header-cart-number bg-yellow {% if cart.item_count > 0 %}has-item{% endif %}">
                  <span class="cart-count c-black p5 bold-500 lh-0">{{ cart.item_count }}</span>
                </span>
              </a>
            </li>
          </ul>
        </div>
      </div>

      
    </div>

    <Teleport to="body">
      <div
        @click="clearSearch"
        class="page-overlay search-overlay zi-5"
        :class="{active: showSearch, 'fade-out': searchOverlayFadeOut}"
      >
        &nbsp;
      </div>
      <div class="bg-white header-search-form zi-6 rounded-bl-4 rounded-br-4" :class="{'active':showSearch, 'search-focus': searchFocus, 'rounded-bl-4 rounded-br-4  d-rounded-bl-8 d-rounded-br-8': searchResult}">
        <div class="w-full mx-auto">  
          <div class="sticky top-0 search-input-wrapper zi-1 bg-white bb-1 b-border">
            <form @submit="handleSearchSubmit" @mouseover="hideDropdown" action="{{ routes.search_url }}" method="get" role="search" class="relative flex">
              <input
                type="search"
                id="search"
                class="w-full pt-12 pb-12 pl-16 t-pl-20 d-pl-32 hd-pl-40 input p2 d-pt-14 d-pb-14"
                name="q"
                v-model="searchTerm"
                ref="search-input"
                placeholder="{{ section.settings.search_placeholder }}"
                role="combobox"
                aria-expanded="false"
                aria-owns="predictive-search-results"
                aria-controls="predictive-search-results"
                aria-haspopup="listbox"
                aria-autocomplete="list"
                autocomplete="off"
                @focus="handleSearchFocus"
                @keyup="goSearch"
                @input="$event.target.composing = false"
              >
              <input type="hidden" name="options[prefix]" value="last">
              <input type="hidden" name="type" value="product">
              <div class="action-buttons flex">
                <button @click="clearSearch" type="button" :class="{'hide-m': searchTerm.length < 1}" class=" p5 uppercase c-grey pt-12 pb-12 pl-16 pr-16 d-pt-14 d-pb-14 ai-center jc-center clear-search dont-break">{{ 'header.clear_all' | t }}</button>
                <button type="submit" class="flex pt-12 pb-12 pl-20 pr-12 d-pt-14 d-pb-14 ai-center icon-search jc-center">
                  {{ settings.icon_search }}
                </button>
                <button @click="clearSearch" type="button" class="flex pt-12 pb-12 pl-16 pr-16 d-pt-14 d-pb-14 t-pr-20 d-pr-32 hd-pr-40 clear-search ai-center jc-center">{{ settings.icon_close }}</button>
              </div>
            </form>
          </div>
          <div class="container search-dropdown-wrapper">
            <template v-if="searchResult" v-cloak>
              <div
                :class="{'hide-m': !searchResult}"
                id="predictive-search"
                tabindex="-1"
                v-html="searchResult"
                v-cloak
              />
            </template>
            <div class="row" v-else>
              <div class="inner-left col-12" :class="searchResult ? 'col-d-12 pl-0' : 'col-d-4'">
                <div class="m-2 grid-flex jc-between">
                  <div v-if="searchRecent && searchHistory.length > 0">
                    <div class="pb-32 mt-24 search-recent d-mt-40 d-pb-40">
                      <div class=" capitalize p4 c-grey mb-12 d-mb-16">{{ 'header.recent_searches' | t }}</div>
                      <ul>
                        <li v-for="(term, index) in searchHistory" :key="index" class="mb-12 lh-0 d-mb-12">
                          <a
                            :href="'{{ routes.search_url }}?type=product&q=' + term"
                            :title="term"
                            class="p2 c-black capitalize"
                            >${term}</a
                          >
                        </li>
                        <button @click="clearSearchHistory" class="underline uppercase c-grey p5 clear-all">
                          {{ 'header.clear_all' | t }}
                        </button>
                      </ul>
                    </div>
                  </div>
                  {%- if section.settings.search_suggestions != blank -%}
                    <div class="pb-32 mt-24 d-mt-40 d-pb-40">
                      <div class=" capitalize p4 c-grey mb-12 d-mb-16">{{ 'header.suggestions' | t }}</div>
                      <ul>
                        {%- assign search_suggestions = section.settings.search_suggestions
                          | newline_to_br
                          | strip_newlines
                          | split: '<br />'
                        -%}
                        {%- for sugg in search_suggestions -%}
                          <li class="lh-0 {% unless forloop.last %}mb-12 d-mb-12{% endunless %}">
                            <a
                              href="{{ routes.search_url }}?type=product&q={{ sugg }}"
                              title="{{ sugg | escape }}"
                              class="p2 c-black"
                            >
                              {{- sugg -}}
                            </a>
                          </li>
                        {%- endfor -%}
                      </ul>
                    </div>
                  {%- endif -%}
                </div>
              </div>
              {%- if settings.search_featured_products != blank -%}
                <div class="pt-32 pb-24 inner-right d-pt-40 d-pb-40 col-12 col-d-8">
                  <p class="uppercase p4 c-grey mb-12 d-mb-16">{{ 'header.results' | t }}</p>

                  <div class="search-featured-products d-mx-auto">
                    {%- for product in settings.search_featured_products -%}
                      {%- render 'product-card', product: product, preload_img: true-%}
                    {%- endfor -%}
                  </div>
                </div>
              {%- endif -%}
            </div>
          </div>
        </div>
      </div>
    </Teleport>

  </header>

  <Teleport to="body">
    <div class="page-overlay mobile-menu-overlay zi-6 " :class="{active: showMobileMenu}" @click="toggleMobileMenu">&nbsp;</div>
    <div
      class="relative bg-white mobile-menu-wrapper zi-8"
      :class="{active: showMobileMenu}"
    >
      <div class="mobile-menu-main">
        <div  class="flex menu-cont flex-column">
          <div class="main-links">
            <ul class="mobile-menu">
              {%- assign main_linklist = linklists[section.settings.main_linklist].links -%}
              {%- for main_menu in main_linklist -%}
                {%- assign has_child = false -%}
                {%- if main_menu.links != blank -%}
                  {%- assign has_child = true -%}
                {%- endif -%}
                <li class="parent lh-0 {% if has_child == true %}with-children{% endif %}">
                  <a
                    href="{{ main_menu.url }}"
                    @click.prevent='clickParentMenu($event, "{{ main_menu.title | escape }}")'
                    class="flex main-menu-title jc-between ai-center"
                    {% if has_child == true %}
                      data-title="{{ main_menu.title | escape }}"
                    {% endif %}
                    {% if main_menu.url == '#' %}rel="nofollow"{% endif %}
                  >
                    <div class="flex ai-center colgap-12">
                      {% assign block_dropdown = section.blocks[forloop.index0] %}
                      <div class="image-mobile-menu">
                        {% render 'global-image-wrapper',
                          image: block_dropdown.settings.image_mobile_menu,
                          additional_class: 'no-bg'
                        %}
                      </div>
                      <span class="flex flex-1 text h8">{{ main_menu.title }}</span>
                    </div>
                    {%- if has_child == true -%}
                      <span class="flex icon-caret ai-center jc-center">{{ settings.icon_caret_right }}</span>
                    {%- endif -%}
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          </div>

          {% if section.settings.loyalty_message != blank %}
            <div class="flex p-16 bg-background loyalty-message ai-center colgap-8">
              {% if section.settings.loyalty_image != blank %}
                <div class="loyalty-image">
                  {% render 'global-image-wrapper',
                    image: section.settings.loyalty_image,
                    additional_class: 'no-bg'
                  %}
                </div>
              {% endif %}
              <div class="flex-1 rte p2">{{ section.settings.loyalty_message }}</div>
            </div>
          {% endif %}

          <div class="bottom-links">
            <ul>
              <li>
                {%- assign auth_text = 'header.login_register' | t -%}
                {%- if customer -%}
                  {%- assign auth_text = 'header.account' | t -%}
                {%- endif -%}
                <a href="{{ routes.account_url }}" title="{{ auth_text | escape }}" class="flex p-16 bottom-menu-title jc-between ai-center bb-1 b-border">
                  <p class="p2 ">{{ auth_text }}</p>
                </a>
              </li>

              {%- assign bottom_links = linklists[section.settings.bottom_mobile_menu].links -%}
              {%- for bottom_menu in bottom_links -%}
                {%- assign has_child = false -%}
                {%- if bottom_menu.links != blank -%}
                  {%- assign has_child = true -%}
                {%- endif -%}
                <li class="parent lh-0 {% if has_child == true %}with-children{% endif %}">
                  <a
                    href="{{ bottom_menu.url }}"
                    @click.prevent='clickParentMenu($event, "{{ bottom_menu.title | escape }}")'
                    class="flex p-16 main-menu-title jc-between ai-center bb-1 b-border"
                    {% if has_child == true %}
                      data-title="{{ bottom_menu.title | escape }}"
                    {% endif %}
                    {% if bottom_menu.url == '#' %}rel="nofollow"{% endif %}
                  >
                    <span class="p2">{{ bottom_menu.title }}</span>
                    {%- if has_child == true -%}
                      <span class="flex icon-caret ai-center jc-center">{{ settings.icon_caret_right }}</span>
                    {%- endif -%}
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          </div>
        </div>

      </div>

      <div class="w-full bg-white mobile-submenus" :class="{active: showMobileSubmenu}">
        <div class="h-full mobile-submenu-wrapper">
          <div class="flex submenu-header ai-center jc-between bb-1 b-border">
            <button type="button" @click="showMobileSubmenu = false" class="flex p-16 ai-center colgap-8">
              <span class="flex icon-caret ai-center jc-center">{{ settings.icon_caret_left }}</span>
              <span class="h8">${activeMobileSubmenu}</span>
            </button>
          </div>

          {% comment %} Mobile Submenu {% endcomment %}
          {%- for main_menu in linklists[section.settings.main_linklist].links -%}
            {%- assign menu_index = forloop.index0 -%}
            {%- if main_menu.links != blank -%}
              {%- assign has_dropdown = false -%}

              {%- for block in section.blocks -%}
                {%- if block.settings.enable == true and forloop.index0 == menu_index and has_dropdown == false -%}
                  {%- assign has_dropdown = true -%}
                  <div class="h-full submenu" :class="{'hide-m': activeMobileSubmenu != '{{ main_menu.title }}'}">
                      {%- if block.type == 'simple_dropdown' -%}
                        <ul>
                          {%- for submenu_lvl2 in main_menu.links -%}
                            <li class="lh-0">
                              <a
                                href="{{ submenu_lvl2.url }}"
                                title="{{ submenu_lvl2.title | escape }}"
                                class="p2"
                              >
                                {{ submenu_lvl2.title }}
                              </a>
                            </li>
                          {%- endfor -%}
                        </ul>
                      {%- endif -%}
                  </div>  
                {%- endif -%}
              {%- endfor -%}
            {%- endif -%}
          {%- endfor -%}
          {% comment %} End Mobile Submenu {% endcomment %}

          {% comment %} Mobile bottom Submenu {% endcomment %}
          {%- for bottom_menu in linklists[section.settings.bottom_mobile_menu].links -%}
            {%- assign menu_index = forloop.index0 -%}
            {%- if bottom_menu.links != blank -%}
              <div class="h-full submenu" :class="{'hide-m': activeMobileSubmenu != '{{ bottom_menu.title }}'}">
                <ul>
                  {%- for submenu_lvl2 in bottom_menu.links -%}
                    <li class="lh-0">
                      <a
                        href="{{ submenu_lvl2.url }}"
                        title="{{ submenu_lvl2.title | escape }}"
                        class="p2"
                      >
                        {{ submenu_lvl2.title }}
                      </a>
                    </li>
                  {%- endfor -%}
                </ul>
              </div>  
            {%- endif -%}
          {%- endfor -%}
          {% comment %} End Mobile bottom Submenu {% endcomment %}
           
        </div>
      </div>
    </div>

    {% comment %} Simple dropdown {% endcomment %}
    {%- for main_menu in linklists[section.settings.main_linklist].links -%}
      {%- assign menu_index = forloop.index0 -%}
      {%- assign simple_dropdown = false -%}
      {%- if main_menu.links != blank -%}
        {%- for block in section.blocks -%}
          {%- if block.type == 'simple_dropdown' -%}
            {%- if block.settings.enable and menu_index == forloop.index0 -%}
              {%- assign block = block -%}
              {%- assign simple_dropdown = true -%}
              {% break %}
            {%- endif -%}
          {%- endif -%}
        {%- endfor -%}

        {%- if simple_dropdown == true -%}
          <div class="relative bg-white menu-dropdown simple-dropdown rounded-bl-8 rounded-br-8" :class="{show: activeSubmenu == {{ menu_index }}}" data-index="{{ menu_index }}">
            <div class="flex sd-links flex-column">
              {%- for submenu_lvl2 in main_menu.links -%}
                <a href="{{ submenu_lvl2.url }}" title="{{ submenu_lvl2.title | escape }}" class="p2 c-black">{{ submenu_lvl2.title }}</a>
              {%- endfor -%}
            </div>
          </div>
        {%- endif -%}
      {%- endif -%}
    {%- endfor -%}
    {% comment %} End Simple dropdown {% endcomment %}

    <div
      @mouseover="onHoverOverlay"
      class="page-overlay header-overlay zi-4"
      :class="{active: showOverlay, 'fade-out': overlayFadeOut}"
    >
      &nbsp;
    </div>
  </Teleport>
</div>

{% schema %}
{
  "name": "Header",
  "settings": [
    {
      "type": "header",
      "content": "General Settings"
    },
    {
      "type": "header",
      "content": "Logo"
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Logo"
    },
    {
      "type": "header",
      "content": "Menu"
    },
    {
      "type": "link_list",
      "id": "main_linklist",
      "label": "Main Menu",
      "default": "main-menu"
    },
    {
      "type": "link_list",
      "id": "bottom_mobile_menu",
      "label": "Bottom Mobile Menu"
    },
    {
      "type": "header",
      "content": "Loyalty Message"
    },
    {
      "type": "image_picker",
      "id": "loyalty_image",
      "label": "Loyalty Image"
    },
    {
      "type": "richtext",
      "id": "loyalty_message",
      "label": "Loyalty Message"
    },
    {
      "type": "header",
      "content": "Search"
    },
    {
      "type": "text",
      "id": "search_placeholder",
      "label": "Search placeholder",
      "default": "Search"
    }
  ],
  "blocks": [
    {
      "type": "simple_dropdown",
      "name": "Simple Dropdown",
      "settings": [
        {
          "type": "paragraph",
          "content": "Submenu links are from the main menu."
        },
        {
          "type": "checkbox",
          "id": "enable",
          "label": "Enable"
        },
        {
          "type": "image_picker",
          "id": "image_mobile_menu",
          "label": "Image for mobile menu"
        }
      ]
    }
  ]
}
{% endschema %}
