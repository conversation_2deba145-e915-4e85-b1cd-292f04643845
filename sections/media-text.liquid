{% liquid
    assign section_name = 'Media + Text'
    assign el_id = section_name | handle | append: '-' | append: section.id

    assign section_padding = section.settings.padding
    assign padding_class = 'sp-' | append: section_padding
    
    if section.settings.no_padding_top
        assign padding_class = padding_class | append: ' spt-no'
    else
        if section.settings.top_half_padding
            assign padding_class = padding_class | append: ' spt-' | append: section_padding | append: '-half'
        endif
    endif

    if section.settings.no_padding_bottom
        assign padding_class = padding_class | append: ' spb-no'
    else
        if section.settings.bottom_half_padding
            assign padding_class = padding_class | append: ' spb-' | append: section_padding | append: '-half'
        endif
    endif

    assign title_text = section.settings.title
    assign text_content = section.settings.text 
    assign image_content = section.settings.image
    assign meta = false
    assign link_url = section.settings.link_url
    if section.settings.use_as_product_vendor
        assign vendor_h = product.vendor | handleize
        assign vendor_coll = collections[vendor_h]
        if vendor_coll
            assign title_text = vendor_coll.metafields.about.title
            assign text_content = vendor_coll.metafields.about.description
            assign image_content = vendor_coll.metafields.about.image
            assign link_url = vendor_coll.url
            assign meta = true
        endif
    endif

    assign has_content = false
    if title_text != blank and text_content != blank and image_content != blank
        assign has_content = true
    endif
%}
{% if request.visual_preview_mode %}
    <img src="{{ "section-preview_media-text.png" | asset_url }}" width="100%" height="100%" />
{% else %}
    {% if has_content == true %}
        <style>
            #{{ el_id }} {
                background: {{ section.settings.background_color }};
                border-top-left-radius: 16px;
                border-top-right-radius: 16px;
            }
            #{{ el_id }}__mobile-image .media-wrapper {
                margin-bottom: -25px;
            }
            #{{ el_id }} .media-wrapper {
                width: calc(100% + 40px);
                margin-left: -20px;
            }
            #{{ el_id }} .media-sticker,
            #{{ el_id }}__mobile-image .media-sticker {
                width: 88px;
                {% if section.settings.align_y_m == 'top' %}
                top: {{ section.settings.position_y_m }}px;
                {% else %}
                bottom: {{ section.settings.position_y_m }}px;
                {% endif %}
                {% if section.settings.align_x_m == 'left' %}
                left: {{ section.settings.position_x_m }}px;
                {% else %}
                right: {{ section.settings.position_x_m }}px;
                {% endif %}
            }
            @media only screen and (min-width:600px) {
                #{{ el_id }}__mobile-image .media-wrapper {
                    margin-bottom: -30px;
                }
                #{{ el_id }} .media-wrapper {
                    width: calc(100% + 76px);
                    margin-left: -38px;
                }
            }
            @media only screen and (min-width:1024px) {
                {% unless section.settings.top_rounded_corners %}
                #{{ el_id }} {
                    border-top-left-radius: 0;
                    border-top-right-radius: 0;
                }
                #{{ el_id }}__mobile-image .media-wrapper {
                    margin-bottom: 0;
                }
                {% endunless %}
                #{{ el_id }} .media-wrapper {
                    width: 100%;
                    margin-left: 0;
                }
                #{{ el_id }} .media-sticker,
                #{{ el_id }}__mobile-image .media-sticker {
                    width: 128px;
                    {% if section.settings.align_y == 'top' %}
                    top: {{ section.settings.position_y }}px;
                    bottom: unset;
                    {% else %}
                    top: unset;
                    bottom: {{ section.settings.position_y }}px;
                    {% endif %}
                    {% if section.settings.align_x == 'left' %}
                    left: {{ section.settings.position_x }}px;
                    right: unset;
                    {% else %}
                    left: unset;
                    right: {{ section.settings.position_x }}px;
                    {% endif %}
                }
            }

            @media only screen and (min-width:1440px) {
                {% if section.settings.narrow_width %}
                #{{ el_id }} .cont-wrapper {
                    max-width: 1105px;
                    margin: 0 auto;
                }
                {% else %}
                #{{ el_id }} .cont-wrapper {
                    width: 100%;
                }
                {% endif %}
            }
            
        </style>
        {%- render 'rounded-corner', el_id: el_id, section: section -%}
        <div id="{{ el_id }}__mobile-image" class="relative hide-d">
            <div class="relative media-wrapper">
                {%- if section.settings.media_type == 'image' -%}
                    {%- assign add_class = '' -%}
                    {%- if section.settings.no_bg -%}
                        {%- assign add_class = add_class | append: 'no-bg' -%}
                    {%- endif -%}
                    {% render 'global-image-wrapper',
                        image: image_content, 
                        image_alt: title_text | escape,
                        meta: meta,
                        preload: section.settings.preload,
                        additional_class: add_class
                    %}
                {%- else -%}
                    <video class="w-full" loop autoplay muted>
                        <source src="{{ section.settings.video.sources[1].url }}" type="video/mp4">
                    </video>
                {%- endif -%}
            </div>
            {%- if section.settings.sticker != blank -%}
                <div class="absolute media-sticker zi-2">
                    {%- render 'global-image-wrapper',
                        image: section.settings.sticker,
                        image_alt: title_text | escape | append: '-sticer',
                        preload: true,
                        additional_class: 'no-bg'
                    -%}
                </div>
            {%- endif -%}
        </div>
        <section id="{{ el_id }}" class="{{ padding_class }} relative zi-1">
            <div class="container">
                <div class="cont-wrapper">
                    <div class="row flex flex-column{% if section.settings.media_position == 'right' %} d-row-reverse{% else %} d-flex-row{% endif %}">
                        <div class="relative col-12 col-d-6 hide-m show-d">
                            <div class="media-wrapper">
                                {%- if section.settings.media_type == 'image' -%}
                                    {%- assign add_class = 'rounded-12' -%}
                                    {%- if section.settings.no_bg -%}
                                        {%- assign add_class = add_class | append: ' no-bg' -%}
                                    {%- endif -%}
                                    {% render 'global-image-wrapper',
                                        image: image_content, 
                                        image_alt: title_text | escape,
                                        meta: meta,
                                        preload: section.settings.preload,
                                        additional_class: add_class
                                    %}
                                {%- else -%}
                                    <video class="w-full" loop autoplay muted>
                                        <source src="{{ section.settings.video.sources[1].url }}" type="video/mp4">
                                    </video>
                                {%- endif -%}
                            </div>
                            {%- if section.settings.sticker != blank -%}
                                <div class="absolute media-sticker">
                                    {%- render 'global-image-wrapper',
                                        image: section.settings.sticker,
                                        image_alt: title_text | escape | append: '-sticer',
                                        preload: true,
                                        additional_class: 'no-bg'
                                    -%}
                                </div>
                            {%- endif -%}
                        </div>
                        <div class="text-wrapper col-12 col-d-6 d-pt-0">
                            <div class="h-full flex flex-column rowgap-48 d-jc-between {% if section.settings.media_position == 'right' %}d-pr-40 hd-pr-80{% else %}d-pl-40 hd-pl-80{% endif %} c-{{ section.settings.text_color }}">
                                <div class="top-text">
                                    <h5 class="h5">{{ section.settings.small_title }}</h5>
                                </div>
                                <div class="bottom-text">
                                    <{{ section.settings.title_element }} class="{{ section.settings.title_class }} mb-12">
                                        {{ title_text }}
                                    </{{ section.settings.title_element }}>
                                    <p class="{{ section.settings.text_class }}">{{ text_content }}</p>
                                    {% if section.settings.link_label != blank and link_url != blank %}
                                        <a href="{{ link_url }}" class="btn{{ section.settings.link_class }} mt-24 d-mt-28">
                                            {{ section.settings.link_label }}
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    {% else %}
        {% comment %} If no content but as product vendor, then set the next outer bg color {% endcomment %}
        {% if section.settings.use_as_product_vendor %}
            {% assign outer_id = 'shopify-section-' | append: section.id %}
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const parentEl = document.getElementById('{{ outer_id }}');
                    if(parentEl) {
                        parentEl.nextElementSibling.style.backgroundColor = '{{ section.settings.outer_bg_color }}';
                    }
                });
            </script> 
        {% endif %}
    {% endif %}
{% endif %}
{% schema %}
    {
        "name": "Media + Text",
        "settings": [
            {
                "type": "header",
                "content": "General Settings"
            },
            {
                "type": "checkbox",
                "id": "use_as_product_vendor",
                "label": "Use as Product Vendor",
                "default": false,
                "info": "If checked, the content will be from product vendor collection."
            },
            {
                "type": "checkbox",
                "id": "top_rounded_corners",
                "label": "Top Rounded Corners",
                "default": false
            },
            {
                "type": "color",
                "id": "outer_bg_color",
                "label": "Outer Background Color",
                "info": "This color will be applied to the outer of rounded corners.",
                "visible_if": "{{ section.settings.top_rounded_corners }}"
            },
            {
                "type": "color",
                "id": "background_color",
                "label": "Background color",
                "default": "#1D4739"
            },
            {
                "type": "checkbox",
                "id": "narrow_width",
                "label": "Narrow Width",
                "default": false
            },
            {
                "type": "select",
                "id": "media_type",
                "label": "Media type",
                "options": [
                    {"value": "image", "label": "Image"},
                    {"value": "video", "label": "Video"}
                ],
                "default": "image"
            },
            {
                "type": "checkbox",
                "id": "no_bg",
                "label": "Image no background"
            },
            {
                "type": "checkbox",
                "id": "preload",
                "label": "Preload image"
            },
            {
                "type": "image_picker",
                "id": "image",
                "label": "Image"
            },
            {
                "type": "video",
                "id": "video",
                "label": "Video"
            },
            {
                "type": "select",
                "id": "media_position",
                "label": "Media position",
                "options": [
                    {"value": "left", "label": "Left"},
                    {"value": "right", "label": "Right"}
                ],
                "default": "right"
            },
            {
                "type": "select",
                "id": "text_color",
                "label": "Text Color",
                "options": [
                    {"value": "brown", "label": "Dark"},
                    {"value": "white", "label": "Light"}
                ],
                "default": "white"
            },
            {
                "type": "text",
                "id": "small_title",
                "label": "Small title"
            },
            {
                "type": "text",
                "id": "title",
                "label": "Title"
            },
            {
                "type": "select",
                "id": "title_element",
                "label": "Title Element",
                "options": [
                    {"value": "h1", "label": "H1"},
                    {"value": "h2", "label": "H2"},
                    {"value": "h3", "label": "H3"},
                    {"value": "h4", "label": "H4"}
                ],
                "default": "h2"
            },
            {
                "type": "select",
                "id": "title_class",
                "label": "Title class",
                "options": [
                    {"value": "h1", "label": "H1"},
                    {"value": "h2", "label": "H2"},
                    {"value": "h3", "label": "H3"},
                    {"value": "h4", "label": "H4"}
                ],
                "default": "h3"
            },
            {
                "type": "textarea",
                "id": "text",
                "label": "Text"
            },
            {
                "type": "select",
                "id": "text_class",
                "label": "Text class",
                "options": [
                    {"value": "p1", "label": "P1"},
                    {"value": "p2", "label": "P2"},
                    {"value": "p3", "label": "P3"},
                    {"value": "p4", "label": "P4"},
                    {"value": "p5", "label": "P5"},
                    {"value": "p6", "label": "P6"}
                ],
                "default": "p1"
            },
            {
                "type": "text",
                "id": "link_label",
                "label": "Link Label"
            },
            {
                "type": "url",
                "id": "link_url",
                "label": "Link URL"
            },
            {
                "type": "range",
                "id": "link_class",
                "label": "Link class (Button Style)",
                "min": 1,
                "max": 11,
                "step": 1,
                "default": 3
            },
            {
                "type": "header",
                "content": "Illustration Settings"
            },
            {
                "type": "image_picker",
                "id": "sticker",
                "label": "Sticker"
            },
            {
                "type": "select",
                "id": "align_x_m",
                "label": "Sticker align (Mobile)",
                "options": [
                    {"value": "left", "label": "Left"},
                    {"value": "right", "label": "Right"}
                ],
                "default": "right"
            },
            {
                "type": "select",
                "id": "align_y_m",
                "label": "Sticker align Y (Mobile)",
                "options": [
                    {"value": "top", "label": "Top"},
                    {"value": "bottom", "label": "Bottom"}
                ],
                "default": "top"
            },
            {
                "type": "select",
                "id": "align_x",
                "label": "Sticker align (Desktop)",
                "options": [
                    {"value": "left", "label": "Left"},
                    {"value": "right", "label": "Right"}
                ],
                "default": "left"
            },
            {
                "type": "select",
                "id": "align_y",
                "label": "Sticker align Y (Desktop)",
                "options": [
                    {"value": "top", "label": "Top"},
                    {"value": "bottom", "label": "Bottom"}
                ],
                "default": "top"
            },
            {
                "type": "number",
                "id": "position_x_m",
                "label": "Position X (Mobile)"
            },
            {
                "type": "number",
                "id": "position_y_m",
                "label": "Position Y (Mobile)"
            },
            {
                "type": "number",
                "id": "position_x",
                "label": "Position X (Desktop)"
            },
            {
                "type": "number",
                "id": "position_y",
                "label": "Position Y (Desktop)"
            },
            {
                "type": "header",
                "content": "Section Padding"
            },
            {
                "type": "select",
                "id": "padding",
                "label": "Padding",
                "options": [
                    {
                        "value": "no",
                        "label": "No"
                    },
                    {
                        "value": "xs",
                        "label": "Extra Small"
                    },
                    {
                        "value": "sm",
                        "label": "Small"
                    },
                    {
                        "value": "md",
                        "label": "Med"
                    },
                    {
                        "value": "lg",
                        "label": "Large"
                    }
                ],
                "default": "md"
            },
            {
                "type": "checkbox",
                "id": "no_padding_top",
                "label": "No padding top",
                "default": false
            },
            {
                "type": "checkbox",
                "id": "top_half_padding",
                "label": "Top half padding",
                "default": false
            },
            {
                "type": "checkbox",
                "id": "no_padding_bottom",
                "label": "No padding bottom",
                "default": false
            },
            {
                "type": "checkbox",
                "id": "bottom_half_padding",
                "label": "Bottom half padding",
                "default": false
            }
        ],
        "presets": [{ "name": "Media + Text" }]
    }
{% endschema %}